variables:
  GIT_STRATEGY: fetch
stages:
  # - check
  - build

# petkit-saas-mp-check:
#   stage: check
#   # cache:
#   #   untracked: true
#   #   paths:
#   #     - node_modules
#   script:
#     - mantas check-code-style -d $CI_COMMIT_REF_NAME
#   tags:
#     - mantas-check-supplier-web

petkit-saas-mp-build:
  stage: build
  cache:
    untracked: true
    paths:
      - node_modules
  before_script:
    - pnpm install
  script:
    - node -v
    - pnpm -v
    # 编译正式环境
    - pnpm build:user:petkit:online
    - echo "mantas mini-ci -u -p /tools/envs/md -un $GITLAB_USER_NAME -e online -t $CI_COMMIT_REF_NAME || true"
    - mantas mini-ci -u -p /tools/envs/md -un $GITLAB_USER_NAME -e online -t $CI_COMMIT_REF_NAME || true
    # 编译UAT环境
    # - pnpm build:user:petkit:uat
    # - echo "mantas mini-ci -u -p /tools/envs/md -un $GITLAB_USER_NAME -e uat -t $CI_COMMIT_REF_NAME || true"
    # - mantas mini-ci -u -p /tools/envs/md -un $GITLAB_USER_NAME -e uat -t $CI_COMMIT_REF_NAME || true
    # 编译测试环境
    - pnpm build:user:petkit:sandbox
    - echo "mantas mini-ci -u -p /tools/envs/wx -un $GITLAB_USER_NAME -e sandbox -t $CI_COMMIT_REF_NAME || true"
    - mantas mini-ci -u -p /tools/envs/wx -un $GITLAB_USER_NAME -e sandbox -t $CI_COMMIT_REF_NAME || true
    # 编译dev环境
    - pnpm build:user:petkit:dev
    - echo "mantas mini-ci -u -p /tools/envs/wx -un $GITLAB_USER_NAME -e dev -t $CI_COMMIT_REF_NAME || true"
    - mantas mini-ci -u -p /tools/envs/wx -un $GITLAB_USER_NAME -e dev -t $CI_COMMIT_REF_NAME || true
  only:
    - /^t.*$/
    - /^d.*$/
    - master
  tags:
    - petkit-saas-mp-build
