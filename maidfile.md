## upgrade

```bash
upgrade() {
  ver=$1

  if [ ! -f VERSION.md ]; then
    touch VERSION.md
  fi

  curr=$(cat VERSION.md)

  if [[ $curr != $ver ]]; then
    # pnpm install
    echo $ver > VERSION.md
  fi
}

upgrade 1.0.1
```

## start

Run task `upgrade` before this.

本地开发对应的启动命令
命令格式 `maid start [options]`

- `-p, project: [chain-staff, chain-user, mantas-user] default chain-user`
- `-e, env: [online, sandbox, dev, local, uat] default sandbox`
- `-t, target: [wx, my, md] default wx, md:医疗小程序`
- `-m, min: [true, false] default false`
- `-a, analysis: [true, false] default false`
- `-s, sourceMap: [true, false] default false`
- `-l, reduxLogger: [true, false] default false`
- `-S, size: max_old_space_size 最好1024的整数倍 单位MB default 4096`

e.g.

- `maid start`
- `maid start -m -s`
- `maid start -sml`
- `maid start -p chain-staff -e sandbox -t wx`
- `maid start -S 2048 -s`

```bash
command="npx cross-env NODE_ENV=development LINT=false npx webpack --watch --progress"

while getopts "p:e:t:S:msla" arg
  do
    case $arg in
      p)
      command="${command} --env project=${OPTARG}"
      ;;
      t)
      command="${command} --env target=${OPTARG}"
      ;;
      e)
      command="${command} --env envConfig=${OPTARG}"
      ;;
      m)
      command="${command} --env min=true"
      ;;
      s)
      command="${command} --env sourceMap=false"
      ;;
      l)
      command="${command} --env reduxLogger=false"
      ;;
      a)
      command="${command} --env analysis=false"
      ;;
      S)
      command="${command} --env spaceSize=${OPTARG}"
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

echo $command

$command
```

## build

Run task `upgrade` before this.

本地开发对应的启动命令
命令格式 `maid build [options]`

- `-p, project: [chain-staff, chain-user, mantas-user] default chain-user`
- `-e, env: [online, sandbox, dev, local, uat] default sandbox`
- `-t, target: [wx, my, md] default wx`
- `-s, sourceMap: [true, false] default false`
- `-l, reduxLogger: [true, false] default false`
- `-u, 是否上传到微信后台 [上传的版本]`
- `-S, size: max_old_space_size 最好1024的整数倍 单位MB default 4096`

e.g.

- `maid build`
- `maid build -sl`
- `maid build -p chain-staff -e sandbox -t wx`
- `maid build -u t3.xxxx.xx`
- `maid build -S 2048`

```bash
command="npx cross-env NODE_ENV=production LINT=false npx webpack --progress --env min=true"

ENV="sandbox";
PROJECT_NAME="chain-user";
uploadFlag=0;
curr_version="";
spaceSize=4096

while getopts "p:e:t:u:S:sl" arg
  do
    case $arg in
      p)
      command="${command} --env project=${OPTARG}"
      PROJECT_NAME=${OPTARG}
      ;;
      t)
      command="${command} --env target=${OPTARG}"
      echo OPTARG;
      if [ ${OPTARG} == 'md' ]
      then
          PROJECT_NAME="chain-medical";
      fi
      ;;
      e)
      command="${command} --env envConfig=${OPTARG}"
      ENV=${OPTARG}
      ;;
      s)
      command="${command} --env sourceMap=true"
      ;;
      u)
       uploadFlag=1;
       curr_version="${OPTARG}";
      ;;
      l)
      command="${command} --env reduxLogger=true"
      ;;
      S)
      command="${command} --env spaceSize=${OPTARG}"
      ;;
      m)
      command="${command} --env min=true"
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done
echo "项目${PROJECT_NAME}";
echo $command

# 微信开发者工具上传代码
wxUploadProjectTask(){
  echo "=============开始上传=============";
  _pwd=`pwd`;
# 打包后的项目路径
  MP_DIST_PATCH=`pwd`"/dist/${PROJECT_NAME}-${ENV}";

# 微信cli工具目录
  WX_CLI="/Applications/wechatwebdevtools.app/Contents/Resources/app.nw/bin";

  if [ -d ${WX_CLI} ];then
      echo '命令存在';
  else
      WX_CLI="/Applications/wechatwebdevtools.app/Contents/MacOS";
      echo '更换命令地址';
  fi

  shopt -s expand_aliases;
  curr_commit_content=`git log -1 --pretty=format:"%s" | sed 's/ //g'`; # 获取最近提交的git内容

# 获取到当前分支
  curr_branch_name=`git -C ${_pwd} rev-parse --abbrev-ref HEAD`;

  echo "当前上传的版本：${curr_version}, 分支: ${curr_branch_name}";

  cd ${WX_CLI};
  echo "当前上传路径：${MP_DIST_PATCH}";

# 执行上传命令 --upload-desc参数值不能有空格
  ./cli -u ${curr_version}@${MP_DIST_PATCH} --upload-desc "${curr_commit_content},开发分支:${curr_branch_name}";
  echo "=============上传成功============="
}
$command

if [[ ${uploadFlag} == 1 ]]; then
  wxUploadProjectTask;
fi
```

## test

Run task `upgrade` before this.

本地开发对应的测试命令
命令格式 `maid test [name]`

e.g.

- `maid test index`

```bash
if [ x"$1" = x ]; then
  command="jest index"
else
  command="jest $1"
fi

echo $command
$command
```
