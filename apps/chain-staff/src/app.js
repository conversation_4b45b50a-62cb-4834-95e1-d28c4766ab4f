import 'config/config';
import {
  store,
  getStorage,
} from '@petkit/redux';
import {
  forEach,
  isFunction,
} from 'lodash-es';

import {
  Provider,
} from 'utils/weapp-redux';
import {
  toastService,
} from 'services/toast.service';
import {
  pollingService,
} from 'services/polling.service';
import {
  loginService,
} from 'services/login.service';
import {
  envService,
} from 'services/env.service';
import {
  globalService,
} from 'services/global.service';
import './app.scss';

//接入阿拉丁SDK
import './config/ald-sdk/ald-stat';

let appConfig = {
  onLaunch() {
    forEach(this.globalData.services, v => isFunction(v.run) && v.run());

    // 根据来源，去首页加弹框提示
    // const isFromWechatWork = this.checkIsFromWechatWork();
    getStorage().setItem('isFromWechatWork', true);
  },

  onError() {
    console.log(arguments);
  },

  checkIsFromWechatWork() {
    const {
      environment
    } = wx.getSystemInfoSync();
    const isFromWechatWork = environment && environment === 'wxwork';
    return isFromWechatWork;
  },

  globalData: {
    wechatUserType: 'normal',
    loginType: '',
    featureManager: {},
    userInfo: null,
    currentCustomer: null,
    services: {
      envService,
      toastService,
      // webSocketService,
      pollingService,
      loginService,
      globalService,
      // webImService,
    },
  }
};

App(Provider(store)(appConfig));

