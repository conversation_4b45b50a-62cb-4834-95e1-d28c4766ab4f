@import 'config/theme.config';

@mixin mp-text-header-title() {
  font-size: u(36);
  line-height: u(36);
  color: #2f2f2f;
}
@mixin mp-text-header-sub() {
  font-size: u(28);
  line-height: u(28);
  color: #9397a2;
}

@mixin mp-app-attendance-theme($theme) {
  .mp-attendance {
    &__container {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border-bottom: u(2) solid #ebebeb;
    }

    &__header {
      height: u(128);
      flex-shrink: 0;
      background-color: white;
      display: flex;
      align-items: center;

      &-img {
        width: u(80);
        height: u(80);
        border-radius: 50%;
        margin-left: u(24);
      }

      &-text {
        margin-left: u(15);
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &-name {
        @include mp-text-header-title();
      }

      &-warehouse {
        @include mp-text-header-sub();
        margin-top: u(8);
      }

      &-date {
        margin-right: u(24);

        &-wrapper {
          flex-grow: 1;
          display: flex;
          justify-content: flex-end;
        }

        &-text {
          font-size: u(28);
          color: #575d6a;
        }
      }
    }

    &__body {
      flex-grow: 1;
      margin: u(24);
      border-radius: u(8);
      background-color: white;
    }
  }
}
