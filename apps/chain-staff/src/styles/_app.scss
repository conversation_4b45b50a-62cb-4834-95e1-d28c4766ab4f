@import './lib/icon';

@mixin wp-tag-theme($theme){
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .wp-tag__container {
    display: flex;
    flex-wrap: wrap;
  }

  .wp-tag {
    line-height: normal;
    display: inline-block;
    color: #fff;
    background-color: mat-color($primary);
    font-size: u(20);
    padding: u(4) u(8);
    border-radius: u(8);
    vertical-align: middle;
  }

  .br-4 {
    border-radius: 4px;
  }
}

@mixin wp-app-theme($theme) {
  @include wp-tag-theme($theme);
  @include wp-app-icon-theme($theme);
}
