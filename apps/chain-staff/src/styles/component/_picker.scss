@mixin mp-picker-theme($theme) {
  .mp-picker {
    &__title {
      display: flex;
      padding: 10px 15px;
      font-size: 14px;
      border-bottom: #f8f8f8 1px solid;
      justify-content: space-between;
    }
    &__cancel {
      color: #9397A2;
    }
    &__finish {
      color: #23B9DE;
    }
    &__input {
      border-bottom: 2rpx solid #E3E3E3;
      text-indent: 1em;
      color: #2F2F2F;
      font-size: 28rpx;
      height: 40rpx;
    }
    &__column {
      line-height: u(60);
      width: 100%;
      text-align: center;
    }
    &__indicator {
      height: u(70);
    }
    &__picker-view {
      width: 100%;
      height: 260px;
    }
    &__view-container {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      z-index: 3;
      background: #fff;
    }
    &__view-container.false {
      transform: translateY(100%);
      -webkit-transform: translateY(100%);
      transition: all .3s cubic-bezier(0, .54, .51, .99);
      -webikit-transition: all .3s cubic-bezier(0, .54, .51, .99);
      opacity: 1;
    }
    &__view-container.true {
      transform: translateY(0%);
      -webkit-transform: translateY(0%);
      transition: all .3s cubic-bezier(0, .54, .51, .99);
      -webikit-transition: all .3s cubic-bezier(0, .54, .51, .99);
      opacity: 1;
    }
    &__mask {
      width: 100%;
      height: 100%;
      position: fixed;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 2
    }
    &__mask.true {
      -webkit-transition: opacity 0.35s, visibility 0.35s;
      transition: opacity 0.35s, visibility 0.35s;
      visibility: visible;
      opacity: 1;
    }
    &__mask.false {
      visibility: hidden;
      opacity: 0;
      -webkit-transition: opacity 0.35s, visibility 0.35s;
      transition: opacity 0.35s, visibility 0.35s;
    }
  }
}
