@mixin mp-calendar-theme($theme) {
  .mp-calendar {
    &__wrapper {
      // height: u(570);
      padding: u(48) u(34);
    }

    &__header {
      &-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: u(24);
      }

      &-title {
        width: calc(100% / 7);
        color: #575d6a;
        font-size: u(24);
        text-align: center;
      }
    }

    &__body {
      &-container {}

      &-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: u(14);
        width: 100%;

        &:last-of-type {
          margin-bottom: 0;
        }
      }

      &-cell {
        width: calc(100% / 7);
        display: flex;
        flex-direction: column;
        text-align: center;
        // justify-content: center;
        align-items: center;

        &-text {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #575d6a;
          font-size: u(32);
          width: u(56);
          height: u(56);
          border-radius: 50%;
          margin-bottom: u(10);

          &.today {
            background: #d7f7ff;
            color: #23b9de;
          }

          &.selected {
            background: #23b9de;
            color: #fff;
          }
        }

        &-tag {
          // margin-top: u(16);
          border-radius: 50%;
          width: u(12);
          height: u(12);
          background: transparent;

          &.normal {
            background: #23b9de;
          }

          &.abnormal {
            background: #fb9e0f;
          }

          &.rest {
            background: #23b9de;
          }
        }
      }
    }
  }
}

