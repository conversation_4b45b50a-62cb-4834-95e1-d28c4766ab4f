@mixin mp-tab-theme($theme) {
  .mp-tab {
    $timing: .375s;

    &__wrapper {
      display: flex;
      flex-direction: column;

      font-size: u(24);
      background-color: #f6f6f6;
      position: relative;
      overflow: auto;

      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    &__top-46 {
      top: 46px;
    }

    &__container {
      flex-shrink: 0;

      display: flex;
      height: u(80);
      align-items: stretch;
      background-color: white;
      border-top:1px solid #E3E3E3;
    }

    &__item {
      flex-grow: 1;
      border-bottom: u(4) solid #ebebeb;
      transition: border-bottom $timing;
      display: flex;
      align-items: center;
      justify-content: center;

      &--active {
        border-bottom: u(4) solid #23b9de;

        .mp-tab__item-text {
          color: #23b9de;
        }
      }

      &-text {
        font-size: u(32);
        color: #b0b0b0;
        transition: color $timing;
      }
    }

    &__body {
      opacity: 0;
      transition: opacity $timing;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      overflow: auto;

      display: flex;
      flex-direction: column;
      align-items: stretch;

      &-wrapper {
        flex-grow: 1;
        position: relative;
      }

      &--active {
        opacity: 1;
      }
    }
  }
}

