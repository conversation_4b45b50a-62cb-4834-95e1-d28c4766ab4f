@import 'config/theme.config';

@mixin mp-table-item() {
  flex-shrink: 0;

  display: flex;
  justify-content: center;
  align-items: center;

  box-sizing: border-box;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

@mixin mp-table-wrapper() {
  overflow: hidden;
  position: absolute;

  box-sizing: border-box;
}

@mixin mp-table-theme($theme) {
  $row-width: 140;
  $row-height: 96;

  $col-width: 120;
  $col-height: 78;

  $border-width: u(198);

  $timing: .375s;

  .mp-table {
    &__container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    &__def {
      @include mp-table-item();

      position: absolute;

      top: 0;
      left: 0;

      width: u($col-width);
      height: u($row-height);

      background-color: #f6f8fa;

      overflow: hidden;

      &-divider {
        border-top: 1px solid #ebebeb;
        position: absolute;
        left: 0;
        top: 0;

        width: $border-width;
        z-index: 1;
        transform: rotate(38deg);
        transform-origin: 0 0;
      }

      &-row {
        position: absolute;
        left: u(8);
        bottom: u(4);
      }

      &-col {
        position: absolute;
        right: u(8);
        top: u(8);
      }
    }

    &__col {
      display: flex;
      flex-direction: row;

      will-change: transform;
      transition: transform $timing;

      &-wrapper {
        @include mp-table-wrapper();

        top: 0;
        left: u($col-width);

        right: 0;
        height: u($row-height);

        background-color: #f6f8fa;
        border-bottom: 1px solid #ebebeb;
      }

      &-item {
        @include mp-table-item();

        width: u($row-width);
        height: u($row-height);
      }
    }

    &__row {
      display: flex;
      flex-direction: column;

      will-change: transform;
      transition: transform $timing;

      &-wrapper {
        @include mp-table-wrapper();

        top: u($row-height);
        left: 0;

        width: u($col-width);
        bottom: 0;

        background-color: #fafbfc;
        border-right: 1px solid #ebebeb;
      }

      &-item {
        @include mp-table-item();
        align-items: stretch;
        flex-direction: column;
        justify-content: start;

        width: u($col-width);
        height: u($col-height);

        position: relative;

        &-triangle {
          position: absolute;

          width: u(26);
          height: u(26);
          right: 0;
          bottom: 0;
        }

        &-text {
          text-align: center;
          margin: 0 auto;
        }

        &-bar {
          position:absolute;
          background-color:#23B9DE;
          width:4px;
          height:37px;
          left:0;
          top:0;
        }
      }
    }

    &__data {
      display: flex;
      flex-direction: column;

      will-change: transform;
      transition: transform $timing;

      &-wrapper {
        @include mp-table-wrapper();

        top: u($row-height);
        left: u($col-width);

        right: 0;
        bottom: 0;

        background-color: #fff;
      }

      &-row {
        height: u($col-height);

        flex-shrink: 0;
        display: flex;
        flex-direction: row;
      }

      &-col {
        width: u($row-width);

        @include mp-table-item();
      }

      &-cell {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;
        width: u($row-width);
        height: u($col-height);
      }

      &-cell-type {
        height: u(32);
        width: u(32);
        border-radius: 50%;
        background-color: #fb9e0f;

        text-align: center;
        line-height: u(32);
        font-size: u(20);
        color: white;
        margin-right: u(4);
      }

      &-cell-timeline {
        $normal-color: #23b9de;
        $abnormal-color: #fb9e0f;

        &-container {
          width: u($row-width);
          height: u($col-height);

          display: flex;
          align-items: center;
          position: absolute;

          &--abnormal {
            .mp-table__data-cell-timeline {
              &-prev {
                border-right-color: $abnormal-color;
              }

              &-dot {
                background-color: $abnormal-color;
              }

              &-next {
                border-right-color: $abnormal-color;
              }

              &-text {
                color: $abnormal-color;
              }
            }
          }
        }

        &-line {
          flex-basis: 33%;
          flex-shrink: 0;
          align-self: stretch;

          position: relative;
          margin-right: u(10);
        }

        &-text {
          white-space: nowrap;
          color: $normal-color;

          &--eat-word {
            color: #666666;
          }
        }

        &-graphic {
          flex-basis: 34%;
          align-self: stretch;

          position: relative;
        }

        &-prev {
          position: absolute;

          top: 0;
          right: 0;
          height: u($col-height/2 + 1);

          border-right: u(4) solid $normal-color;

          &--eat-line {
            border-right: u(4) solid #666666;
          }
        }

        &-dot {
          position: absolute;

          top: 50%;
          right: u(-7);
          margin-top: u(-10);

          width: u(20);
          height: u(20);

          border-radius: 50%;
          background-color: $normal-color;

          &--eat-dot {
            background-color: #666666;
          }
        }

        &-next {
          position: absolute;

          bottom: 0;
          right: 0;
          height: u($col-height/2 + 1);

          border-right: u(4) solid $normal-color;

          &--eat-next {
            border-right: u(4) solid #666666;
          }
        }

        &-eat {
          color: #666666;
        }

        &-word {
          color: #666666;
        }

        &-changed {
          height: u(12);
          width: u(12);

          background-color: $abnormal-color;

          border-radius: 50%;

          position: absolute;
          right: u(8);
          top: u(8);
        }

        &-triangle {
          width: u(26);
          height: u(26);
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
  }
}
