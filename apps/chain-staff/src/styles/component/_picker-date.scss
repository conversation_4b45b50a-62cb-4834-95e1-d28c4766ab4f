@import 'config/theme.config';

@mixin mp-picker-date-theme($theme) {
  .mp-date-picker {
    &__container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    &__picker {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 100%;
      height: 100%;
    }

    &__date {
      width: u(6000);
      height: u(96);
      font-size: u(32);
      line-height: u(96);
      text-align: center;
    }

    &__icon {
      width: u(32);
      height: u(32);
    }

    &__prev {
      @extend .mp-date-picker__icon;

      margin-right: u(80);
    }

    &__next {
      @extend .mp-date-picker__icon;

      margin-left: u(80);
    }

    &__down {
      @extend .mp-date-picker__icon;

      position: absolute;
      top: u(32);
      right: u(32);
    }
  }
}


