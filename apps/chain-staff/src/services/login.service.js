import {
  store,
  acs,
} from '@petkit/redux';
import {
  envService,
} from 'services/env.service';

export const loginService = {
  login() {
    envService.qy(() => {
      wx.qy.login({
        timeout: 1000 * 60,
        success: (res) => {
          console.log('success', res);
          const {code, errMsg} = res;
          if (code) {
            
            store.dispatch(acs.user.loginWp.byQy({code}, {
              success() {
                wx.redirectTo({
                  url: '/pages/check/check',
                });
              }
            }));
          } else {
            console.log('登录失败！', errMsg);
          }
        },
        fail: err => {
          console.log('fail', err);
        },
        complete: obj => {
          console.log('complete', obj);
        }
      });
    });

    envService.wechat(() => {
      wx.login({
        success: ({
          code,
        }) => {
          store.dispatch(acs.user.loginWp.weapp({code}));
        }
      });
    });
  },
  run() {
    this.login();
  },
};

