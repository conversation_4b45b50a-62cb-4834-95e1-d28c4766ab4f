import {
  getStorage,
} from '@petkit/redux';
import {
  store,
  acsByAT,
} from '@petkit/redux';
import {
  isObject,
} from 'lodash-es';
import {
  getWebSocketHost,
} from 'config/config';
import {
  envService,
} from './env.service';

export const typeMapping = {
};

export const webSocketService = {
  data: {
    alive: true,
    task: null,
    interval: 45000,
  },
  destroy() {
    let data = {
      'F-Session': getStorage().getItem('F-Session'),
    };

    wx.sendSocketMessage({
      data: 'offline' + JSON.stringify(data),
    });
  },
  openSocket() {
    this.task = wx.connectSocket({
      url: getWebSocketHost() + '/websocket',
      // url: 'ws://*************:8887',
    });

    this.task.onOpen(() => {
      this.sendStartConn();
    });

    this.task.onMessage(({data: action}) => {
      try {
        action = JSON.parse(action);
        if (isObject(action)) {
          let {
            type,
            payload,
          } = action;

          if (type) {
            type = typeMapping[type] || type;

            store.dispatch(acsByAT[type](payload));
          }
        }
      } catch (e) {
        if (action === 'pong') {
          this.data.alive = true;
        }
      }
    });

    this.task.onClose(() => {
      setTimeout(() => {
        this.openSocket();
      }, 5000);
    });
  },
  sendStartConn() {
    let data = {
      'F-Session': getStorage().getItem('F-Session'),
      data: 'on open',
    };

    this.task.send({
      data: 'online' + JSON.stringify(data),
      success: () => {
        this.data.alive = true;
      }
    });
  },
  pingMsg() {
    setInterval(() => {
      if (this.data.alive) {
        this.task.send({
          data: 'ping',
        });
        this.data.alive = false;
      } else {
        this.task.close();
      }
    }, this.data.interval);
  },
  run() {
    envService.wechat(() => {
      this.openSocket();
      this.pingMsg();
    });
  },
};

