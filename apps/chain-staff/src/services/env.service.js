import {
  isFunction,
} from 'lodash-es';

export const envService = {
  data: {
    systemInfo: {},
  },

  qy(fn) {
    const {
      environment,
    } = this.data.systemInfo;
    if ('wxwork' === environment) {
      if (isFunction(fn)) {
        fn(this.data);
      } else {
        throw new Error('qy Fn is not a function');
      }
    }
  },

  wechat(fn) {
    const {
      environment,
    } = this.data.systemInfo;
    if (!environment) {
      if (isFunction(fn)) {
        fn(this.data);
      } else {
        throw new Error('wechat Fn is not a function');
      }
    }
  },
  run() {
    this.data.systemInfo = wx.getSystemInfoSync();
  },
};

