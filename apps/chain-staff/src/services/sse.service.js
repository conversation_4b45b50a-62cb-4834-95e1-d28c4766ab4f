import {
  Fetch,
} from '@petkit/redux';
import {
  store,
  acsByAT,
} from '@petkit/redux';
import {
  isObject,
} from 'lodash-es';

console.log(acsByAT);

export const typeMapping = {
};

export const sseService = {
  run() {
    let source = Fetch.sse({
      url: 'https://dev-food.petkit.cn/api/web/store/sse',
      // url: 'http://120.55.78.34:9979/api/web/store/sse',
    });

    source.onmessage = ({data: action}) => {
      action = JSON.parse(action);
      if (isObject(action)) {
        let {
          type,
          payload,
        } = action;

        if (type) {
          type = typeMapping[type] || type;

          store.dispatch(acsByAT[type](payload));
        }
      }
    };
  },
};

