import {
  store,
  acs,
} from '@petkit/redux';

export const toastService = {
  isShowToast: false,
  timerId: -1,
  interval: 500,
  msg({
    state,
    finished,
  }) {
    let toasts = state.global.toast;

    // 如果不存在toast数据，返回
    if (toasts.length === 0) {
      return;
    }
    // 如果正在显示中，返回
    if (this.isShowToast || state.global.http.loading.httpList.length > 0) {
      return;
    }

    let toast = toasts[0];
    let icon;
    if (toast.meta.type === 'success') {
      icon = 'success';
    } else {
      icon = 'none';
    }

    wx.showToast({
      title: toast.message,
      duration: toast.duration,
      icon,

      mask: true,
      success: () => {
        setTimeout(() => {
          this.isShowToast = false;
          if (typeof finished === 'function') {
            finished();
          }
        }, toast.duration);
      },
      fail: () => {
        this.isShowToast = false;
      }
    });

    this.isShowToast = true;
  },
  http({
    state,
  }) {
    if (this.isShowToast) {
      clearTimeout(this.timerId);
      return;
    }

    let httpList = state.global.http.loading.httpList;
    if (httpList.length === 0) {
      this.timerId = setTimeout(() => {
        wx.hideLoading();
        this.timerId = -1;
      }, this.interval);
    } else {
      if (this.timerId !== -1) {
        clearTimeout(this.timerId);
      }
      wx.showLoading({
        mask: true,
      });
    }
  },
  run() {
    store.subscribe(() => {
      let state = store.getState();

      this.msg({
        state,
        finished() {
          store.dispatch(acs.global.toast.hide());
        }
      });

      this.http({
        state,
      });
    });
  },
};

