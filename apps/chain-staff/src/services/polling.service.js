import {
  envService,
} from './env.service';

export const pollingService = {
  data: {
    _listeners: new Map(),
  },
  add(fn, {
    interval = 1500,
  } = {}) {
    envService.qy(() => {
      const that = this;
      this.data._listeners.set(fn, new function () {
        that.id = setInterval(fn, interval);
      });
    });
  },
  remove(fn) {
    envService.qy(() => {
      if (this.data._listeners.get(fn)) {
        clearInterval(this.data._listeners.get(fn).id);
        this.data._listeners.delete(fn);
      }
    });
  },
  run() {
  },
};

