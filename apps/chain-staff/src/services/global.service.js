export const globalService = {
  data: {
    systemInfo: {},
    scale: .5,
  },
  init() {
    const systemInfo = wx.getSystemInfoSync();

    this.data.systemInfo = systemInfo;
    this.data.scale = Number((systemInfo.screenWidth / 750).toFixed(2)) || .5;
  },
  get systemInfo() {
    return this.data.systemInfo;
  },
  get scale() {
    return this.data.scale;
  },
  run() {
    this.init();
  }
};

