// petkit
@import '~@petkit/style/theming';

$app-primary: mat-palette($mat-cyan);
$app-accent: mat-palette($mat-orange);
$app-warn: mat-palette($mat-yellow, 700, 300, 900);
$app-theme: mat-light-theme($app-primary, $app-accent, $app-warn);
$app-size: size(wp);


// import MUI colors
// @import '~muicss/lib/sass/mui/colors';
//
// $mui-primary-color:       mui-color('cyan', '500');
// $mui-primary-color-dark:  mui-color('cyan', '700');
// $mui-primary-color-light: mui-color('cyan', '100');
//
// $mui-accent-color:        mui-color('orange', 'A200');
// $mui-accent-color-dark:   mui-color('orange', 'A100');
// $mui-accent-color-light:  mui-color('orange', 'A400');

// import MUI SASS
// @import '~muicss/lib/sass/mui';
