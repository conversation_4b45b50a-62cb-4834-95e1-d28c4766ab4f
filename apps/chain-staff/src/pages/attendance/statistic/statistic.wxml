<view class="wp-page mp-attendance__container mp-attendance__statistic-container">
  <view class="mp-attendance__header">
    <image class="mp-attendance__header-img" src="https://tower.im/assets/default_avatars/jokul.jpg"></image>
    <view class="mp-attendance__header-text">
      <text class="mp-attendance__header-name">{{name}}</text>
      <text class="mp-attendance__header-warehouse">考勤门店: {{warehouseName}}</text>
    </view>
    <view class="mp-attendance__header-date-wrapper">
      <view class="mp-attendance__header-date">
        <picker mode="date" value="{{_month}}" start="{{_start}}" end="{{_end}}" fields="month" bindchange="bindMonthChange">
          <text class="mp-attendance__header-date-text">{{month}}</text>
          <icon class="icon-draw"></icon>
        </picker>
      </view>
    </view>
  </view>

  <block>
    <view class="mp-attendance__statistic-dashboard">
      <!-- 出勤面板 -->
      <view class="mp-attendance__statistic-dashboard__container">
        <view class="mp-attendance__statistic-dashboard__calendary" bind:tap="goToCalendar">
          <image class="mp-attendance__statistic-dashboard__calendary-image" src="/images/attendance/statistic/calendar.svg" /> 打卡月历
        </view>
        <view class="mp-attendance__statistic-dashboard__title">出勤天数 {{attendanceDay}}天</view>
        <view class="mp-attendance__statistic-dashboard__subtitle">该月需出勤总天数为{{totalAttendanceDay}}天</view>

        <view class="mp-attendance__statistic-dashboard__duty-info__container">
          <view class="mp-attendance__statistic-dashboard__duty-info__block" wx:for="{{dutyInfo}}" wx:key="info" wx:for-item="info">
            <view class="mp-attendance__statistic-dashboard__duty-info__block-number">{{info.value}}</view>
            <view class="mp-attendance__statistic-dashboard__duty-info__block-title">{{info.title}}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="mp-attendance__body mp-attendance__statistic-body">
      <!-- 统计 -->
      <view class="mp-attendance__statistic-body__container">
        <view class="mp-attendance__statistic-body__schedule-type__wrapper" wx:for="{{scheduleTypes}}" wx:for-item="st" wx:key="st">
          <view class="mp-attendance__statistic-body__schedule-type__container" data-title="{{st.title}}" bind:tap="onScheduleTypeTap">
            <view class="mp-attendance__statistic-body__schedule-type__title">
              {{st.title}}
            </view>
            <view class="mp-attendance__statistic-body__schedule-type__value {{st.status === scheduleTypeStatus.NORMAL ? 'mp-attendance__statistic-body__schedule-type__value--normal' : 0}} {{st.status === scheduleTypeStatus.ABNORMAL ? 'mp-attendance__statistic-body__schedule-type__value--abnormal' : 0}}">
              {{st.value}}
              <image class="mp-attendance__statistic-body__schedule-type__array {{st.isExpanded ? 'active' : ''}}" wx:if="{{st.records.length}}" src="/images/attendance/array.svg" />
              <image class="mp-attendance__statistic-body__schedule-type__array {{st.isExpanded ? 'active' : ''}}" wx:if="{{!st.records.length}}" src="/images/attendance/array_grey.svg" />
            </view>
          </view>
          <view class="mp-attendance__statistic-body__schedule-type__records" wx:if="{{st.isExpanded}}">
            <view class="mp-attendance__statistic-body__schedule-type__records-item__container" wx:for="{{st.records}}" wx:key="redcord" wx:for-item="record">
              <view class="mp-attendance__statistic-body__schedule-type__records-item__datetime">
                <view class="mp-attendance__statistic-body__schedule-type__records-item__date">{{record.date}}</view>
                <view class="mp-attendance__statistic-body__schedule-type__records-item__time">{{record.time}}</view>
              </view>
              <view class="mp-attendance__statistic-body__schedule-type__records-item__info" wx:if="{{record.info}}">{{record.info}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </block>

</view>
