import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  CONSTANT,
} from '@petkit/redux';
import moment from 'moment';
import {map, cloneDeep} from 'lodash-es';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    name: '...',
    warehouseName: '无',
    month: moment().format('YYYY.MM'),
    _month: moment().format('YYYY-MM'),


    _start: '2010-01',
    _end: moment().format('YYYY-MM'),

    dutyInfo: [],
    scheduleTypes: [],
    scheduleTypeStatus: CONSTANT.ATTENDANCE.SCHEDULE_TYPE_STATUS,

    calendarOption: {
      showTag: true,
    },

    needTagItems: [],

    hasChanged: true,
  },

  onShow() {

    this._requestSummary();

    this._onSub();
  },

  onReady() {
  },

  onHide() {
    this.data.sub();
  },

  onUnload() {
    this.data.sub();
  },

  bindMonthChange({
    detail: {
      value,
    }
  }) {
    this.setData({
      month: moment(value).format('YYYY.MM'),
      _month: moment(value).format('YYYY-MM'),
      hasChanged: true,
    });
    this._requestSummary();
  },

  onScheduleTypeTap(ev) {
    let message = '本月无';
    const title = ev.currentTarget.dataset.title;
    const scheduleTypes = this.data.scheduleTypes;
    map(scheduleTypes, scheduleType => {
      if (scheduleType.title === title) {
        if (scheduleType.records && scheduleType.records.length) {
          scheduleType.isExpanded = !scheduleType.isExpanded;
        } else {
          message += scheduleType.title;
          this.showToast({
            message: message,
          });
        }
      } else {
        scheduleType.isExpanded = false;
      }
    });

    this.setData({
      hasChanged: false,
      scheduleTypes: cloneDeep(scheduleTypes),
    });
  },

  goToCalendar() {
    const month = moment(this.data.month.replace('.', '-')).startOf('month').valueOf();
    const currentMonth = moment().startOf('month').valueOf();
    let isCurrentMonth = Number(month === currentMonth);
    wx.navigateTo({
      url: '/pages/attendance/statistic/month-calendar/month-calendar?month=' + this.data.month.replace('.', '-') + '&isCurrentMonth=' + isCurrentMonth,
    });
  },

  _onSub() {
    this.data.sub = store.subscribe(() => {

      if (
        this.data._scheduleTypes &&
        this.data._scheduleTypes.length &&
        this.data.hasChanged) {
        const scheduleTypes = [];
        map(this.data._scheduleTypes, _scheduleType => {
          scheduleTypes.push({
            ..._scheduleType,
            isExpanded: false,
            hasChanged: false,
          });
        });

        this.setData({
          scheduleTypes: scheduleTypes,
        });
      }

    });
  },

  _requestSummary() {
    const userId = this.data.userId;
    const start = moment(this.data.month.split('.').join('-')).startOf('month').valueOf();
    const end = moment(this.data.month.split('.').join('-')).endOf('month').valueOf();
    if (userId && start && end) {
      this.requestMonthlySummarizing({ userId, start, end });
    }
  },
};

const mapStateToData = state => {
  return {
    name: state.user.loginWp.userName,
    userId: state.user.loginWp.userId,
    warehouseName: state.attendance.clock.warehouseName,
    dutyInfo: state.attendance.statistic.dutyInfo,
    _scheduleTypes: state.attendance.statistic.scheduleTypes,
    attendanceDay: state.attendance.statistic.attendanceDay,
    totalAttendanceDay: state.attendance.statistic.totalAttendanceDay,
  }
};

const mapDispatchToPage = dispatch => bindActionCreators({
  showToast: acs.global.toast.show,
  requestMonthlySummarizing: acs.attendance.statistic.requestMonthlySummarizing,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
