@import 'styles/app/attendance';

$primary-color: #23b9de;
// $accent-color: #
$primary-content-color: #9397a2;

@mixin mp-attendance-statistic-theme($theme) {
  .mp-attendance__statistic {

    &-container {
      border-bottom: u(2) solid #ebebeb;
    }

    &-dashboard {
      flex-shrink: 1;
      height: u(252);
      margin: u(24) u(24) u(12);
      border-radius: u(8);
      background-color: white;

      &__container {
        position: relative;
        padding: u(24) u(24) u(16);
      }

      &__calendary {
        position: absolute;
        right: u(24);
        top: u(24);
        color: $primary-color;
        font-size: u(24);
        display: flex;
        // align-items: center;
        // justify-content: center;


        &-image {
          width: u(28);
          height: u(28);
          vertical-align: middle;
          margin-right: u(16);
          margin-top: u(2);
        }
      }

      &__title {
        color: #2f2f2f;
        font-size: u(36);
      }

      &__subtitle {
        color: $primary-content-color;
        font-size: u(24);
      }

      &__duty-info {
        &__container {
          display: flex;
          margin-top: u(48);
        }

        &__block {
          width: calc(100% / 3);
          text-align: center;
          position: relative;

          &::after {
            content: '';

          }
        }

        &__block-number {
          color: $primary-color;
          font-size: u(34);
          line-height: 1em;
          margin-bottom: u(16);
        }

        &__block-title {
          color: $primary-content-color;
          font-size: u(24);
          line-height: 1em;
        }
      }
    }

    &-body {
      margin-top: u(12);

      &__container {
      }

      &__schedule-type {
        &__wrapper {
          border-bottom: 1px solid #f1f1f1;

          &:last-of-type {
            border-bottom: none;
          }
        }

        &__container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: u(38) u(24);
        }

        &__title {
          color: #575d6a;
          font-size: u(32);
        }

        &__value {
          color: #d4d4d4;
          font-size: u(32);

          &--normal {
            color: $primary-content-color;
          }

          &--abnormal {
            color: #fa6262;
          }
        }

        &__array {
          width: u(24);
          height: u(12);

          &.active {
            transform: rotate(180deg);
          }
        }

        &__records {
          background: #f6fdff;
          padding: 0 u(24);
          border-top: 1px solid #f1f1f1;

          &-item {
            &__container {
              padding: u(34) 0;
              border-bottom: 1px solid #f1f1f1;

              &:last-of-type {
                border-bottom: none;
              }
            }

            &__datetime {
              color: $primary-content-color;
              font-size: u(32);
              line-height: 1em;
              display: flex;
            }

            &__date {}

            &__time {
              margin-left: u(16);
            }

            &__info {
              color: $primary-content-color;
              font-size: u(24);
              line-height: 1em;
              margin-top: u(16);
            }
          }
        }
      }
    }

  }
}

@include mp-app-attendance-theme($app-theme);
@include mp-attendance-statistic-theme($app-theme);

