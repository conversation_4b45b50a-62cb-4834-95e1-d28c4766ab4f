import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  CONSTANT,
} from '@petkit/redux';
import moment from 'moment';
import {map, cloneDeep} from 'lodash-es';
import  * as weUtils  from 'utils/we-uitils';

const dutyTime = {
  start: moment().format('YYYY-MM-DD') + ' 09:00',
  end: moment().format('YYYY-MM-DD') + ' 18:00',
};

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    name: '...',
    warehouseName: '无',
    month: null,
    day: '...',
    _day: 0,
    timeStampt: 0,
    isCurrentMonth: false,
    isToday: true,

    showTag: true,

    needTagItems: [],
    selectedDay: 0,

    clockTypes: CONSTANT.ATTENDANCE.CLOCK_TYPES,

    // clockInfo: {
    //   statistic: {
    //     clockNumber: 0,
    //     info: '0',
    //   },
    //   schedule: {
    //     hasSchedule: true,
    //     isRest: false,

    //     id: 0,
    //     storeId: 0,
    //     storeName: '中华店',
    //     storeAddress: '',

    //     ondutyTime: moment(dutyTime.start).format('HH:mm'),
    //     _ondutyTime: moment(dutyTime.start).valueOf(),

    //     offdutyTime: moment(dutyTime.end).format('HH:mm'),
    //     _offdutyTime: moment(dutyTime.end).valueOf(),
    //   },
    //   onduty: {
    //     // 时间
    //     clockTime: '08:50',
    //     _clockTime: 0,
    //     // clockTime: '',
    //     // _clockTime: 0,

    //     // 地点
    //     storeId: 1,
    //     locationName: '(中华店)',
    //     locationAddress: '上海',
    //     isScheduleLocation: true,

    //     // 事件
    //     hasManagerModify: true,
    //     manageModifyInfo: '店长调整为正常',

    //     // 异常状态
    //     clockStatus: CONSTANT.ATTENDANCE.CLOCK_TYPES.ABNORMAL,
    //     abnormalTypes: ['迟到1小时05分钟'],
    //   },
    //   offduty: {
    //     // 时间
    //     clockTime: '18:50',
    //     _clockTime: 0,
    //     // clockTime: '',
    //     // _clockTime: 0,

    //     // 地点
    //     storeId: 0,
    //     locationName: '',
    //     locationAddress: '上海',
    //     isScheduleLocation: false,

    //     // 事件
    //     hasManagerModify: false,
    //     manageModifyInfo: '',

    //     // 异常状态
    //     clockStatus: CONSTANT.ATTENDANCE.CLOCK_TYPES.NORMAL,
    //     abnormalTypes: [],
    //   },
    // },
  },

  onLoad(option) {
    const isCurrentMonth = Boolean(Number(option.isCurrentMonth));
    
    this._setDate(option.month, isCurrentMonth);
  },

  onShow() {

    this._requestMonthAbnormal();

    this._onSub();
  },

  onReady() {
  },

  onHide() {
    this.data.sub();
    this.resetClockInfo();
  },

  onUnload() {
    this.data.sub();
    this.resetClockInfo();
  },

  _onSub() {
    this.data.sub = store.subscribe(() => {
    });
  },

  onDateSelected(ev) {
    this._setDate(this.data.month, this.data.isCurrentMonth, ev.detail.day);

    this._requestDayClockInfo();
  },

  // 设置右上角时间
  _setDate(month, isCurrentMonth, day) {
    let isToday = false;
    const now = moment().startOf('day').valueOf();
    let _day = null;
    let timeStampt = 0;
    if (isCurrentMonth) {
      _day = moment(day || moment().valueOf()).format('YYYY.MM.DD');
      timeStampt = moment(day || moment().valueOf()).valueOf();
    } else {
      _day = moment(day || moment(month).startOf('month').valueOf()).startOf('day').format('YYYY.MM.DD');
      timeStampt = moment(day || moment(month).startOf('month').valueOf()).startOf('day').valueOf();
    }
    const weekDay = weUtils.getWeekDayName(timeStampt);

    isToday = (now === moment(timeStampt).startOf('day').valueOf());
    this.setData({
      isCurrentMonth: isCurrentMonth,
      month: month,
      day: _day + ' ' + weekDay,
      _day: moment(_day).valueOf(),
      timeStampt,
      selectedDay: moment(_day).valueOf(),
      isToday,
    });
  },

  _requestMonthAbnormal() {
    // const userId = 1343;
    const userId = this.data.userId;
    const start = moment(this.data.month.split('.').join('-')).startOf('month').valueOf();
    const end = moment(this.data.month.split('.').join('-')).endOf('month').valueOf();
    this.requestMonthlyAbnormalDay({userId, start, end});
  },

  _requestDayClockInfo() {
    // const userId = 1369;
    const userId = this.data.userId;
    const day = moment(this.data.timeStampt).valueOf();
    this.requestClockInfo({
      userId, day,
    });
  }

};

const mapStateToData = state => ({
  name: state.user.loginWp.userName,
  userId: state.user.loginWp.userId,
  needTagItems: state.attendance.statistic.abnormalDates,
  clockInfo: state.attendance.clock._clockInfo,
  warehouseName: state.attendance.clock.warehouseName,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  showToast: acs.global.toast.show,
  requestMonthlyAbnormalDay: acs.attendance.statistic.requestMonthlyAbnormalDay,
  requestClockInfo: acs.attendance.clock.requestClockInfo,
  resetClockInfo: acs.attendance.clock.resetClockInfo,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
