<view class="wp-page mp-attendance__container mp-attendance__month-calendar-container">
  <view class="mp-attendance__header">
    <image class="mp-attendance__header-img" src="https://tower.im/assets/default_avatars/jokul.jpg"></image>
    <view class="mp-attendance__header-text">
      <text class="mp-attendance__header-name">{{name}}</text>
      <text class="mp-attendance__header-warehouse">考勤门店: {{warehouseName}}</text>
    </view>
    <view class="mp-attendance__header-date-wrapper">
      <view class="mp-attendance__header-date">
        <text class="mp-attendance__header-date-text">{{day}}</text>
      </view>
    </view>
  </view>

  <block>
    <view class="mp-attendance__month-calendar-calendar">
      <!-- 日历 -->
      <calendar
        month="{{month}}"
        show-tag="{{showTag}}"
        need-tag-items="{{needTagItems}}"
        selected-day="{{selectedDay}}"
        bind:dateSelected="onDateSelected"></calendar>
    </view>

    <view class="mp-attendance__month-calendar-body">
      <!-- 班次信息 -->
      <view class="mp-attendance__month-calendar-plan-info">
        班次:
        <text wx:if="{{clockInfo.schedule.hasSchedule}}">{{'已排班'}} {{clockInfo.schedule.storeName}}</text>
        <text wx:if="{{!clockInfo.schedule.hasSchedule}}">{{'暂未排班'}}</text>
      </view>
      <!-- 当日打卡情况汇总 -->
      <view class="mp-attendance__month-calendar-duty-info" wx:if="{{(clockInfo.schedule.id && clockInfo.onduty._clockTime) || !isToday || clockInfo.schedule.isRest}}">
        <image class="mp-attendance__month-calendar-duty-info__image" src="/images/attendance/statistic/time.svg" />
        <text wx:if="{{clockInfo.schedule.hasSchedule}}">今日打卡{{clockInfo.statistic.clockNumber}}次，工时共计{{clockInfo.statistic.info}}</text>
        <text wx:if="{{!clockInfo.schedule.hasSchedule}}">今日无需打卡</text>
      </view>

      <view class="mp-attendance__month-calendar-clock-info" wx:if="{{(clockInfo.schedule.id && clockInfo.onduty._clockTime) || !isToday || clockInfo.schedule.isRest}}">
        <!-- 打卡记录 -->
        <view class="mp-attendance__month-calendar-clock">
          <view class="mp-attendance__month-calendar-clock__container">
            <view class="mp-attendance__month-calendar-clock__dot">上</view>
            <view class="mp-attendance__month-calendar-clock__line" wx:if="{{(clockInfo.schedule.id && clockInfo.offduty._clockTime) || !isToday || clockInfo.schedule.isRest}}"></view>
            <block wx:if="{{clockInfo.schedule.hasSchedule}}">
              <view class="mp-attendance__month-calendar-clock__time">
                <text wx:if="{{!clockInfo.schedule.isRest}}">打卡时间<text wx:if="{{clockInfo.onduty.isClocked}}">{{clockInfo.onduty.clockTime}}</text><text wx:if="{{!clockInfo.onduty.isClocked}}">无记录</text>(上班时间 {{clockInfo.schedule.ondutyTime}})</text>
                <text wx:if="{{clockInfo.schedule.isRest}}">今日休息</text>
              </view>
              <view wx:if="{{!clockInfo.schedule.isRest}}">
                <view wx:if="{{clockInfo.onduty.isClocked}}" class="mp-attendance__month-calendar-clock__real-location">
                  <image class="mp-attendance__month-calendar-clock__real-location-icon" src="/images/attendance/clock/location.svg" />
                  <text wx:if="{{clockInfo.onduty.isScheduleLocation}}" class="mp-attendance__month-calendar-clock__real-location-name">{{clockInfo.onduty.locationName}}</text>
                  {{clockInfo.onduty.locationAddress}}
                </view>
                <view>
                  <view class="mp-attendance__month-calendar-clock__status">
                    <text wx:if="{{clockInfo.onduty.clockStatus === clockTypes.ABNORMAL ||  clockInfo.onduty.clockStatus === clockTypes.VOCATION}}" class="tag tag-warn" wx:for="{{clockInfo.onduty.abnormalTypes}}" wx:key="at" wx:for-item="at">{{ at }}</text>
                    <text wx:if="{{clockInfo.onduty.clockStatus === clockTypes.NORMAL}}" class="tag tag-primary">正常</text>
                    <text wx:if="{{clockInfo.onduty.hasManagerModify}}" class="tag tag-primary">{{clockInfo.onduty.manageModifyInfo}}</text>
                  </view>
                </view>
              </view>
            </block>

            <block wx:if="{{!clockInfo.schedule.hasSchedule}}">
              <view class="mp-attendance__month-calendar-clock__time">
                <text>今日暂未排班</text>
              </view>
            </block>
          </view>
        </view>
      </view>

      <view class="mp-attendance__month-calendar-clock-info" wx:if="{{(clockInfo.schedule.id && clockInfo.offduty._clockTime) || !isToday || clockInfo.schedule.isRest}}">
        <!-- 打卡记录 -->
        <view class="mp-attendance__month-calendar-clock">
          <view class="mp-attendance__month-calendar-clock__container">
            <view class="mp-attendance__month-calendar-clock__dot">下</view>
            <block wx:if="{{clockInfo.schedule.hasSchedule}}">
              <view class="mp-attendance__month-calendar-clock__time">
                <text wx:if="{{!clockInfo.schedule.isRest}}">打卡时间<text wx:if="{{clockInfo.offduty.isClocked}}">{{clockInfo.offduty.clockTime}}</text><text wx:if="{{!clockInfo.offduty.isClocked}}">无记录</text>(下班时间 {{clockInfo.schedule.offdutyTime}})</text>
                <text wx:if="{{clockInfo.schedule.isRest}}">今日休息</text>
              </view>
              <view wx:if="{{!clockInfo.schedule.isRest}}">
                <view wx:if="{{clockInfo.offduty.isClocked}}" class="mp-attendance__month-calendar-clock__real-location">
                  <image class="mp-attendance__month-calendar-clock__real-location-icon" src="/images/attendance/clock/location.svg" />
                  <text wx:if="{{clockInfo.offduty.isScheduleLocation}}" class="mp-attendance__month-calendar-clock__real-location-name">{{clockInfo.offduty.locationName}}</text>
                  {{clockInfo.offduty.locationAddress}}
                </view>
                <view>
                  <view class="mp-attendance__month-calendar-clock__status">
                    <text wx:if="{{clockInfo.offduty.clockStatus === clockTypes.ABNORMAL ||  clockInfo.offduty.clockStatus === clockTypes.VOCATION}}" class="tag tag-warn" wx:for="{{clockInfo.offduty.abnormalTypes}}" wx:key="at" wx:for-item="at">{{ at }}</text>
                    <text wx:if="{{clockInfo.offduty.clockStatus === clockTypes.NORMAL}}" class="tag tag-primary">正常</text>
                    <text wx:if="{{clockInfo.offduty.hasManagerModify}}" class="tag tag-primary">{{clockInfo.offduty.manageModifyInfo}}</text>
                  </view>
                </view>
              </view>
            </block>

            <block wx:if="{{!clockInfo.schedule.hasSchedule}}">
              <view class="mp-attendance__month-calendar-clock__time">
                <text>今日暂未排班</text>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
  </block>

</view>
