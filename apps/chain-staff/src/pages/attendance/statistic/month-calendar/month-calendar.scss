@import 'styles/app/attendance';

$primary-color: #23b9de;
// $accent-color: #
$primary-content-color: #9397a2;

@mixin mp-attendance-month-calendar-theme($theme) {
  .mp-attendance__month-calendar {

    &-container {
    }

    &-calendar {
      flex-shrink: 1;
      // height: u(570);
      margin: u(24) u(24) u(12);
      border-radius: u(8);
      background-color: white;
    }

    &-body {
      flex-shrink: 1;
      margin: u(12) u(24) u(24);
      padding: u(24);
      border-radius: u(8);
      background: #fff;
    }

    &-plan-info {
      color: #9397a2;
      font-size: u(24);
      margin-bottom: u(26);
    }

    &-duty-info {
      color: #2f2f2f;
      font-size: u(28);
      display: flex;
      align-items: center;
      margin-bottom: u(50);

      &__image {
        width: u(32);
        height: u(32);
        margin-right: u(16);
      }
    }

    &-clock {
      &-info {}

      $dot-width: u(32);
      $margin-top: u(20);

      &__container {
        min-height: u(260);
        color: #575d6a;
        padding-left: u(40);
        position: relative;
      }

      &__dot {
        width: $dot-width;
        height: $dot-width;
        border-radius: 50%;
        background: #b0b0b0;
        position: absolute;
        z-index: 1;
        left: 0;
        top: u(6);
        color: #fff;
        font-size: u(20);
        text-align: center;
        line-height: $dot-width;
      }

      &__line {
        width: 2px;
        position: absolute;
        left: u(14);
        top: u(24);
        min-height: u(260);
        height: calc(100% + u(20));
        background: #ebebeb;
      }

      &__time {
        display: flex;
        font-size: u(28);
        margin-bottom: u(24);
        color: #2f2f2f;
        margin-left: u(6);
      }

      &__status {
        // margin-left: u(16);
        span {
          margin-right: u(16);
        }
      }

      &__real-time {
        font-size: u(36);
        line-height: 1em;
        margin-bottom: u(24);
      }

      &__real-location {
        color: #9397a2;
        font-size: u(24);
        display: flex;
        align-items: center;
        margin-bottom: u(24);
        line-height: 1em;

        &-icon {
          width: u(20);
          height: u(26);
          margin-right: u(16);
        }

        &-name {
          margin-right: u(6);
        }
      }

      &__button {
        width: u(250);
        height: u(250);
        background-image: linear-gradient(-180deg, #37ddf3 13%, #2cb0d4 90%);
        box-shadow: 0 7px 18px 0 #b8e2ec;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-bottom: u(48);

        &-group {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          margin-top: u(24);
        }

        &-text {
          color: #fff;
          font-size: u(36);
        }

        &-time {
          color: #a0ecff;
          font-size: u(32);
        }
      }
    }
  }
}

@include mp-app-attendance-theme($app-theme);
@include mp-attendance-month-calendar-theme($app-theme);

