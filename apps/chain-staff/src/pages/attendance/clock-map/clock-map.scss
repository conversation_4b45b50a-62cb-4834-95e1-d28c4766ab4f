@import 'config/theme.config';

@mixin mp-attendance-clock-map-theme {
  .mp-attendance__clock-map {
    $bottom-height: u(442);
    $top-height: calc(100% - #{$bottom-height});

    &__container {
      width: 100%;
      height: 100%;
      overflow: hidden;
      box-sizing: border-box;
      border-bottom: u(2) solid #ebebeb;
    }

    &__top {
      height: $top-height;
    }

    &__map {
      position: relative;
      width: 100%;
      height: 100%;

      &-cover-view {
        position: absolute;
        left: u(20);
        bottom: u(60);
        width: u(200);
        line-height: 2em;
        font-size: u(28);
        border-radius: 16px;
        background: #fff;
        border: 1px solid #f1f1f1;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;

        &__image {
          width: u(32);
          height: u(32);
          margin-right: u(16);
        }
      }
    }

    &__bottom {
      height: u(442);
      padding: u(24);
      box-sizing: border-box;
      overflow: auto;
      background: #fff;
    }

    &__mine-info {
      display: flex;
      color: #2f2f2f;
      font-size: u(32);
      margin-bottom: u(24);

      &__container {
        display: flex;
        align-self: flex-end;
      }

      &--right-location {
        color: #9397a2;
        font-size: u(24);
        align-self: flex-end;
        margin-left: u(10);

        span {
          color: #0cb9e0;
        }
      }

      &--wrong-location {
        color: #fb9e0f;
        font-size: u(24);
        vertical-align: bottom;
        align-self: flex-end;
        margin-left: u(10);
      }
    }

    &__location {
      height: u(72);
      color: #9397a2;
      font-size: u(28);
      padding-bottom: u(20);
      border-bottom: 1px solid #f1f1f1;
      margin-bottom: u(30);
      display: flex;

      &-status {
        margin-right: u(16);
      }
    }

    &__remark {
      display: flex;
      margin-bottom: u(28);
      color: #2f2f2f;
      font-size: u(32);
      padding-bottom: u(30);
      border-bottom: 1px solid #f1f1f1;
      align-items: center;

      &-label {
        margin-right: u(32);
      }

      &-input {
        width: calc(100% - 200rpx);
      }
    }

    &__button {
      padding: u(5) 0;
    }

    &__success {
      &__page {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, .5);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &__container {
        width: u(580);
        height: u(708);
        background: #fff;
        border-radius: u(10);
        padding: u(48);
        display: flex;
        align-items: center;
        flex-direction: column;
      }

      &__picture {
        width: u(320);
        height: u(150);
        margin-top: u(32);
      }

      &__clock {
        &-time {
          font-size: u(84);
          color: #23b9de;
          line-height: u(84);
          margin-top: u(72);
          font-weight: 500;
        }

        &-type {
          font-size: u(36);
          color: #23b9de;
          line-height: u(36);
          margin-top: u(20);
        }

        &-tip {
          font-size: u(32);
          color: #9397a2;
          line-height: u(32);
          margin-top: u(48);
        }
      }

      &__button {
        margin-top: u(72);
      }
    }
  }
}

@include mp-attendance-clock-map-theme();
