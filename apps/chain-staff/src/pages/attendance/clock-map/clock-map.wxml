<view class="wp-page mp-attendance__clock-map__container">
  <view class="mp-attendance__clock-map__top">
    <!-- map -->
    <map
      class="mp-attendance__clock-map__map"
      longitude="{{duty.longitude}}"
      latitude="{{duty.latitude}}"
      scale="19"
      markers="{{markers}}"
      circles="{{circles}}"
      bindmarkertap="onMarkerTap"
    >
      <!-- show-location -->
      <cover-view class="mp-attendance__clock-map__map-cover-view" bind:tap="reLocation">
        <cover-image class="mp-attendance__clock-map__map-cover-view__image" src="../../../images/attendance/clock-map/relocation.svg"></cover-image>
        重新定位
      </cover-view>
    </map>
  </view>
  <view class="mp-attendance__clock-map__bottom">
    <view class="mp-attendance__clock-map__mine-info">
      我的位置
      <view class="mp-attendance__clock-map__mine-info__container" wx:if="{{clockInfo.schedule.id}}">
        <view class="mp-attendance__clock-map__mine-info--right-location" wx:if="{{duty.isScheduleLocation}}">（在<text>考勤范围</text>内）</view>
        <view class="mp-attendance__clock-map__mine-info--wrong-location" wx:if="{{!duty.isScheduleLocation}}">（距离门店{{duty.restRange}}m, 未进入考勤区域）</view>
        <!-- <view class="mp-attendance__clock-map__mine-info--wrong-location" wx:if="{{!duty.storeId && !duty.isScheduleLocation && duty.isStoreLocation}}">（在该店暂未排班）</view> -->
      </view>
      <!-- <view class="mp-attendance__clock-map__mine-info__container" wx:if="{{!clockInfo.schedule.id}}">
        <view class="mp-attendance__clock-map__mine-info--wrong-location" wx:if="{{!duty.isScheduleLocation}}">（今日暂未排班）</view>
        <view class="mp-attendance__clock-map__mine-info--wrong-location" wx:if="{{duty.isScheduleLocation}}">（在该店暂未排班）</view>
      </view> -->
    </view>
    <view class="mp-attendance__clock-map__location">
      <view wx:if="{{duty.clockStatus !== clockTypes.NORMAL}}">
        <text class="mp-attendance__clock-map__location-status tag tag-warn" wx:for="{{duty.abnormalTypes}}" wx:for-item="type" wx:key="type">{{type}}</text>
      </view>
      <view wx:if="{{duty.clockStatus === clockTypes.NORMAL}}">
        <text class="mp-attendance__clock-map__location-status tag tag-primary">正常</text>
      </view>
      <text wx:if="{{duty.storeId}}">({{duty.locationName}})</text>{{duty.locationAddress}}
    </view>
    <view class="mp-attendance__clock-map__remark">
      <label class="mp-attendance__clock-map__remark-label">备注 </label>
      <input class="mp-attendance__clock-map__remark-input" placeholder="选填" placeholder-class="phcolor" value="{{remark}}" bindinput="onRemarkInput" />
        <!-- hintText="上传视频" -->
      <component-upload
        smallImg="{{smallImg}}"
        editable="{{imgEditable}}"
        type="imgFromCamera"
        urls="{{cameraImg}}"
        bind:uploaded="onPhotoUploaded"
      ></component-upload>
    </view>
    <button class="mp-attendance__clock-map__button button {{duty.clockStatus === clockTypes.NORMAL ? 'button-primary' : 'button-warn'}}" bind:tap="onClock">{{clockTime}} {{ duty.dutyText || '更新打卡' }}</button>
  </view>
</view>

<cover-view class="mp-attendance__clock-map__success__page" wx:if="{{successContent.show}}">
  <cover-view class="mp-attendance__clock-map__success__container">
    <cover-image class="mp-attendance__clock-map__success__picture" src="/images/attendance/clock/clock-success.png" />
    <cover-view class="mp-attendance__clock-map__success__clock-time">{{successContent.time}}</cover-view>
    <cover-view class="mp-attendance__clock-map__success__clock-type">{{successContent.title}}</cover-view>
    <cover-view class="mp-attendance__clock-map__success__clock-tip">{{successContent.content}}</cover-view>

    <button class="mp-attendance__clock-map__success__button button button-primary" bind:tap="onIkown">我知道了</button>
  </cover-view>
</cover-view>
