import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  CONSTANT,
} from '@petkit/redux';
import moment from 'moment';
import * as amap from 'utils/amap-wx';
import {
  getDistanceByLatAndLon
} from 'utils/getDistanceByLatAndLon';
import {
  setTimer,
} from 'utils/setTimer';

// images
import petkitLogo from '../../../images/attendance/clock-map/logo-open.png';
import cameraImg from '../../../images/attendance/clock-map/upload-camera-img.svg';
import myLocation from '../../../images/attendance/clock-map/me.png';
import {cloneDeep, map} from 'lodash-es';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    sub: null,

    currentTime: '',

    currentLocation: {
      latitude: 0,
      longitude: 0,
      locationAddress: '...',
    },
    isLocating: false,

    latitude: 0,
    longitude: 0,
    mapConfig: null,
    markers: [],
    circles: [],
    stores: [],

    dutyTypes: CONSTANT.ATTENDANCE.DUTY_TYPE,
    clockTypes: CONSTANT.ATTENDANCE.CLOCK_TYPES,

    clockTime: moment().format('HH:mm:ss'),

    relocationTimer: null,

    smallImg: true,
    imgEditable: false,
    cameraImg: cameraImg,
    remark: '',

    successContent: {
      show: false,
      time: 0,
      title: '',
      content: '',
    },
  },

  onLoad() {
    this._initAmap();
  },

  onShow() {
    this._calculateCurrentTime();
    this.requestStoreList();

    this._onSub();
  },

  onHide() {
    const duty = cloneDeep(this.data.duty);
    duty.dutyType = this.data.dutyTypes.NONE_DUTY;
    if (this.data.sub) {
      this.data.sub();
    }
    this.setData({
      duty: duty,
    });
  },

  onUnload() {
    const duty = cloneDeep(this.data.duty);
    duty.dutyType = this.data.dutyTypes.NONE_DUTY;
    if (this.data.sub) {
      this.data.sub();
    }
    this.setData({
      duty: duty,
    });
  },

  _onSub() {
    this.data.sub = store.subscribe(() => {
      // console.log(this.data.duty);
      // this._setLongitudeAndLatitude();

      if (!this.data.isLocating) {
        this._getEmployeeLocation();
      }

      this._setMapOptions();
    });
  },

  onRemarkInput(ev) {
    const duty = cloneDeep(this.data.duty);
    duty.remark = ev.detail.value;
    this.setData({
      // duty: cloneDeep(duty),
      remark: ev.detail.value,
    });
  },

  onPhotoUploaded({
    detail
  }) {
    console.log(detail);
    const duty = this.data.duty;
    duty.punchPhoto = detail[0].url;
    console.log(cloneDeep(duty));
    this.setData({
      duty: cloneDeep(duty),
    });
  },

  onMarkerTap(ev) {
    console.log(ev);
  },

  reLocation() {
    this._getEmployeeLocation();
  },

  onClock(ev) {
    // const type = ev.currentTarget.dataset.type;
    const dutyTypes = this.data.dutyTypes;
    const duty = this.data.duty;
    const type = duty.dutyType;
    const clockInfo = this.data.clockInfo;
    const param = {
      punchTime: moment().valueOf(),
      userId: clockInfo.employee.id,
      scheduleId: clockInfo.schedule.id,
      punchLatitude: duty.latitude,
      punchLongitude: duty.longitude,
      punchAddress: duty.locationAddress,
      punchRemark: this.data.remark,
      // punchRemark: duty.remark,
      punchType: 'CHECKIN',
      punchPhoto: duty.punchPhoto,
    };

    if (type) {
      if (type === dutyTypes.ON_DUTY) {
        param.punchType = 'CHECKIN';
      } else {
        param.punchType = 'CHECKOUT';
      }
    } else {
      if (duty.dutyType === dutyTypes.ON_DUTY) {
        param.punchType = 'CHECKIN';
      } else {
        param.punchType = 'CHECKOUT';
      }
    }

    this.requestPunch(param, {
      success: () => {
        this._setSuccessContent();

        this.requestClockInfo({
          userId: this.data.userId,
          day: moment(this.data.clockInfo.date, 'Asia/Shanghai').valueOf(),
        }, {
          success: () => {
            this._getEmployeeLocation();
          }
        });
        // this.showToast({
        //   message: '打卡成功！',
        // });
        // this.requestClockInfo({
        //   userId: this.data.userId,
        //   day: moment(this.data.date).valueOf(),
        // });
        // wx.navigateBack();
      },
      failure: () => {
        wx.navigateBack();
      },
    });
  },

  onIkown() {
    this._resetSuccessContent();
    wx.navigateBack();
  },

  _setSuccessContent() {
    const successContent = this.data.successContent;
    const duty = this.data.duty;
    const dutyTypes = this.data.dutyTypes;
    const clockTime = this.data.clockTime;

    successContent.show = true;
    successContent.time = clockTime.split(':')[0] + ':' + clockTime.split(':')[1];
    if (duty.dutyType === dutyTypes.ON_DUTY) {
      successContent.title = '上班打卡成功';
      successContent.content = '迎着朝阳奋斗吧，少年！';
    } else if (duty.dutyType === dutyTypes.OFF_DUTY || duty.dutyType === dutyTypes.NONE_DUTY) {
      successContent.title = '下班打卡成功';
      successContent.content = '小佩和你一起成长！';
    } else {
      successContent.title = '未知打卡成功';
    }

    this.setData({
      successContent: successContent,
    });
  },

  _resetSuccessContent() {
    this.setData({
      successContent: {
        show: false,
        time: 0,
        title: '',
        content: '',
      }
    });
  },

  _initAmap() {
    const _amap = new amap.AMapWX({key: 'bb5f8ee7dd62ebed24dc2ef2674866b9'});
    this.setData({
      amap: _amap,
    });
  },

  _setLongitudeAndLatitude(longitude, latitude) {
    const duty = this.data.duty;
    this.setData({
      longitude,
      latitude,
    });
    // let longitude = this.data.longitude;
    // let latitude = this.data.latitude;
    // if (longitude !== duty.longitude) {
    //   longitude = duty.longitude;
    //   this.setData({
    //     longitude,
    //   });
    // }
    // if (latitude !== duty.latitude) {
    //   latitude = duty.latitude;
    //   this.setData({
    //     latitude,
    //   });
    // }
  },

  // 实时计算打卡处显示的时间
  _calculateCurrentTime() {
    setTimer(() => {
      const clockTime = moment().format('HH:mm:ss');
      this.setData({
        clockTime: clockTime,
      });
    }, 1000);
  },

  _getEmployeeLocation() {
    try {
      this.setData({
        isLocating: true,
      });
      // 该方法为 如果在调用该方法之前，且第一次使用该方法时，则会弹出授权请求提示框
      // 如果选择拒绝，则会走fail回调
      // 如果选择同意，则会走success回调
      // 如果以前就拒绝授权地理位置时，则不会弹出授权请求提示框，并走fail回调
      // 如果以前就同意授权地理位置时，则直接走success回调
      // 此处fail的话，需要提示用户去手动授权地理位置
      wx.authorize({
        scope: 'scope.userLocation',
        success: (res) => {
          // console.log('authorize success: ', res);
          this._getLocation();
        },
        fail: (err) => {
          // console.log('authorize fail: ', err);
          wx.openSetting({
            success: (res) => {
              this._getLocation();
            },
            fail: (err) => {
              this.showToast({
                message: '您已取消授权，无法定位当前位置！',
              });
            }
          });
        }
      });

    } catch (e) {
      this.setData({
        isLocating: false,
      });
      console.log(e);
    }
    // 先获取设备的当前位置信息
    // 在通过schedule中的考勤信息来判断是否是在打卡范围内
  },

  _getLocation() {
    this.data.amap.getRegeoWithWXLocation({
    // this.data.amap.getRegeo({
      success: (res) => {
        const result = res[0];
        const duty = this.data.duty;
        duty.latitude = result.latitude;
        duty.longitude = result.longitude;
        duty.locationAddress = result.name;
        duty.storeId = 0;
        duty.locationName = '';
        this._setLocation(duty);
        // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
        wx.removeStorage({key:"userLocation"});
      },
      fail: (info) => {
        console.log(info);
        // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
        wx.removeStorage({key:"userLocation"});
      }
    });
  },

  _setMapConfig() {
    const mapConfig = {};
    mapConfig.longitude = this.data.duty.longitude;
    mapConfig.latitude = this.data.duty.latitude;
    this.setData({
      mapConfig: mapConfig,
    });
  },

  _setLocation(duty) {
    if (this.data.clockInfo) {
      const clockInfo = this.data.clockInfo;
      // const duty = this.data.duty;
      const currentLatitude = duty.latitude;
      const currentLongitude = duty.longitude;
      const schedule = this.data.clockInfo.schedule;
      const scheduleLatitude = schedule.latitude;
      const scheduleLongitude = schedule.longitude;
      // console.log(currentLatitude, currentLongitude, scheduleLatitude, scheduleLongitude);
      let _distance = getDistanceByLatAndLon(currentLatitude, currentLongitude, scheduleLatitude, scheduleLongitude) * 1000;
      // console.log(_distance, clockInfo.schedule.range);
      // console.log(duty.isStoreLocation);
      // 在打卡范围内
      duty.restRange = _distance ? Math.round(_distance) : '';
      if (_distance && _distance <= clockInfo.schedule.range) {
        duty.isScheduleLocation = true;
        duty.isStoreLocation = true;
        duty.locationName = clockInfo.schedule.storeName;
        duty.locationAddress = clockInfo.schedule.storeAddress;
        duty.storeId = clockInfo.schedule.storeId;
        duty.latitude = duty.latitude;
        duty.longitude = duty.longitude;

        duty.dutyText = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).dutyText;
        duty.clockStatus = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).clockStatus;
        duty.abnormalTypes = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).abnormalTypes;
      } else {
        duty.isScheduleLocation = false;
        duty.isStoreLocation = false;
        duty.dutyText = '外勤打卡';
        duty.clockStatus = this.data.clockTypes.ABNORMAL;
        duty.abnormalTypes = [CONSTANT.ATTENDANCE.ABNORMAL_TYPES.LEGWORK];
      }

      if (!duty.isStoreLocation) {
        // const noneScheduleLatitude =
        this.data.markers.map((store) => {

          if (store.id !== clockInfo.schedule.storeId) {
            const noneScheduleLatitude = Number(store.latitude);
            const noneScheduleLongitude = Number(store.longitude);
            _distance = getDistanceByLatAndLon(currentLatitude, currentLongitude, noneScheduleLatitude, noneScheduleLongitude) * 1000;

            if (_distance && _distance <= clockInfo.schedule.range) {
              duty.isStoreLocation = true;
            }
          }

        });
        // console.log(cloneDeep(this.data.markers));
      }

      // duty.isStoreLocation是否在门店范围中
      const callout = {
        borderRadius: 6,
        bgColor: '#23b9de',
        textAlign: 'center',
        color: '#fff',
        fontSize: 14,
        // borderWidth: 1,
        padding: 10,
        display: 'ALWAYS',
      };
      const markers = cloneDeep(this.data.markers);
      const index = markers.findIndex(item => item.id === 1000000);
      // console.log(schedule, duty);
      if (schedule.id) {
        if (duty.isScheduleLocation) {
          callout.content = '已进入考勤范围';
        } else if (!duty.isScheduleLocation) {
          if (duty.restRange !== null || duty !== undefined) {
            callout.content = `距离考勤门店${duty.restRange}m, 未进入考勤区域`;
          } else {
            callout.content = '定位失败，请授权地理位置后重试';
          }
          callout.bgColor = '#fb9e0f';
        }
        //  else if (!duty.storeId && !duty.isScheduleLocation && duty.isStoreLocation) {
        //   callout.content = `在${schedule.storeName}暂未排班`;
        //   callout.bgColor = '#fb9e0f';
        // }
      }

      if (index > 0) {
        markers.splice(index, 1);
      }
      markers.push({
        id: 1000000,
        longitude: duty.longitude,
        latitude: duty.latitude,
        iconPath: myLocation,
        width: 34,
        height: 42,
        anchor: {x: .5, y: .9},
        callout,
      });

      // const circles = this.data.circles;
      // console.log('_setLocation', markers, circles);

      this.setData({
        duty: cloneDeep(duty),
        clockInfo: cloneDeep(clockInfo),
        markers,
      });
    }
  },
  _setMapOptions() {
    const marker = this.data.markers[this.data.markers.length - 1];
    const markers = [];
    const circles = [];
    const stores = this.data.stores;

    map(stores, store => {
      if (store.latitude && store.longitude) {
        markers.push({
          ...store,
          iconPath: petkitLogo,
          width: 40,
          height: 40,
          anchor: {x: .5, y: .5},
        });
        circles.push({
          latitude: Number(store.latitude),
          longitude: Number(store.longitude),
          color: '#23b9de',
          fillColor: '#23b9de33',
          radius: this.data.clockInfo.schedule.range,
          stokeWidth: 1,
        });
      }
    });
    marker && markers.push(marker);


    // console.log('_setMapOptions', markers, circles);
    this.setData({
      markers,
      circles,
    });
  }
};

const mapStateToData = state => ({
  userId: state.user.loginWp.userId,
  userName: state.user.loginWp.userName,
  duty: state.attendance.clock.duty,
  clockInfo: state.attendance.clock.clockInfo,
  stores: state.attendance.clock.stores.filter(item => item.latitude && item.longitude && Number(item.longitude) > Number(item.latitude)),
  // range: state.attendance.clock.schedule.range,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  saveDuty: acs.attendance.clock.saveDuty,
  saveClockInfo: acs.attendance.clock.saveClockInfo,
  requestStoreList: acs.attendance.clock.requestStoreList,
  requestPunch: acs.attendance.clock.requestPunch,
  requestClockInfo: acs.attendance.clock.requestClockInfo,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
