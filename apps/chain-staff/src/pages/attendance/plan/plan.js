import {connect} from 'utils/weapp-redux';
import {bindActionCreators} from 'redux';
import {acs, getStorage, store} from '@petkit/redux';
import {cloneDeep} from 'lodash-es';
import moment from 'moment';

const tableConfig = {
  tableTap(e) {
    console.log(e);
  }
};

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    isTop46: true,
    pickerState: {
      disabled: true
    },
    // 当前门店id是否是当前的门店的店长
    storeId: null,
    // 排班设置
    showModalStatus: false,
    storeList: [],
    employeeList: [],
    employeePanelData: [],
    tableData: [],
    // 选择店员界面选中的店员
    checkedEmployee: {},
    // 门店员工列表，不包含员工信息
    storeWorkEmployeeList: [],
    // refreshEmployeeData: true,
    // 排班设置 - 下拉搜索框
    employeeCheckedId: '',
    inputShowed: false,
    inputVal: '',
    showPanel: false,
    showList: true,
    shifts: [],

    // 班次设置
    // tabs: [{
    //   id: 0,
    //   text: '排班设置',
    // }, {
    //   id: 1,
    //   text: '班次设置',
    // }],
    tabs: [],
    storeLeader: 0,
    // 表格最下面一行颜色显示的列表
    tablePickerList: [],
    // 传递给 table 的 picker 内容
    shiftPickerList: [],
    table: {
      options: {
        def: {
          row: '日期',
          col: '姓名'
        },
        rows: [],
        cols: [],
        cell: {}
      },
      data: []
    },
    sub: null,
    colors: [],
    originColors: [],
    // list: [],
    shift: {
      id: '',
      storeId: '',
      name: '',
      start: '',
      end: '',
      color: '',
      enable: '',
      usable: '',
      workShiftSuspendList:[{
        start: '',
        end: ''
      }],
    },
    origin: {
      storeList: [],
      employeeList: []
    },
    detailTitle: '新建班次',
    // 标识状态
    _action: '',
    showDetail: false,
    // 是否设置吃饭时间
    isSelected: false,
    // 当前吃饭时间次序
    currentEatIndex: 0,

    // 是否显示班次设置
    showSettingWork: false,

    // 经营范围
    businessModel: '',
  },
  bindPickerScheduleChange() {
    this.setData({
      pickerState: {
        disabled: true
      }
    });
  },
  // 点击 table 的 cell弹出的 picker 确定事件
  pickerChange({detail}) {
    let index = Number(detail.index);
    let shift = this.data.shiftPickerList[index];
    let key = {};
    key.firstKey = detail.cell.month + '/' + detail.cell.day;
    key.secondKey =
      detail.cell.userId + '-' + detail.cell.month + '/' + detail.cell.day;
    this.saveModifyTableCellKey(key);
    // 取消排班在班次不存在，是前端写死的一个类似于班次的选项, 用于删除排班
    // 有排班：取消排班；更新排班
    // 无排班：取消排班；新增排班
    let that = this;
    if (shift.name === '取消排班') {
      wx.showModal({
        title: '提示',
        content: '更改排班信息会导致该店员当天考勤、请假纪录清除，修改排班后记得重新打卡、提交修改考勤与请假。',
        success: function (res) {
          if (res.confirm && detail.cell && detail.cell.id) {
            const params = {
              id: detail.cell.id,
              userId: that.data.userId,
              storeId: that.data.storeId
            };
            that.requestDeleteSchedule(params, {
              success: () => {
                let data = that.data.tableData;
                let table = that.data.table;
                table.data = data;
                let start = Date.now();
                that.setData({
                  table: table
                }, () => {
                  let end = Date.now();
                  console.log(end - start, '渲染时长');
                });
              }
            });
          }
        }
      });
    } else {
      let that = this;
      if (detail.cell.workShiftId !== shift.id) {
        if (detail.cell.id) {
          wx.showModal({
            title: '提示',
            content: '更改排班信息会导致该店员当天考勤、请假纪录清除，修改排班后记得重新打卡、提交修改考勤与请假。',
            success: res => {
              if (res.confirm) {
                that.updateTableSchedule(detail, shift, that);
              }
            }
          });
        } else {
          this.updateTableSchedule(detail, shift, that);
        }
      }
    }
  },
  // 更新排班信息
  updateTableSchedule(detail, shift, that) {
    // startTime, endTime 需要拼接转换成毫秒
    let endTime = '',
      startTime = '';

    if (shift.name === '休息') {
      endTime =
      detail.cell.year +
      '-' +
      detail.cell.month +
      '-' +
      detail.cell.day +
      ' ' +
      shift.end +
      ':59';
    } else {
      endTime =
      detail.cell.year +
      '-' +
      detail.cell.month +
      '-' +
      detail.cell.day +
      ' ' +
      shift.end +
      ':00';
    }

    startTime =
    detail.cell.year +
    '-' +
    detail.cell.month +
    '-' +
    detail.cell.day +
    ' ' +
    shift.start +
    ':00';

    endTime = moment(endTime).valueOf();
    startTime = moment(startTime).valueOf();

    const workScheduleParams = {
      start: startTime,
      end: endTime,
      color: shift.color,
      name: shift.name,
      workShiftId: shift.id,
      id: detail.cell.id,
      employeeId: detail.cell.userId
    };
    const params = {
      userId: that.data.userId,
      storeId: that.data.storeId,
      workScheduleParams: workScheduleParams
    };

    this.acsShowLoading();
    that.requestUpdateSchedule(params, {
      success: () => {
        this.acsShowLoadingHide();
        let data = that.data.tableData;
        let table = that.data.table;
        table.data = data;
        that.setData({
          table: table
        });
      }
    });
  },
  /**
   *  点击事件
   */
  onCellTap({detail}) {
    this.setData({
      pickerState: {
        disabled: false
      }
    });
    if (detail.isOtherStore) {
      let message = `当前已在${detail.workStoreName}排班, ${
        detail.workStoreName
      }店长取消排班后，请重新添加店员`;
      wx.showModal({
        content: message,
        showCancel: false
      });
    }
  },
  onColTap({detail}) {
    // 点击表头
  },
  onRowTap({detail}) {
    // 点击时间跳转到修改考勤界面
    let dateTime = null;
    const time = this.data.time;
    const _time = time.timeBak.find(
      item => moment(item.day).format('MM/DD') === detail.text
    );
    if (_time) {
      dateTime = _time.day;
      wx.navigateTo({
        url: '/pages/attendance/schedule/schedule?datetime=' + dateTime
      });
    }
  },
  onStoreChange({detail: {storeId, isLeader}}) {
    this.changeTableBySelectStore(storeId, isLeader);
    this.setData({
      storeId: storeId,
      storeLeader: isLeader,
    });
  },
  /*班次设置 */
  setingWork(){
    this.setData({
      showSettingWork: true
    })
  },
  setingRest(){
    wx.navigateTo({
      url: '/pages/attendance/plan/seting/seting-rest/seting-rest',
    });
    this.changeIsStoreRestPage();
  },
  /*店休设置 */
  /*----排班设置----*/
  powerDrawer(e) {
    let currentStatus = e.currentTarget.dataset.status;
    // 获取所有的门店和员工，用于下拉选择

    if (currentStatus === 'open'
      && this.data.origin.storeList
      && this.data.origin.storeList.length === 0
    ) {
      let type = getStorage().getItem('businessModel');

      if (type === 'JOIN') {
        this.getAllJoinStoreEmployees();
      } else {
        this.getAllStoreEmployees();
      }
    }
    this.setData({
      inputVal: '',
      showList: false,
      showPanel: false,
      employeeCheckedId: ''
    });
    this.util(currentStatus);
  },

  powerDrawerSubmit(e) {
    let currentStatus = e.currentTarget.dataset.status;
    let hasEmployee = false;
    this.util(currentStatus);
    this.data.storeWorkEmployeeList.forEach(item => {
      if (item.userId === this.data.checkedEmployee.userId) {
        hasEmployee = true;
      }
    });
    if (
      !hasEmployee &&
      this.data.checkedEmployee &&
      this.data.checkedEmployee.storeId &&
      this.data.time
    ) {
      this.changeTableByAddEmployee(
        {
          userId: this.data.userId,
          storeId: this.data.storeId,
          start: this.data.time.currentFirstDay,
          end: this.data.time.nextLastDay,
          employeeIds: [this.data.checkedEmployee.userId]
        },
        {
          success: () => {
            let storeWorkEmployeeList = this.data.storeWorkEmployeeList,
              cols = [],
              table = this.data.table;
            if (storeWorkEmployeeList) {
              storeWorkEmployeeList.map(item => {
                cols.push(item.name);
              });
            }
            table.options.cols = cols;
            table.data = this.data.tableData;
            this.setData({
              table: table,
              storeWorkEmployeeList: storeWorkEmployeeList
            });
          }
        }
      );
    }
  },

  /* 动画部分 */
  util: function (currentStatus) {
    // 第1步：创建动画实例
    let animation = wx.createAnimation({
      duration: 200, //动画时长
      timingFunction: 'linear', //线性
      delay: 0 //0则不延迟
    });

    // 第2步：这个动画实例赋给当前的动画实例
    this.animation = animation;

    // 第3步：执行第一组动画
    animation
      .opacity(0)
      .rotateX(-100)
      .step();

    // 第4步：导出动画对象赋给数据对象储存
    this.setData({
      animationData: animation.export()
    });

    // 第5步：设置定时器到指定时候后，执行第二组动画
    setTimeout(
      function () {
        // 执行第二组动画
        animation
          .opacity(1)
          .rotateX(0)
          .step();
        // 给数据对象储存的第一组动画，更替为执行完第二组动画的动画对象
        this.setData({
          animationData: animation
        });

        //关闭
        if (currentStatus == 'close') {
          this.setData({
            showModalStatus: false
          });
        }
      }.bind(this),
      200
    );

    // 显示
    if (currentStatus == 'open') {
      this.setData({
        showModalStatus: true
      });
    }
  },
  /*-----排班设置 - 搜索下拉框 - 开始-----*/
  showInput: function () {
    this.setData({
      inputShowed: true
    });
  },

  hideInput: function () {
    this.setData({
      inputVal: '',
      inputShowed: false
    });
  },

  // 点击清除搜索框内容
  clearInput: function () {
    this.setData({
      inputVal: '',
      showPanel: false,
      employeeCheckedId: '',
    });
  },

  // 搜索框输入事件
  inputTyping: function (e) {
    let storeList = [],
      employeeList = [],
      value = e.detail.value,
      showList = this.data.showList,
      showPanel = this.data.showPanel;
    storeList = this.data.origin.storeList.filter(
      // item => item.storeName.indexOf(value) !== -1
      item => item.storeName.includes(value)
    );
    employeeList = this.data.origin.employeeList.filter(
      // item => item.name.indexOf(value) !== -1
      item => item.name.includes(value)
    );
    showList = true;
    showPanel = false;
    this.setData({
      showList: showList,
      showPanel: showPanel,
      inputVal: e.detail.value,
      storeList: storeList,
      employeeList: employeeList,
      employeeCheckedId: '',
    });
  },

  selectItemTap(e) {
    let inputVal = e.target.dataset.value,
      storeId = e.target.dataset.storeId,
      item = e.target.dataset.item,
      employeePanelData = [];

    if (item instanceof Array) {
      employeePanelData = item;
    } else {
      employeePanelData = [item];
    }
    this.setData({
      inputVal: inputVal,
      showList: false,
      showPanel: true,
      employeePanelData: employeePanelData
    });
  },
  /*-----排班设置 - 搜索下拉框 - 结束-----*/

  /*----班次设置----*/
  onTabTap(e) {
    console.log(e);
    this.setData({
      showSettingWork: false,
    })
  },

  // 班次时间选择变化事件
  bindTimeChange(e) {
    this.setData({
      time: e.detail.value
    });
  },

  // 班次是否启用事件
  onTapCheckboxChange(e) {
    if (this.data.showDetail) {
      return;
    }
    if (e.currentTarget.dataset.name === '休息') {
      wx.showModal({
        content: '默认的休息班次不可修改',
        showCancel: false
      });
      return;
    }
    let id = e.currentTarget.dataset.value;
    let checkboxItems = this.data.list;
    let tablePickerList = this.data.tablePickerList;
    let usable = false;

    checkboxItems.forEach(item => {
      if (item.id === id) {
        usable = !item.usable;
        item.usable = !item.usable;
      }
    });
    let shift = this.data.list.filter(item => item.id === id),
      paramUsable = usable ? 1 : 0,
      storeId = this.data.storeId;
    shift.usable = usable;

    // 更新班次信息之后，需要手动更新传入给 table 的可选择班次
    this.requestUpdateStatus(
      {
        id: id,
        usable: paramUsable,
        userId: this.data.userId,
        storeId: storeId
      },
      {
        success: () => {
          this.getList(
            {storeId: this.data.storeId, userId: this.data.userId},
            {
              success: () => {
                let showList = cloneDeep(this.data.list);
                let tablePickerList = showList.filter(
                  item => item.usable === true
                );

                let table = this.data.table;
                let cell = {};
                let editOption = {};
                let shiftPickerList = cloneDeep(tablePickerList);
                // 特殊逻辑，新增取消排班
                let cancelShift = {
                  id: 0,
                  name: '取消排班',
                  color: '#ffffff',
                  usable: true,
                  key: '取消排班'
                };
                shiftPickerList.push(cancelShift);
                editOption.type = 'picker';
                editOption.mode = 'selector';
                editOption.range = shiftPickerList;
                editOption.rangeKey = 'key';

                cell.editOption = editOption;
                table.options.cell = cell;
                this.setData({
                  table: table,
                  tablePickerList: tablePickerList,
                  shiftPickerList: shiftPickerList,
                  showDetail: false
                });
              }
            }
          );
        }
      }
    );
    tablePickerList = checkboxItems.filter(item => {
      item.usable === true;
    });
    this.setData({
      list: checkboxItems,
      shift: shift,
      tablePickerList: tablePickerList
    });
  },

  // 点击班次跳转到编辑页面事件
  onTapEdit(e) {

    let id = e.currentTarget.dataset.value;
    let shift = this.data.list.filter(item => item.id === id);
    let eatTimeStartIndex = this.data.timeLags.indexOf(shift[0].start);
    let eatTimeEndIndex = this.data.timeLags.indexOf(shift[0].end);
    this.getEatShiftTimeRange({eatTimeStartIndex, eatTimeEndIndex});
    let timeDiff = (getSeconds(shift[0].start, shift[0].end)) / 3600;
    let colors = cloneDeep(this.data.originColors);
    colors.forEach(item => {
      if (item.value === shift[0].color) {
        item.used = false;
      }
    });
    let detailTitle = '修改' + shift[0].showValue;
    if (this.data.isChecked || shift[0].workShiftSuspendList) {
      if (shift[0].workShiftSuspendList) {
        if (shift[0].workShiftSuspendList.length > 0 && shift[0].workShiftSuspendList.length < 2) {
          this.setData({
            isChecked: true
          });
        } else if (shift[0].workShiftSuspendList.length === 2){
          this.setData({
            isChecked: true,
            isShow: false,
          });
        } else {
          this.setData({
            isChecked: false
          });
        }
      }
    } else {
      this.setData({
        isChecked: false,
      });
    }
    this.setData({
      shift: shift[0],
      showDetail: true,
      detailTitle: detailTitle,
      _action: shift[0].color,
      colors: colors,
      timeDiff
    });

    function getSeconds(t1, t2) {
      let a = t1.split(':');
      let b = t2.split(':');
      return ((Number(b[0])*3600+Number(b[1])*60) - (Number(a[0])*3600+Number(a[1])*60));
    }
},

  // 班次列表删除事件
  onTapDelete(e) {
    let that = this;
    wx.showModal({
      title: '提示',
      content: '是否删除？',
      success: function (res) {
        if (res.confirm) {
          const id = e.currentTarget.dataset.value,
            storeId = that.data.storeId;
          const params = {
            id: id,
            storeId: storeId,
            userId: that.data.userId
          };
          that.requestDelete(params, {
            success: () => {
              that.getList(
                {storeId: that.data.storeId, userId: that.data.userId},
                {
                  success: () => {
                    let showList = cloneDeep(that.data.list);
                    let tablePickerList = showList.filter(
                      item => item.usable === true
                    );

                    let table = that.data.table;
                    let cell = {};
                    let editOption = {};
                    let shiftPickerList = cloneDeep(tablePickerList);
                    // 特殊逻辑，新增取消排班
                    let cancelShift = {
                      id: 0,
                      name: '取消排班',
                      color: '#ffffff',
                      usable: true,
                      key: '取消排班'
                    };
                    shiftPickerList.push(cancelShift);
                    editOption.type = 'picker';
                    editOption.mode = 'selector';
                    editOption.range = shiftPickerList;
                    editOption.rangeKey = 'key';

                    cell.editOption = editOption;
                    table.options.cell = cell;
                    that.setData({
                      table: table,
                      tablePickerList: tablePickerList,
                      shiftPickerList: shiftPickerList,
                      showDetail: false
                    });
                  }
                }
              );
            }
          });
        } else if (res.cancel) {
          console.log('用户点击取消删除');
        }
      }
    });
  },

  // 编辑班次页面提交事件，分为新增和更新两种情况，根据是否有班次 id 区分
  onSubmitTap(e) {
    const eatTimes = this.data.shift.workShiftSuspendList;
    let message = '';
    if (!this.data.shift.name) {
      message = '请输入班次名称';
    } else if (!this.data.shift.start) {
      message = '请选择班次的开始时间';
    } else if (!this.data.shift.end) {
      message = '请选择班次的结束时间';
    } else if (!this.data.shift.color) {
      message = '请选择班次颜色';
    } else if (this.data.isChecked && eatTimes.length > 0) {
      eatTimes.forEach(item => {
        if (!item.start) {
          message = '请填写吃饭时间';
        }
      });
    }
    if (message !== '') {
      wx.showModal({
        content: message,
        showCancel: false
      });
      return;
    }


    if (this.data.shift.id) {
      const params = {
        id: this.data.shift.id,
        color: this.data.shift.color,
        end: this.data.shift.end,
        name: this.data.shift.name,
        start: this.data.shift.start,
        storeId: this.data.storeId,
        userId: this.data.userId,
        usable: this.data.shift.usable ? 1 : 0
      }
      if (this.data.isChecked && this.data.shift.workShiftSuspendList && this.data.shift.workShiftSuspendList.length > 0) {
        params.workShiftSuspendList = this.data.shift.workShiftSuspendList;
      }
      this.requestUpdate(
        params,
        {
          success: () => {
            this.getList(
              {storeId: this.data.storeId, userId: this.data.userId},
              {
                success: () => {
                  let showList = cloneDeep(this.data.list);
                  let tablePickerList = showList.filter(
                    item => item.usable === true
                  );

                  let table = this.data.table;
                  let cell = {};
                  let editOption = {};
                  let shiftPickerList = cloneDeep(tablePickerList);
                  // 特殊逻辑，新增取消排班
                  let cancelShift = {
                    id: 0,
                    name: '取消排班',
                    color: '#ffffff',
                    usable: true,
                    key: '取消排班'
                  };
                  shiftPickerList.push(cancelShift);
                  editOption.type = 'picker';
                  editOption.mode = 'selector';
                  editOption.range = shiftPickerList;
                  editOption.rangeKey = 'key';

                  cell.editOption = editOption;
                  table.options.cell = cell;
                  this.setData({
                    table: table,
                    _action: '',
                    tablePickerList: tablePickerList,
                    shiftPickerList: shiftPickerList,
                    showDetail: false
                  });
                }
              }
            );
          }
        }
      );
    } else {
      // @TODO 当颜色不选择不能提交，或者提交报错
      // 当不勾选吃饭时间时：当时后端要求，不将该workShiftSuspendList字段传参过去
      const params = {
        color: this.data.shift.color,
        enable: true,
        end: this.data.shift.end,
        name: this.data.shift.name,
        start: this.data.shift.start,
        userId: this.data.userId,
        storeId: this.data.storeId,
      };
      if (this.data.isChecked && this.data.shift.workShiftSuspendList && this.data.shift.workShiftSuspendList.length > 0) {
        params.workShiftSuspendList = this.data.shift.workShiftSuspendList;
      }
        this.requestCreate(
          params,
          {
            success: (res) => {
              console.log('res', res);
              this.getList(
                {storeId: this.data.storeId, userId: this.data.userId},
                {
                  success: () => {
                    let showList = cloneDeep(this.data.list);
                    let tablePickerList = showList.filter(
                      item => item.usable === true
                    );

                    let table = this.data.table;
                    let cell = {};
                    let editOption = {};
                    let shiftPickerList = cloneDeep(tablePickerList);
                    // 特殊逻辑，新增取消排班
                    let cancelShift = {
                      id: 0,
                      name: '取消排班',
                      color: '#ffffff',
                      usable: true,
                      key: '取消排班'
                    };
                    shiftPickerList.push(cancelShift);
                    editOption.type = 'picker';
                    editOption.mode = 'selector';
                    editOption.range = shiftPickerList;
                    editOption.rangeKey = 'key';

                    cell.editOption = editOption;
                    table.options.cell = cell;
                    this.setData({
                      table: table,
                      _action: '',
                      tablePickerList: tablePickerList,
                      shiftPickerList: shiftPickerList,
                      showDetail: false
                    });
                  }
                }
              );
            }
          }
        );
    }
  },

  // 班次编辑取消事件
  onCancelTap(e) {
    this.setData({
      showDetail: false,
    });
  },

  // 选择颜色事件
  onTapChangeStatus(e) {
    let shift = this.data.shift;
    if (e.target.dataset && !e.target.dataset.used) {
      if (this.data._action !== e.target.dataset.value) {
        this.setData({
          _action: e.target.dataset.value
        });
        shift.color = e.target.dataset.value;
      }
    }
  },

  // 点击新增班次
  onTapAdd(e) {
    let shift = {
      id: '',
      storeId: '',
      name: '',
      start: '',
      end: '',
      color: '',
      enable: '',
      workShiftSuspendList:[{
        start: '',
        end: ''
      }],
    };
    let defaultColor = '';
    let originColors = this.data.originColors;
    for (let item of originColors) {
      if (!item.used) {
        defaultColor = item.value;
        break;
      }
    }
    if (!defaultColor || defaultColor.length === 0) {
      wx.showModal({
        content: '默认颜色已用完，请删除无用排班后重新添加或联系产品添加',
        showCancel: false
      });
    } else {
      shift.color = defaultColor;
    }

    this.setData({
      shift: shift,
      showDetail: true,
      detailTitle: '新建班次',
      colors: this.data.originColors,
      _action: defaultColor,
      isChecked: false,
      isShow: true,
    });
  },

  // 是否设置吃饭时间
  onSelectedEatTimeTap(e) {
    const eatTimes =  this.data.shift && this.data.shift.workShiftSuspendList || [];
    if (!this.data.isChecked && eatTimes.length === 0) {
      eatTimes.push({});
    }

    this.setData({
      isChecked: !this.data.isChecked,
      shift: {
        ...this.data.shift,
        workShiftSuspendList: eatTimes
      }
    });

  },

  // 取消吃饭时间
  onCancelEatTime(e) {
    const index = e.currentTarget.dataset.index;
    const eatTimes = this.data.shift.workShiftSuspendList;
    index === 1 ? eatTimes.pop() : eatTimes.shift();
    this.setData({
      shift: {
        ...this.data.shift,
        workShiftSuspendList: eatTimes
      },
    });
  },

  // 编辑班次页面 - 班次时间选择事件
  bindPickerChange(e) {
    let index = e.detail.value;
    let shift = this.data.shift;
    if (e.target.dataset.type === 'start') {
      if (
        !shift.end ||
        (shift.end && CompareDate(shift.end, this.data.timeLags[index]))
      ) {
        shift.start = this.data.timeLags[index];
      }
      this.setData({
        shift: shift,
        workStartIndex: index
      });
    } else if (e.target.dataset.type === 'end') {
      if (
        !shift.start ||
        (shift.start && CompareDate(this.data.timeLags[index], shift.start))
      ) {
        shift.end = this.data.timeLags[index];
      }

      let timeDiff = (getSeconds(shift.start, shift.end)) / 3600;

      this.setData({
        shift: this.data.shift,
        workEndIndex: index,
        timeDiff
      });
    }

    function CompareDate(t1, t2) {
      let date = new Date();
      let a = t1.split(':');
      let b = t2.split(':');
      return date.setHours(a[0], a[1]) > date.setHours(b[0], b[1]);
    }

    function getSeconds(t1, t2) {
      let a = t1.split(':');
      let b = t2.split(':');
      return ((Number(b[0])*3600+Number(b[1])*60) - (Number(a[0])*3600+Number(a[1])*60));
    }

    let eatTimeStartIndex = this.data.workStartIndex;
    let eatTimeEndIndex = this.data.workEndIndex;
    this.getEatShiftTimeRange({eatTimeStartIndex, eatTimeEndIndex});
  },

  onSetCurrentEatTime(e) {
    const currentEatIndex = e.currentTarget.dataset.index;
    this.setData({
      currentEatIndex,
    });
  },

  // 选择吃饭时间 -- 第一次吃饭时间
  bindEatPickerChange(e) {
    // start end
    let type = e.target.dataset.type;
    let index= e.detail.value;
    let eatTimes = this.data.shift.workShiftSuspendList;

    if (type === 'start') {
      // 之前吃饭时间只定为半小时
      let endIndex = Number(index) + 1;

      eatTimes[this.data.currentEatIndex] = {
        start: this.data.eatTimeLags[index],
        end: this.data.eatTimeLags[endIndex] ? this.data.eatTimeLags[endIndex] : this.getEndTime(this.data.eatTimeLags[index])
      }
    } else {
      const startIndex = this.data.eatTimeLags.findIndex(item => item === eatTimes[this.data.currentEatIndex].start);
      if (Number(index) <= startIndex) {
        index = startIndex + 1;
      }
      if (Number(index) - startIndex > 4) {
        index = startIndex + 4;
      }

      eatTimes[this.data.currentEatIndex] = {
        start: eatTimes[this.data.currentEatIndex].start,
        end: this.data.eatTimeLags[index],
      }
    }

    this.setData({
      shift: {
        ...this.data.shift,
        workShiftSuspendList: eatTimes,
      }
    });
  },

  getEndTime(timestr) {
    let time = timestr.split(':');
    let min = parseInt(time[1]) + 30;

    let hour = min == 60 ? parseInt(time[0]) + 1 : time[0];
    hour = hour + '';
    let newTimer = `${hour.length==2?hour:'0'+hour}:${min==60 ? '00' : min}`
    return newTimer;
  },
  // 增加第二次吃饭时间
  onGetEatTime() {
    const shift = cloneDeep(this.data.shift);
    if (!shift || !shift.workShiftSuspendList || shift.workShiftSuspendList.length < 2) {
      shift.workShiftSuspendList.push({});
      this.setData({
        shift,
      })
    }
  },

  // 编辑班次页面 - 输入班次名称事件
  bindInputTap(e) {
    let shift = this.data.shift;
    let inputName = e.detail.value;
    if (inputName === '休息') {
      shift.name = '';
      wx.showModal({
        content: '不可新增与默认班次名称相同的班次',
        showCancel: false
      });
    } else {
      shift.name = inputName;
    }
    this.setData({
      shift: shift
    });
  },

  // 点击选择店员，点击取消选择店员；
  bindCheckedTap(e) {
    let checkedUserId = e.currentTarget.dataset.id;
    let employeeCheckedId = '';
    let checkedEmployee = {};
    if (this.data.employeeCheckedId === checkedUserId) {
      employeeCheckedId = '';
      checkedEmployee = {};
    } else {
      employeeCheckedId = checkedUserId;
      checkedEmployee = e.currentTarget.dataset.item;
    }
    this.setData({
      employeeCheckedId: employeeCheckedId,
      checkedEmployee: checkedEmployee
    });
  },

  changeTableBySelectStore(storeId, storeLeader) {
    let table = this.data.table;
    let tabs = [];
    if (storeLeader === 1) {
      tabs = [
        {
          id: 0,
          text: '点击表格排班'
        },
        {
          id: 1,
          text: '设置'
        }
      ];
    } else {
      tabs = [
        {
          id: 0,
          text: '查看排班'
        }
      ];
    }
    // 获取表格左侧时间列的时间
    this.getWorkScheduleTime();
    // 获取班次时间的选择
    this.getWorkShiftAllTimeLag();
    table.options.rows = this.data.time && this.data.time.showTimeCols || [];
    this.setData({
      table: table,
      storeLeader: storeLeader,
      tabs: tabs
    });
    if (this.data.time && this.data.userId) {
      let params = {
        userId: this.data.userId,
        storeId: storeId,
        start: this.data.time.currentFirstDay,
        end: this.data.time.nextLastDay,
        // year: this.data.time.curYear,
        // months: [this.data.time.curMonth, this.data.time.nextMonth]

      };
      // 获取表头和排班信息，并组成要传递给表格的数据形式
      this.getStoreWorkSchedule(params, {
        success: () => {
          let data = this.data.tableData,
            table = this.data.table,
            cols = [],
            storeWorkEmployeeList = this.data.storeWorkEmployeeList;
          storeWorkEmployeeList.map(item => {
            cols.push(item.name);
          });
          table.data = data;
          table.options.cols = cols;
          let startTime = Date.now();
          table.options.rows = this.data.time && this.data.time.showTimeCols || [];
          this.setData({
            table: table,
            storeWorkEmployeeList: storeWorkEmployeeList
          }, () => {
            let endTime = Date.now();
            console.log(endTime - startTime, '渲染时长:955');
          });
        }
      });
      // 获取班次的列表
      this.getList(
        {
          userId: this.data.userId,
          storeId: storeId
        },
        {
          success: () => {
            let showList = cloneDeep(this.data.list);
            let tablePickerList = showList.filter(item => item.usable === true);

            let table = this.data.table;
            let cell = {};
            let editOption = {};
            let shiftPickerList = cloneDeep(tablePickerList);
            // 特殊逻辑，新增取消排班
            let cancelShift = {
              id: 0,
              name: '取消排班',
              color: '#ffffff',
              usable: true,
              key: '取消排班'
            };
            shiftPickerList.push(cancelShift);
            editOption.type = 'picker';
            editOption.mode = 'selector';
            editOption.range = shiftPickerList;
            editOption.rangeKey = 'key';

            cell.editOption = editOption;
            table.options.cell = cell;
            this.setData({
              table: table,
              tablePickerList: tablePickerList,
              shiftPickerList: shiftPickerList
            });
          },
          failure: () => { }
        }
      );
    }
  },

  onFinishPlanTap() {
    this.setData({
      showSettingWork: false,
    });
  },
  /*----班次设置----*/

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
     // 页面第一次加载时判断是否选中吃饭时
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    let storeId = getStorage().getItem('storeId');
    let storeLeader = getStorage().getItem('storeLeader');
    const businessModel = getStorage().getItem('businessModel');
    this.setData({
      storeId: storeId,
      storeLeader: storeLeader,
      businessModel,
    });
    if (storeId && storeId !== '') {
      this.data.sub = store.subscribe(data => {
        let state = store.getState(),
          storeList = state.attendance.workArrange.storeList,
          employeeList = state.attendance.workArrange.employeeList;
        if (!this.data.showList) {
          this.setData({
            storeList: storeList,
            employeeList: employeeList,
            origin: {
              storeList: storeList,
              employeeList: employeeList
            }
          });
        }
      });
    } else {
      wx.showModal({
        content: '未选择门店，请先选择门店!',
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            console.log('用户点击确定');
          }
          wx.navigateTo({
            url: '/pages/check/check'
          });
        }
      });
    }

    // 店休设置后重新获取改变排班列表
    if (this.data.isStoreRestPage) {
      const userId = this.data.userId;
      const start = moment().startOf('months').valueOf();
      const end = moment().add(1, 'months').endOf('months').valueOf();
      this.getStoreWorkSchedule({
        storeId,
        userId,
        start,
        end
      }, {
        success: () => {
          const table = this.data.table;
          table.data = this.data.tableData;
          this.setData({
            table,
          });

          this.changeIsStoreRestPage();
        }
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
};
const mapStateToData = state => {
  return {
    list: state.attendance.workShift.list,
    time: state.attendance.workArrange.time,
    colors: state.attendance.workShift.colors,
    tableData: state.attendance.workArrange.tableData,
    simpleStoreList: state.attendance.workArrange.simpleStoreList,
    storeWorkEmployeeList: state.attendance.workArrange.storeWorkEmployeeList,
    originColors: state.attendance.workShift.colors,
    timeLags: state.attendance.workShift.timeLags,
    userId: state.user.loginWp.userId,
    eatTimeLags: state.attendance.workShift.eatTimeLags,
    workShiftSuspendList: state.attendance.workShift.workShiftSuspendList,
    isStoreRestPage: state.attendance.store.rest.isStoreRestPage,
  }
};

const mapDispatchToPage = dispatch =>
  bindActionCreators(
    {
      acShowToast: acs.global.toast.show,
      getList: acs.attendance.workShift.getList,
      getDetail: acs.attendance.workShift.getDetail,
      getWorkShiftAllTimeLag: acs.attendance.workShift.getWorkShiftAllTimeLag,
      requestCreate: acs.attendance.workShift.requestCreate,
      requestUpdate: acs.attendance.workShift.requestUpdate,
      requestDelete: acs.attendance.workShift.requestDelete,
      requestUpdateStatus: acs.attendance.workShift.requestUpdateStatus,
      getStoreEmployees: acs.attendance.workArrange.getStoreEmployees,
      getAllStoreEmployees: acs.attendance.workArrange.getAllStoreEmployees,
      getAllJoinStoreEmployees: acs.attendance.workArrange.getAllJoinStoreEmployees,
      getStoreWorkSchedule: acs.attendance.workArrange.getStoreWorkSchedule,
      requestUpdateSchedule: acs.attendance.workArrange.requestUpdateSchedule,
      requestDeleteSchedule: acs.attendance.workArrange.requestDeleteSchedule,
      saveModifyTableCellKey: acs.attendance.workArrange.saveModifyTableCellKey,
      getWorkScheduleTime: acs.attendance.workArrange.getWorkScheduleTime,
      getEmployeeWorkSchedule: acs.attendance.workArrange.getEmployeeWorkSchedule,
      changeTableByAddEmployee: acs.attendance.workArrange.changeTableByAddEmployee,
      getModifyList: acs.attendance.schedule.getModifyList,
      getUserStoreInfo: acs.user.loginWp.getUserStoreInfo,
      acsShowLoading: acs.global.http.loading.show,
      acsShowLoadingHide: acs.global.http.loading.hide,
      getEatShiftTimeRange: acs.attendance.workShift.getEatShiftTimeRange,
      getEatWorkShiftSuspendList: acs.attendance.workShift.getEatWorkShiftSuspendList,
      changeIsStoreRestPage: acs.attendance.store.rest.changeIsStoreRestPage,
    },
    dispatch
  );

Page(
  connect(mapStateToData, mapDispatchToPage)({
    ...pageConfig,
    ...tableConfig
  })
);
