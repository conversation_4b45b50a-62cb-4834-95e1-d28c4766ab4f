<view class="wp-page">
  <check-header class="wp-check__fixed" bind:storechange="onStoreChange"></check-header>
  <tab items="{{tabs}}" selected="{{tabs[0].id}}" isTop46="{{isTop46}}" bind:tabtap="onTabTap" class="fix-top-46">
    <!-- 排班设置 -->
    <view slot="{{tabs[0].id}}" class="mp-attendance__shift-container">
      <!-- <picker mode="date" value="{{[1, 2]}}" start="2015-09-01" end="2017-09-01" bindchange="bindDateChange">
      </picker> -->
      <!-- 表格的弹框显示 -->
      <view class="mp-attendance__table-workSchedule">
        <table options="{{table.options}}" data="{{table.data}}"
          bind:celltap="onCellTap"
          bind:rowtap="onRowTap"
          bind:coltap="onColTap"
          bind:pickerchange="pickerChange"
          bind:bindcolumnchange="pickerColumnChange"
        ></table>
        <view class="mp-attendance__table-workSchedule-shift" wx:if="{{tablePickerList.length > 0}}">
          <view class="mp-attendance__table-workSchedule-shift-item" wx:for="{{tablePickerList}}" wx:key="{{index}}">
            <view class="mp-attendance__table-workSchedule-shift-item-color" style="background-color: {{item.color}}">
            </view>{{item.key}}
          </view>
          <!-- 添加店休 -->
          <view class="mp-attendance__table-workSchedule-shift-item">
            <view class="mp-attendance__table-workSchedule-shift-item-color" style="background-color: #ebebeb"></view>
            店休
          </view>
        </view>
        <view class="mp-attendance__table-workSchedule-shift" wx:else>
          当前还没设置任何班次
        </view>
      </view>

      <!-- 弹框实现，考虑到本弹框业务性很强，故不将本弹框封装成组件 - start -->
      <!-- mask -->
      <view class="mp-attendance__drawer">
        <view class="mp-attendance__drawer-screen" data-status="close" wx:if="{{showModalStatus}}" bindtap="powerDrawer" data-status="close"></view>
        <!-- content -->
        <view class="mp-attendance__drawer-box" wx:if="{{showModalStatus}}" animation="{{animationData}}">
          <view class="mp-attendance__drawer-title">添加店员</view>
          <view class="mp-attendance__drawer-content">
            <!-- 搜索下拉框 - start -->
            <view class="page__bd">
              <view class="weui-search-bar mp-attendance__drawer-seach-bar">
                <view class="weui-search-bar__form mp-attendance__drawer-form">
                  <view class="weui-search-bar__box">
                    <icon class="weui-icon-search_in-box" type="search" size="14"></icon>
                    <input type="text" class="weui-search-bar__input" placeholder="门店/店员" value="{{inputVal}}" focus="{{inputShowed}}" bindinput="inputTyping" />
                    <view class="weui-icon-clear" wx:if="{{inputVal.length > 0}}" bindtap="clearInput">
                      <icon type="clear" size="14"></icon>
                    </view>
                  </view>
                  <label class="weui-search-bar__label" hidden="{{inputShowed}}" bindtap="showInput">
                    <icon class="weui-icon-search" type="search" size="14"></icon>
                    <view class="weui-search-bar__text">门店/店员</view>
                  </label>
                </view>
              </view>
              <view class="weui-cells searchbar-result mp-attendance__drawer-cells" wx:if="{{(inputVal.length > 0) && showList}}">
                <view class="mp-attendance__drawer-cell mp-attendance__drawer-cell-title"
                wx:if="{{storeList.length>0}}">门店</view>
                <view class="mp-attendance__drawer-cell" hover-class="mp-attendance__drawer-cell--active" wx:for="{{storeList}}" wx:key="{{index}}">
                  <view class="weui-cell__bd">
                    <view bindtap="selectItemTap"
                    data-storeId="{{storeId}}"
                    data-value="{{item.storeName}}"
                    data-item="{{item.employees}}">{{item.storeName}}</view>
                  </view>
                </view>
                <view class="mp-attendance__drawer-cell  mp-attendance__drawer-cell-title"
                wx:if="{{employeeList.length>0}}">店员</view>
                <view class="mp-attendance__drawer-cell" hover-class="mp-attendance__drawer-cell--active" wx:for="{{employeeList}}" wx:key="{{index}}">
                  <view class="weui-cell__bd">
                    <view bindtap="selectItemTap"
                    data-item="{{item}}"
                    data-value="{{item.storeEmployee}}">{{item.storeEmployee}}</view>
                  </view>
                </view>
                <view class="mp-attendance__drawer-cell mp-attendance__drawer-cell-title" wx:if="{{(storeList.length===0) && (employeeList.length===0)}}">
                  未搜索到相关门店和店员，请重新输入
                </view>
              </view>
              <view class="mp-attendance__drawer-panel-container" wx:if="{{showPanel}}">
                <view class="mp-attendance__drawer-panel-title">选择店员</view>
                <view class="mp-attendance__drawer-panel-body">
                  <view wx:for="{{employeePanelData}}"
                  wx:key="{{index}}" class="mp-attendance__drawer-panel-item-container" bindtap="onTapChangeStatus">
                    <view class="mp-attendance__drawer-panel-item">
                      <view class="mp-attendance__drawer-panel-item-top" data-item="{{item}}" data-id="{{item.userId}}" bindtap="bindCheckedTap">
                        <image class="mp-attendance__drawer-panel-item-top-image {{item.userId === employeeCheckedId ? 'mp-attendance__drawer-panel-item-top-image--selected' : ''}}" src="../../../images/check/notice.png"
                        wx:if="{{item.avatar && (item.avatar.length)>0}}" lazy-load="false"></image>
                        <image class="mp-attendance__drawer-panel-item-top-image {{item.userId === employeeCheckedId ? 'mp-attendance__drawer-panel-item-top-image--selected' : ''}}"
                        src="../../../images/check/logo.png"
                        wx:else
                        lazy-load="false"></image>
                        <!-- <view class="mp-attendance__drawer-panel-item-top-type">班</view> -->
                        <!-- <view class="mp-attendance__drawer-panel-item-top-type">休</view> -->
                      </view>
                      <view class="mp-attendance__drawer-panel-item-bottom-text">{{item.storeEmployee}}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 搜索下拉框 - end -->
          </view>
          <button class="mp-attendance__drawer-btn--ok bc-p color-white" bindtap="powerDrawerSubmit" data-status="close" disabled="{{employeeCheckedId ===''}}">
            确定
          </button>
        </view>
      </view>
      <!-- 弹框 - end  -->
      <!-- <view class="mp-attendance__shift-add" bindtap="powerDrawer" data-status="open" wx:if="{{storeLeader === 1 && businessModel === 'DIRECT'}}"> -->
      <view class="mp-attendance__shift-add" bindtap="powerDrawer" data-status="open" wx:if="{{storeLeader === 1 }}">
        <image class="mp-attendance__shift-add-icon" src="../../../images/attendance/add.png">
        </image>
      </view>
    </view>
    <!-- 班次设置 -->
    <view slot="{{tabs[1].id}}" class="mp-attendance__plan-work-container" >
      <!-- 新建班次 -->
      <view class="mp-attendance__plan-work-detail" wx:if="{{showDetail}}">
        <view class="mp-attendance__plan-work-detail-header">{{detailTitle}}</view>
        <view class="mp-attendance__plan-work-detail-body">
          <view class="mp-attendance__plan-work-detail-plan-name">
            班次名称
            <input class="mp-attendance__plan-work-detail-plan-name-input bd-1" bindinput='bindInputTap' value="{{shift.name}}" />
          </view>
          <!-- 时间日期选择 -->
          <view class="mp-attendance__plan-work-detail-plan-time">
            <view class="mp-attendance__plan-work-detail-plan-time-start">
              <picker bindchange="bindPickerChange" data-type="start" value="{{index}}" range="{{timeLags}}">
                <view class="bd-1 mp-attendance__plan-work-detail-plan-time-start-input">{{shift.start}}</view>
              </picker>
              <!-- <mp-picker range="{{timeLags}}" value="{{shift.start}}" bindfinish="finishStartHandler"></mp-picker> -->
            </view>
            <view class="mp-attendance__plan-work-detail-plan-time-strigula">></view>
            <view class="mp-attendance__plan-work-detail-plan-time-end">
              <picker bindchange="bindPickerChange" data-type="end" value="{{index}}" range="{{timeLags}}">
                <view class="bd-1 mp-attendance__plan-work-detail-plan-time-end-input">{{shift.end}}</view>
              </picker>
              <!-- <mp-picker range="{{timeLags}}" value="{{shift.end}}" bindfinish="finishEndHandler"></mp-picker> -->
            </view>
          </view>
          <!-- 设置吃饭时间 -->
          <view class="mp-attendance__plan-work-detail-plan-eat" bindtap="onSelectedEatTimeTap">
             <radio class="mp-attendance__plan-work-detail-plan-radio" checked="{{isChecked}}" color="#23B9DE"></radio>
             设置吃饭时间
          </view>
          <view class="mp-attendance__plan-work-detail-plan-show" wx:if="{{isChecked}}">
            <view class="mp-attendance__plan-work-detail-plan-time" wx:for="{{shift.workShiftSuspendList}}" wx:key="start" wx:if="{{shift.workShiftSuspendList && shift.workShiftSuspendList.length > 0}}">
              <view class="mp-attendance__plan-work-detail-plan-time-start" data-index="{{index}}" bindtap="onSetCurrentEatTime">
                <picker bindchange="bindEatPickerChange" data-type="start" value="{{index}}" range="{{eatTimeLags}}">
                  <view class="bd-1 mp-attendance__plan-work-detail-plan-time-start-input">{{item.start}}</view>
                </picker>
              </view>
              <view class="mp-attendance__plan-work-detail-plan-time-strigula">></view>
              <view class="mp-attendance__plan-work-detail-plan-time-end">
                <picker bindchange="bindEatPickerChange" data-type="end" value="{{index}}" range="{{eatTimeLags}}">
                  <view class="bd-1 mp-attendance__plan-work-detail-plan-time-end-input">{{item.end}}</view>
                </picker>
              </view>
               <view class="mp-attendance__plan-work-detail-plan-cancel" data-index="{{index}}" bindtap="onCancelEatTime">
                  <text class="PETKIT icon-error mp-attendance__plan-work-detail-plan-cancel-icon"></text>
                </view>
            </view>
             <view class="mp-attendance__plan-work-detail-plan-message-disable" wx:if="{{shift.workShiftSuspendList && shift.workShiftSuspendList.length === 2}}">+ 至多添加2次吃饭时间</view>
             <view class="mp-attendance__plan-work-detail-plan-message" bindtap="onGetEatTime" wx:if="{{shift.workShiftSuspendList && shift.workShiftSuspendList.length < 2 && timeDiff >= 8}}">+ 至多添加2次吃饭时间</view>
          </view>
          <view class="mp-attendance_plan-work-detail-color">
            <view class="mp-selec-color__wrapper">
              <view class="mp-select-color__container">
                <view class="mp-select-color__header">选择颜色</view>
                <view class="mp-select-color__body">
                  <view wx:for="{{colors}}" wx:key="{{index}}" class="mp-select-color__item-container">
                    <view class="mp-select-color__item" style="background-color: {{item.value}};" bindtap="onTapChangeStatus" data-value="{{item.value}}" data-used="{{item.used}}" class="mp-select-color__item {{item.used ? 'mp-select-color__item--used' : ''}} {{_action === item.value ? 'mp-select-color__item--active' : ''}} "></view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="mp-attendance__plan-work-detail-footer">
            <button class="bc-d color-primary mp-attendance__plan-work-detail-btn--cancel" bindtap="onCancelTap">
              取消
            </button>
            <!-- 当是休息的时候不能修改，保存 -->
            <!-- disabled="{{shift.name === '休息'}}" -->
            <button class="bc-p color-white mp-attendance__plan-work-detail-btn--submit"
            bindtap="onSubmitTap">
              确定
            </button>
          </view>
        </view>
      </view>
      <view class="mp-attendance__plan__container">

      <block wx:if="{{!showSettingWork}}">
        <!-- 班次设置 -->
        <view class="mp-attendance__plan__container-seting">
          <view class="mp-attendance__plan__container-seting-all" bindtap='setingWork'>
            <view class="mp-attendance__plan__container-seting-all-icon PETKIT icon-schedule"></view>
            <text class="mp-attendance__plan__container-seting-all-text">班次设置</text>
          </view>
        </view>
        <!-- 店休设置 -->
        <view class="mp-attendance__plan__container-seting">
          <view class="mp-attendance__plan__container-seting-all" bindtap='setingRest'>
            <view class="mp-attendance__plan__container-seting-all-icon PETKIT icon-store-break"></view>
            <text class="mp-attendance__plan__container-seting-all-text">店休设置</text>
          </view>
        </view>
      </block>
    </view>



      <!-- 班次列表 -->
      <block wx:if="{{showSettingWork}}">
        <view class="mp-attendance__plan-work-list">
          <view class="mp-attendance__plan-work-list-header">选择班次</view>
          <view class="mp-attendance__plan-work-list-body">
            <!-- 列表项 -->
            <view class="weui-cell" wx:for="{{list}}" wx:key="value">
              <view class="mp-attendance__plan-work-list-circle" data-value="{{item.id}}" data-name="{{item.name}}" bindtap="onTapCheckboxChange" wx:if="{{!item.usable}}"></view>
              <view class="mp-attendance__plan-work-list-success" data-value="{{item.id}}" data-name="{{item.name}}" bindtap="onTapCheckboxChange" wx:if="{{item.usable}}">
                <image class="mp-attendance__plan-work-image" src="../../../images/attendance/selected.png"></image>
              </view>
              <view class="weui-cell__bd">{{item.showValue}}</view>
              <view class="mp-attendance__plan-work-list-edit" wx:if="{{!showDetail && item.name !== '休息'}}" data-value="{{item.id}}" bindtap="onTapEdit" size="23" >
                <image class="mp-attendance__plan-work-image" src="../../../images/attendance/edit.png"></image>
              </view>
              <view class="mp-attendance__plan-work-list-delete"
              wx:if="{{!showDetail && item.name !== '休息'}}"
              data-value="{{item.id}}" bindtap="onTapDelete" size="23">
                <image class="mp-attendance__plan-work-image" src="../../../images/attendance/delete.png"></image>
              </view>
            </view>
          </view>
        </view>
        <button class="mp-attendance__plan-work-list-button" bind:tap="onFinishPlanTap">完成</button>
        <view bindtap="onTapAdd" class="mp-attendance__plan-work-add" wx:if="{{!showDetail}}">
          <image class="mp-attendance__plan-work-add-icon" src="../../../images/attendance/add.png"></image>
        </view>
      </block>
    </view>
  </tab>
</view>
