@import 'styles/app/attendance';
@mixin mp-attendance__add {
  position: fixed;
  bottom: u(45);
  right: u(30);
  width: u(100);
  height: u(100);
  border-radius: 50%;
  // background-color: #23b9de;
  // color: #fff;
  // font-size: u(80);
  // text-align: center;
  // line-height: u(100);
  overflow: hidden;
}

@mixin mp-attendance-plan-theme($theme) {
  // 排班设置
  .mp-attendance__shift {
    &-container {
      // position: relative;
      border-bottom: u(2) solid #ebebeb;
      height: 100%;
    }
    &-add {
      @include mp-attendance__add;
      z-index: 999;
      &-icon {
        width: 100%;
        height: 100%;
      }
    }
  } // 班次设置
  .mp-attendance__plan {
    &__container{
      display:flex;
      justify-content: space-between;
      font-size:u(32);
      &-seting{
        width:u(340);
        height:u(240);
        &-all{
          display: flex;
          flex-direction:column;
          align-items: center;
          border-radius:u(8); 

          background: #fff;
         
          &-icon {
            color: #23B9DE;
            font-size: 40px;
            line-height: 40px;
            width:u(82);
            height:u(76);
            margin-top: u(56);
            margin-bottom: u(22);
          }
          &-text{
        
            font-size:u(32);
            margin-bottom: u(48);
          }
        }
         
      }
     
    }
    &-work {
      &-image {
        width: 100%;
        height: 100%;
      }
      &-add {
        @include mp-attendance__add;
        &-icon {
          width: 100%;
          height: 100%;
        }
      }
      &-container {
        flex-grow: 1;
        margin: u(24); // background-color: white;
        position: relative;
        .bd-1 {
          border-bottom: u(2) solid #E3E3E3;
        }
      }
      &-list {
        background-color: white;
        border-radius: u(8);
        padding: u(30);
        &-header {
          font-size: u(32);
        }
        &-circle {
          width: u(42);
          height: u(42);
          border: 1px solid #D4D4D4;
          border-radius: 50%;
          margin-right: u(16);
          box-sizing: border-box;
        }
        &-success {
          width: u(42);
          height: u(42);
          margin-right: u(16);
          box-sizing: border-box;
        }
        &-edit {
          width: u(42);
          height: u(42);
          margin-right: u(70);
          box-sizing: border-box;
        }
        &-delete {
          width: u(36);
          height: u(42);
        }

        &-button {
          margin-top: u(30);
          background-color: #23B9DE;
          color: #fff;
        }
      }
      &-detail {
        background-color: white;
        border-radius: u(8);
        padding: u(30);
        margin-bottom: u(24);
        &-header {
          font-size: u(32);
        }
        &-plan-name {
          color: #9397A2;
          font-size: u(20);
          margin-top: u(26);
          &-input {
            color: #000;
            font-size: u(28);
          }
        }
        &-color {
          box-sizing: border-box;
        } // 班次时间选择
        &-plan-time {
          display: flex;
          margin: u(40) 0;
          &-strigula {
            font-size: u(32);
            color: #9397A2;
            margin: 0 u(24);
          }
          &-start,
          &-end {
            flex: 1;
          }
          &-start-input,
          &-end-input {
            text-indent: 1em;
            color: #2F2F2F;
            font-size: u(28);
            height: u(40);
          }
        }

        &-plan-eat {
          width: u(240);
          font-size: u(28);
          color: #575D6A;
          margin-top: u(48);
        }

        &-plan-radio {
          transform: scale(0.7);
          color:#23B9DE;
        }
        &-plan-message {
          width: 100%;
          height: u(64);
          color: #23B9DE;
          border: 1px solid #23B9DE;
          font-size: u(28);
          text-align: center;
          border-radius: u(8);
          margin: u(24) 0;
          line-height: u(64);

          &-disable {
            width: 100%;
            height: u(64);
            font-size: u(28);
            text-align: center;
            border-radius: u(8);
            margin: u(24) 0;
            line-height: u(64);
            color: #B0B0B0;
            border: 1px solid #B0B0B0;
          }
        }
        &-plan-cancel {
          width: u(32);
          height: u(32);

          &-icon {
            opacity: 0.5;
            margin-left: u(12);
          }
        }
        &-btn {
          margin-top: u(64);
          &--cancel {
            flex: 1;
            margin-right: u(18);
            border: 1px solid #23B9DE;
            color: #23B9DE;
            background-color: #fff;
            box-sizing: border-box;
            &::after {
              border: none;
            }
          }
          &--submit {
            flex: 1;
          }
        }
        &-footer {
          display: flex;
        }
      }
    }
  } // 排班表格
  .mp-attendance__table {
    .top-left-header {
      border-top: 100px threedlightshadow solid;
      border-left: 120px windowframe solid;
      width: 0;
      height: 0;
      position: relative;
      color: #FFF;
      span {
        display: block;
        width: 40px
      }
      &-name {
        position: absolute;
        top: -70px;
        left: -44px;
        color: black;
      }
      &-time {
        position: absolute;
        bottom: 8px;
        right: 55px;
      }
    }
  } // 选择颜色
  .mp-select-color {
    &__header {
      color: #9397A2;
      font-size: 28rpx;
      // margin: 24rpx 30rpx;
      margin: u(24) 0;
    }
    &__body {
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
    }
    &__item-container {
      width: 20%;
    }
    &__item {
      width: 46px;
      height: 46px;
      border-radius: 50%;
      margin-bottom: 48rpx;
      position: relative;
      box-sizing: border-box;
    }
    &__item--used::after {
      box-sizing: border-box;
      display: block;
      content: "已选";
      color: #fff;
      background-color: #000;
      opacity: .5;
      position: absolute;
      height: 46px;
      width: 46px;
      line-height: 46px;
      text-align: center;
      font-size: 14px;
      border-radius: 50%;
    }
    &__item--active {
      border: 2px solid #23B9DE !important;
    }
  } // 弹框
  .mp-attendance__drawer {
    /*---- 弹框基础样式 - start -----*/
    &-screen {
      width: 100%;
      height: 100%;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
      background: #000;
      opacity: 0.5;
      overflow: hidden;
    }
    &-box {
      width: u(650);
      padding: u(20);
      overflow: hidden;
      position: fixed;
      top: 50%;
      left: 0;
      z-index: 1001;
      background: #fff;
      margin: -420rpx u(50) 0 u(50);
      box-sizing: border-box;
      border-radius: u(10);
    }
    &-title {
      padding: u(30);
      font-size: u(32);
      font-weight: bold;
      text-align: center;
    }
    &-content {
      height: u(500);
      overflow-y: scroll;
      /*超出父盒子高度可滚动*/
    }
    &-btn--ok {
      margin: 0 auto;
      box-sizing: border-box;
      text-align: center;
    }
    /*---- 弹框基础样式 - end -----*/
    &-seach-bar {
      border: none;
      background-color: #fff;
    }
    &-form {
      border: none;
      border-bottom: 1px solid #E3E3E3;
      border-radius: 0;
    }
    &-cells {
      margin-top: 0;
    }
    &-cell {
      height: u(64);
      line-height: u(64);
      font-size: u(28);
      text-indent: u(20);
      color: #575D6A;
      &-title {
        font-size: u(24);
        color: #9397A2;
        height: u(50);
        line-height: u(50);
      }
    }
    &-cell--active {
      background-color: #23B9DE;
      color: #fff;
    } // 弹框选择panel
    &-panel {
      &-container {
        background-color: #fff;
      }
      &-title {
        color: #9397A2;
        font-size: u(28);
      }
      &-body {
        display: flex;
        flex-wrap: wrap;
        box-sizing: border-box;
      }
      &-item-container {
        width: 25%;
      }
      &-item {
        margin: 0 auto;
        &-top {
          position: relative;
          width: u(90);
          height: u(90);
          overflow: hidden;
          margin: u(20) auto 0;
        }
        &-top-image {
          border-radius: 50%;
          width: 100%;
          height: 100%;
          margin: 0 auto;
          box-sizing: border-box;
          border: 1px solid #EBEBEB;
          &--selected {
            border: 2px solid #23B9DE;
          }
        }
        &-top-type {
          color: #fff;
          background-color: #23B9DE;
          width: u(32);
          height: u(32);
          text-align: center;
          line-height: u(32);
          position: absolute;
          z-index: 9;
          right: 0;
          top: 0;
          font-size: u(20);
          border-radius: 50%;
        }
        &-bottom-text {
          text-align: center;
        }
        &-selected::after {
          border: 2px solid #23B9DE !important;
        }
      }
    }
  } // 表格
  .mp-attendance__table {
    &-workSchedule {
      height: 100%;

      table {
        display: block;
        width: 100%;
        // height: calc(100% - #{u(44)});
      }

      &-shift {
        width: 100%;
        height: u(48);
        line-height: u(48);
        background-color: #FAFBFC;
        border-top: u(2) solid #EBEBEB;
        position: absolute;
        bottom: 0;
        z-index: 99;
        text-indent: 1em;
        display: flex;
        flex-wrap: no-wrap;
        box-sizing: border-box;
        padding-left: u(20);
        overflow-x: auto;
        &-item {
          color: #9397A2;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          &-color {
            width: u(20);
            height: u(20);
            border-radius: 50%;
            margin-right: u(10);
          }
        }
      }
    }
  }
}

@include mp-app-attendance-theme($app-theme);
@include mp-attendance-plan-theme($app-theme);
