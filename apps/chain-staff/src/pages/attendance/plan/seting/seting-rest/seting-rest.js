import {
    connect,
  } from 'utils/weapp-redux';
  import {
    bindActionCreators,
  } from 'redux';
  import {
    acs,
    getStorage,
    store,
  } from '@petkit/redux';
  import moment from 'moment';
  
  const pageConfig = {
    /**
     * 页面的初始数据
     */
    data: {
      month: null,
      showTag: true,
      selectedDay: 0,
      // 门店 id
      storeId: 0,
      // 是否是店休设置页面
      isStoreRest: true,
      // 选中当天的店休按钮是否开启
      isStoreRestChecked: false,
      // 当前显示月
      monthBeShowed: moment().format('M'),
      // 当前显示月对应时间戳
      timestamp: moment().valueOf(),
      // 向左按钮是否可点击
      isLeftCanClick: false,
      // 向右按钮是否可点击
      isRightCanClick: true,
      // 是否切换了月份
      isChangeMonth: false,

      selectedDay: 0,
      startTime: 0,
      endTime: 0,
      // 工作状态，区分是否是店休
      status: null,
      isChangeToggleByListen: true,
      storeRestListLength: 0,
    },
  
    onLoad(option) {
    },
  
    onShow() {
    },
  
    onReady() {
      const storeId = Number(getStorage().getItem('storeId'));
      this.setData({
        month: moment().format('YYYY-MM'),
        selectedDay: moment().valueOf(),
        storeId,
      })

      const time = moment().valueOf();
      this._requestStoreRestList(time);

      this._listenStoreRestInfoList();
    },
  
    onHide() {
      this._restSub();
    },
  
    onUnload() {
      this._restSub();
    },
  
    onDateSelected({
      detail: {
        day,
        status
      }
    }) {
      const startTime = moment(day).startOf('day').valueOf();
      const endTime = moment(day).endOf('day').valueOf();

      this.setData({
        startTime,
        endTime,
        status: status || null,
        isStoreRestChecked: status === 'REST',
        selectedDay: day,
      });
    },

    onConfirmStoreRestSwitch({
      detail: {
        value: isStoreRestChecked
      }
    }) {
      const storeId = this.data.storeId;
      const startTime = this.data.startTime;
      const endTime = this.data.endTime;

      const time = this.data.selectedDay
      const dateToChange = this.data.storeRestInfoList.find(info => info.time === time);

      // 该日若从未被设为店休，则算作新增，不然算作修改
      if (dateToChange) {
        this.requestChangeStoreRest({
          storeId,
          startTime,
          endTime,
          id: dateToChange.id,
          offStatus: dateToChange.offStatus === 1 ? 0 : 1,
        }, {
          success: () => {
            this._requestStoreRestList(time);
          },
          failure: () => {
            this.setData({
              isStoreRestChecked: !isStoreRestChecked,
            });
          }
        })
      } else {
        this.requestAddStoreRest({
          storeId,
          startTime,
          endTime,
          offStatus: 1,
        }, {
          success: () => {
            this._requestStoreRestList(time);
          },
          failure: () => {
            this.setData({
              isStoreRestChecked: !isStoreRestChecked,
            });
          }
        })
      }

      this.setData({
        isStoreRestChecked
      });
    },

    onShowDifferentMonthTap({
      currentTarget: {
        dataset: {
          type
        }
      }
    }) {
      switch(type) {
        case 'next':
          if (this.data.isRightCanClick) {
            const timestamp = moment(this.data.timestamp).add(1, 'months').valueOf();
            this.setData({
              timestamp,
              monthBeShowed: moment(timestamp).format('M'),
              isLeftCanClick: true,
              isRightCanClick: false,
              isChangeMonth: true,

              month: moment(timestamp).format('YYYY-MM'),
              selectedDay: 0,

              isChangeToggleByListen: true,
            })

            this._requestStoreRestList(timestamp);

          }
          break;

        case 'back':
          if (this.data.isLeftCanClick) {
            const timestamp = moment().valueOf();
            this.setData({
              timestamp,
              monthBeShowed: moment().format('M'),
              isLeftCanClick: false,
              isRightCanClick: true,
              isChangeMonth: true,

              month: moment().format('YYYY-MM'),
              selectedDay: moment().valueOf(),

              isChangeToggleByListen: true,
            })

            this._requestStoreRestList(timestamp);
          }
          break;
      }
    },

    _requestStoreRestList(time) {
      const startTime = moment(time).startOf('month').valueOf();
      const endTime = moment(time).endOf('month').valueOf();
      this.requestStoreRestList({
        storeId: this.data.storeId,
        startTime,
        endTime,
      });
    },

    _listenStoreRestInfoList() {
      this.data.sub = store.subscribe(() => {
        const storeRestList = store.getState().attendance.store.rest.storeRestInfoList;
        const selectedDay = this.data.selectedDay;
        
        const isSelectedDayRest = 
          storeRestList.
            filter(info => info.offStatus === 1).
            some(item => moment(item.time).isSame(selectedDay));

        if (this.data.isChangeToggleByListen) {
          this.setData({
            isStoreRestChecked: isSelectedDayRest,
          })
        }

        if (storeRestList.length !== this.data.storeRestListLength) {
          this.setData({
            isChangeToggleByListen: false,
            storeRestListLength: storeRestList.length,
          })
        }
      })
    },

    _restSub() {
      if (this.data.sub) {
        this.data.sub();
      }
    },

  };
  
  const mapStateToData = state => ({
    storeRestInfoList: state.attendance.store.rest.storeRestInfoList,
  });
  
  const mapDispatchToPage = dispatch => bindActionCreators({
    showToast: acs.global.toast.show,
    requestStoreRestList: acs.attendance.store.rest.requestStoreRestList,
    requestAddStoreRest: acs.attendance.store.rest.requestAddStoreRest,
    requestChangeStoreRest: acs.attendance.store.rest.requestChangeStoreRest,
  }, dispatch);
  
  Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
  