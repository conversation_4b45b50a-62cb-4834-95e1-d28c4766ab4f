@import 'styles/app/attendance';

$primary-color: #23b9de;
// $accent-color: #
$primary-content-color: #9397a2;

@mixin mp-attendance-month-calendar-theme($theme) {
  .mp-attendance__month-calendar {
    &-calendar {
      flex-shrink: 1;
      // height: u(570);
      margin: 0 u(20) 0;
      border-radius: u(8);
      background-color: white;
    }

    &-seting-rest {
      display: flex;
      flex-shrink: 1;
      justify-content: space-between;
      align-items: center;

      margin: u(24) u(20);
      padding: u(24);
      border-radius: u(8);
      font-size: u(28);
      color: #2f2f2f;
      background: #fff;

      &-text {
        margin-top: u(8);
      }

      &-open {
        display: flex;
        align-items: center;
        
        height: u(28);
        
        color:#23B9DE;

        span {
          font-size:u(28);
        }
         
      }

      &-switch {
        transform: scale(.7);
      }
    }

    &-prompt {
      flex-shrink: 1;

      margin: 0 u(20) u(20);
      padding: u(32) u(24);
      border-radius: u(8);

      font-size: u(28);
      background:#fff;

      &-title {
        margin-bottom: u(20);

        color:#fb9e0f;
      }
      &-tips {
        line-height: u(44);
        color:#575d6a;
      }
    }

    &-month {
      display: flex;
      justify-content: center;
      align-items: center;

      margin: u(24) u(20) u(2);
      border-radius: u(8);

      font-size: u(40);
      color: #2f2f2f;
      background-color: #fff;

      &-arrow {
        margin: u(16) u(36);

        &--inactive {
          color: #d4d4d4;
        }
      }

      &-text {
        font-size: u(36);
      }
    }
  }
}

@include mp-app-attendance-theme($app-theme);
@include mp-attendance-month-calendar-theme($app-theme);

