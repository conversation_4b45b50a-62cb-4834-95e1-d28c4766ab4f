<view class="wp-page mp-attendance__container mp-attendance__month-calendar-container">

  <view class="mp-attendance__month-calendar-month">
    <view class="mp-attendance__month-calendar-month-icon" data-type="back" bind:tap="onShowDifferentMonthTap">
      <view class="PETKIT icon-back mp-attendance__month-calendar-month-arrow {{isLeftCanClick ? '' : 'mp-attendance__month-calendar-month-arrow--inactive'}}">
      </view>
    </view>
    {{monthBeShowed}}<span class="mp-attendance__month-calendar-month-text">月</span>
    <view class="mp-attendance__month-calendar-month-icon" data-type="next" bind:tap="onShowDifferentMonthTap">
      <view class="PETKIT icon-right-arrow mp-attendance__month-calendar-month-arrow {{isRightCanClick ? '' : 'mp-attendance__month-calendar-month-arrow--inactive'}}">
      </view>
    </view>
  </view>

  <view class="mp-attendance__month-calendar-calendar">
    <!-- 日历 -->
    <calendar
      month="{{month}}"
      show-tag="{{showTag}}"
      need-tag-items="{{storeRestInfoList}}"
      is-store-rest="{{isStoreRest}}"
      is-change-month="{{isChangeMonth}}"
      selected-day="{{selectedDay}}"
      bind:dateSelected="onDateSelected">
    </calendar>
  </view>

  <view class="mp-attendance__month-calendar-seting-rest">
    <!-- 设置店休 -->
    <view class="mp-attendance__month-calendar-seting-rest-text">设置为店休</view> 
    <view class="mp-attendance__month-calendar-seting-rest-open">
      <switch checked="{{isStoreRestChecked}}" color="#21acce" class="mp-attendance__month-calendar-seting-rest-switch" bindchange="onConfirmStoreRestSwitch"/>
      <span>{{isStoreRestChecked ? '已开启' : '未开启'}}</span>
    </view>
    
    
  </view>

  <view class="mp-attendance__month-calendar-prompt">
    <!-- 温馨提示 -->
    <view class="mp-attendance__month-calendar-prompt-title">温馨提示</view>
    <view class="mp-attendance__month-calendar-prompt-tips">
      <view>1.存在预约单的日期不能设置店休，请优先检查洗护预约安排</view>
      <view>2.请店长提前设置店休,以免提早被预约上</view>
      <view>3.店休不影响排休息、非本店排班的员工</view>
    </view>
  </view>

</view>
