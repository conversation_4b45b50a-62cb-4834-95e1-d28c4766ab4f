@import 'config/theme.config';
@import 'styles/component/modal';

@mixin mp-attendance-schedule-theme($theme) {
  .mp-attendance__schedule {
    &-container {
      border-bottom: u(2) solid #ebebeb;
    }

    &-body {
    }

    &-card {
      margin: u(24);
      background-color: white;
      border-radius: u(8);
      position: relative;
      padding: u(24);
      box-sizing: border-box;

      &-modify-icon {
        width: u(90);
        height: u(90);
        position: absolute;
        right: 0;
        top: 0;

        &-image {
          width: 100%;
          height: 100%;
        }
      }

      &-punch {
        &-header {
          display: flex;
          align-items:flex-end;
        }

        &-name {
          color: #2F2F2F;
          font-size: u(32);
          font-weight: bold;
          margin-right: u(16);
          justify-content: left;
        }

        &-day {
          color: #9397A2;
          font-size: u(28);
        }

        &-time {
          color: #2F2F2F;
          font-size: u(28);
        }

        &-status {
          display: inline-block;
          color: #ffffff;
          font-size: u(24);
          border-radius: u(16);
          height: u(32);
          line-height: u(32);
          padding: 0 .5em;
          margin-top: u(12);

          &-normal {
            background-color: #23B9DE;
          }

          &-abnormal {
            background-color: #FB9E0F;
          }

          &-blank {
            background-color: #fff;
          }
        }

        &-modify {
          margin-top: u(20);
          font-size: u(28);

          &-content {
            color: #23B9DE;
            word-break:keep-all;
            white-space:nowrap;
            overflow:hidden;
            text-overflow:ellipsis;
          }

          &-time {
            color: #B0B0B0;
          }
        }
      }
    }


    &-modify {
      &-modal {
        background-color: white;
        width: u(578);
        border-radius: u(8);
        padding: u(24);

        &-header {
        }

        &-name {
          display: inline-block;

          font-size: u(32);
        }

        &-warning {
          display: inline-block;

          color: white;
          background-color: #fb9e0f;

          font-size: u(22);
          line-height: u(32);

          text-align: center;
          padding: 0 u(16);

          border-radius: u(16);
          margin-left: u(16);
        }

        &-status {
          margin-top: u(32);
          border-bottom: 1px solid #e3e3e3;
        }

        &-time {
          margin-top: u(32);
          display: flex;
          align-items: center;
        }

        &-start {
          flex-grow: 1;
          border-bottom: 1px solid #e3e3e3;
        }

        &-arrow-right {
          width: u(16);
          height: u(32);
          margin: 0 u(24);
        }

        &-end {
          flex-grow: 1;
          border-bottom: 1px solid #e3e3e3;
        }

        &-save {
          margin-top: u(32);

          background-color: #23b9de;
          border-radius: u(12);

          color: white;
          height: u(84);
          line-height: u(84);
          font-size: u(32);
          text-align: center;
        }
      }

      &-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: stretch;
      }

      &-date {
        &-container {
          height: u(96);
          background-color: white;
          border-bottom: 1px solid #ebebeb;
          position: relative;
          z-index: 1;

          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      &-table {
        &-container {
          flex-grow: 1;
          position: relative;
        }
      }
    }

    &-picker-date {
      width: 100%;
      height: 100%;
    }
  }
}

@include mp-attendance-schedule-theme($app-theme);
@include mp-modal-theme($app-theme);

