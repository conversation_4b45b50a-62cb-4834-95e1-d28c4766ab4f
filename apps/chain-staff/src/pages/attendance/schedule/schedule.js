import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  CONSTANT,
  ModifyInfo,
  getStorage,
} from '@petkit/redux';
import moment from 'moment';
import {map, cloneDeep} from 'lodash-es';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    // 当前所在门店
    storeId: null,
    tabs: [{
      id: 0,
      text: '异常列表',
    }, {
      id: 1,
      text: '修改记录',
    }],
    modal: {
      hasBackdrop: true,
      hidden: true,
    },
    pickerDate: {
      value: null,
      displayFormat: 'MM.DD'
    },
    pickerSelect: {
      status: [],
      statusKey: '',
      start: [],
      startKey: '',
      end: [],
      endKey: '',
    },
    modalData: null,
    table: {
      options: {
        def: {
          row: '时间',
          col: '姓名',
        },
        rows: [],
        cols: [],
        cell: {
          rowStyle: 'align-items: flex-start'
        },
      },
      data: [],
    },
    items: [],
  },
  onTabTap({
    detail: cell
  }) {
  },
  onCellTap(ev) {
    if (ev.detail && ev.detail.editable && this.data.storeLeader) {
      const managerId = this.data.userId;
      const userId = ev.detail.userId;
      const date = moment(ev.detail.cellStart).format('YYYY-MM-DD');
      const time = ev.detail.time;
      const modifyId = ev.detail.modifyId;
      const scheduleId = ev.detail.scheduleId;
      this.getModifyInfo({managerId, userId, date, time, modifyId});

      const {
        modal,
      } = this.data;
      modal.hidden = false;

      this.setData({
        modal,
      });
    }
  },
  onPickerDateChange({
    detail,
  }) {
    if (detail !== this.data.date) {
      this.setData({
        date: detail
      });
      this._getDailyList();
    }
  },
  onPickerSelectChange({
    detail,
    currentTarget: {
      dataset: {
        type,
      }
    },
  }) {
    // 组件内部要完成数据的双向绑定，所以会触发两次
    const timeLags = this.data.timeLagList;
    const statusRange = this.data.pickerSelect.status;
    const modalData = cloneDeep(this.data.modalData);
    const index = Number(detail.value);
    const optType = detail.type;
    const timeLagStampts = timeLags.map(item => moment(this.data.date + ' ' + item).valueOf());
    let modalDataChangedValue = null;

    if (modalData && index > -1) {
      modalDataChangedValue = modalData.changeValue({type, index, optType, statusRange, timeLineList: timeLagStampts});

      if (optType === 'manual') {
        if (modalDataChangedValue.message) {
          this.showToast({
            message: modalDataChangedValue.message,
          });
        }

        modalData.startValue = modalDataChangedValue.startValue;
        modalData.endValue = modalDataChangedValue.endValue;
        modalData.statusValue = modalDataChangedValue.statusValue;
        modalData.disableStartValue = modalDataChangedValue.disableStartValue;
        modalData.disableEndValue = modalDataChangedValue.disableEndValue;
        modalData.disableStatusValue = modalDataChangedValue.disableStatusValue;
      }

      this.setData({
        modalData,
      });
    }
  },

  // modal被隐藏时触发
  onModalHidden() {
    const modalData = this.data.modalData;
    modalData.startValue = -1;
    modalData.endValue = -1;
    modalData.statusValue = -1;
    modalData.disableStartValue = false;
    modalData.disableEndValue = false;
    modalData.disableStatusValue = false;
    this.setData({
      modalData: new ModifyInfo(),
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.datetime) {
      const datetime = Number(options.datetime);
      const pickerDate = this.data.pickerDate;
      pickerDate.value = moment(datetime).format('YYYY-MM-DD');
      this.setData({
        // date: moment(datetime).format('YYYY-MM-DD'),
        pickerDate,
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 获取所有考勤状态接口
    this.getCategory();
    // 获取当天的timeline
    this.getTimeLineList();
    this.getWorkShiftAllTimeLag();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this._onSub();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    if (this.data.sub) {
      this.data.sub();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    if (this.data.sub) {
      this.data.sub();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  onSaveModified(ev) {

    const pickerSelect = this.data.pickerSelect;
    const date = this.data.date;
    const modalData = this.data.modalData;
    const startTime = date + ' ' + pickerSelect.start[modalData.startValue];
    const endTime = date + ' ' + pickerSelect.end[modalData.endValue];
    const start = moment(startTime).valueOf();
    const end = moment(endTime).valueOf();
    const category = pickerSelect.status[modalData.statusValue];
    const attendanceCategory = category ? category.value : '';
    const type = category.type;
    const userId = modalData.userId;
    const scheduleId = modalData.scheduleId;
    const managerId = this.data.userId;
    const storeId = this.data.storeId;
    let previousState = this.data.modalData.previousState;
    const id = this.data.modalData.modifyId;

    if (previousState === '正常') {
      previousState = CONSTANT.ATTENDANCE.CATEGORY_TYPES.NORMAL;
    }

    // attendanceCategory,
    // // 开始时间 00:00 - 24:00
    // start,
    // // 结束时间 00:00 - 24:00
    // end,
    // // 假种类型 1 - 考勤 2 - 请假
    // type,
    // // 当前被修改用户的userId
    // userId,
    // // 当前记录的id
    // scheduleId,
    // // 当前登陆用户的userId
    // managerId,
    // // 修改前的状态
    // previousState,

    if (!id) {
      // 不存在店长修改过的情况，所以为创建
      this.createModify({
        attendanceCategory,
        start,
        end,
        type,
        userId,
        storeId,
        scheduleId,
        managerId,
        previousState
      }, {
        success: () => {
          this._getDailyList();
        }
      });
    } else {
      // 存在店长修改过的情况，所以为修改
      this.updateModify({
        id,
        attendanceCategory,
        start,
        end,
        type,
        userId,
        storeId,
        scheduleId,
        managerId,
        previousState
      }, {
        success: () => {
          this._getDailyList();
        }
      });
    }

    const {
      modal,
    } = this.data;
    modal.hidden = true;

    this.setData({
      modal,
    });
  },

  _onSub() {
    let storeId = getStorage().getItem('storeId');
    let storeLeader = getStorage().getItem('storeLeader');
    this.setData({
      storeId: storeId,
      storeLeader: storeLeader,
    });
    this.data.sub = store.subscribe(() => {

      let times = [];
      const pickerSelect = this.data.pickerSelect;
      const table = this.data.table;
      let modalData = this.data.modalData;
      if (!modalData || (modalData.startValue === -1 && modalData.endValue === -1 && modalData.statusValue === -1)) {
        modalData = this.data.modifyInfo;
      }

      // 不显示半点
      table.options.rows = this.data.timeLagList.map(item => {
        item.indexOf('30');
        if (item.indexOf('30') !== -1) {
          return '';
        }
        return item;
      });

      table.options.cols = this.data.employeeList;
      table.data = this.data.scheduleInfoList;

      pickerSelect.status = this.data.statusRange;
      pickerSelect.statusKey = 'text';
      // times = map(this.data.timeLineList, (timeLine, _i) => ({
      //   value: _i,
      //   text: timeLine,
      // }));
      pickerSelect.start = this.data.timeLagList;
      pickerSelect.end = this.data.timeLagList;
      this.setData({
        table,
        pickerSelect,
        modalData,
      });
    });
  },

  _getDailyList() {
    // const userId = 1370;
    const userId = this.data.userId;
    const day = moment(this.data.date).valueOf();
    const storeLeader = getStorage().getItem('storeLeader');
    const storeId = getStorage().getItem('storeId');
    this.getDailyList({
      day: day,
      userId: userId,
      storeId: storeId,
      storeLeader: storeLeader,
    }, {
      success: () => {
        this._getModifyList();
      },
    });
  },

  _getModifyList() {
    const userId = this.data.userId;
    const day = moment(this.data.date).valueOf();
    const storeLeader = getStorage().getItem('storeLeader');
    const storeId = getStorage().getItem('storeId');
    this.getModifyList({
      day: day,
      userId: userId,
      storeId: storeId,
      storeLeader: storeLeader,
    });
  }

};

const mapStateToData = state => {
  const choosedEatTime = state.attendance.schedule.choosedEatTime;
  return {
    userId: state.user.loginWp.userId,
    items: state.attendance.schedule.modifyList,
    timeLineList: state.attendance.schedule.timeLineList,
    timeLagList: state.attendance.workShift.timeLags,
    employeeList: state.attendance.schedule.employeeList,
    // storeLeader: state.user.loginWp.storeLeader,
    scheduleInfoList: state.attendance.schedule.scheduleInfoList,
    statusRange: state.attendance.schedule.statusRange.filter(
      item => item.value !== CONSTANT.ATTENDANCE.CATEGORY_TYPES.LEGWORK && item.value !== CONSTANT.ATTENDANCE.CATEGORY_TYPES.ABSENTCARD
    ),
    modifyInfo: state.attendance.schedule.modifyInfo,
    dailyList: state.attendance.schedule.dailyList,
    choosedEatTime,
  }
};

const mapDispatchToPage = dispatch => bindActionCreators({
  showToast: acs.global.toast.show,
  getCategory: acs.attendance.schedule.getCategory,
  getDailyList: acs.attendance.schedule.getDailyList,
  postModify: acs.attendance.schedule.postModify,
  getModifyList: acs.attendance.schedule.getModifyList,
  getTimeLineList: acs.attendance.schedule.getTimeLineList,
  getModifyInfo: acs.attendance.schedule.getModifyInfo,
  createModify: acs.attendance.schedule.createModify,
  updateModify: acs.attendance.schedule.updateModify,
  changeModifyInfo: acs.attendance.schedule.changeModifyInfo,
  getWorkShiftAllTimeLag: acs.attendance.workShift.getWorkShiftAllTimeLag,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
