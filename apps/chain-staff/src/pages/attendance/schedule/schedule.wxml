<view class="wp-page mp-attendance__schedule-container">
  <modal hasBackdrop="{{modal.hasBackdrop}}" hidden="{{modal.hidden}}" bind:hide="onModalHidden">
    <view class="mp-attendance__schedule-modify-modal">
      <view class="mp-attendance__schedule-modify-modal-header">
        <view class="mp-attendance__schedule-modify-modal-name">{{modalData.name}}</view>
        <view class="mp-attendance__schedule-modify-modal-warning">{{modalData.warning}}</view>
      </view>

      <view class="mp-attendance__schedule-modify-modal-status">
        <picker-select
          value="{{modalData.statusValue}}"
          range="{{pickerSelect.status}}"
          range-key="{{pickerSelect.statusKey}}"
          placeholder="点击修改状态"
          bind:change="onPickerSelectChange"
          disabled="{{modalData.disableStatusValue}}"
          data-type="status"
        ></picker-select>
      </view>

      <view class="mp-attendance__schedule-modify-modal-time">
        <view class="mp-attendance__schedule-modify-modal-start">
          <picker-select
            value="{{modalData.startValue}}"
            range="{{pickerSelect.start}}"
            range-key="{{pickerSelect.startKey}}"
            placeholder="开始时间"
            bind:change="onPickerSelectChange"
            disabled="{{modalData.disableStartValue}}"
            data-type="start"
          ></picker-select>
        </view>
        <image class="mp-attendance__schedule-modify-modal-arrow-right" src="../../../images/action/arrow_right.svg"></image>
        <view class="mp-attendance__schedule-modify-modal-end">
          <picker-select
            value="{{modalData.endValue}}"
            range="{{pickerSelect.end}}"
            range-key="{{pickerSelect.endKey}}"
            placeholder="结束时间"
            bind:change="onPickerSelectChange"
            disabled="{{modalData.disableEndValue}}"
            data-type="end"
          ></picker-select>
        </view>
      </view>
      <view class="mp-attendance__schedule-modify-modal-save" bind:tap="onSaveModified">
        保存
      </view>
    </view>
  </modal>

  <tab items="{{tabs}}" selected="{{tabs[0].id}}" bind:tabtap="onTabTap">
    <view slot="{{tabs[0].id}}" class="mp-attendance__schedule-body mp-attendance__schedule-modify-container">
      <view class="mp-attendance__schedule-modify-date-container" bind:tap="onTapDate">
        <picker-date
          class="mp-attendance__schedule-picker-date"
          value="{{pickerDate.value}}"
          displayFormat="{{pickerDate.displayFormat}}"
          bind:change="onPickerDateChange"
        ></picker-date>
      </view>
      <view class="mp-attendance__schedule-modify-table-container">
        <table options="{{table.options}}" data="{{table.data}}" timeline="{{true}}" bind:celltap="onCellTap"></table>
      </view>
    </view>
    <view slot="{{tabs[1].id}}" class="mp-attendance__schedule-body">
      <view wx:for="{{items}}" wx:key="{{index}}" class="mp-attendance__schedule-card">
        <view class="mp-attendance__schedule-card-punch">
          <view class="mp-attendance__schedule-card-punch-header">
            <view class="mp-attendance__schedule-card-punch-name">{{item.name}}</view>
            <view class="mp-attendance__schedule-card-punch-day">{{item.punchDay}}</view>
          </view>
          <!-- <view class="mp-attendance__schedule-card-punch-time">{{item.punchTime}}</view> -->
          <view wx:if="{{item.isShowStatus && item.fromStatus === 0}}" class="mp-attendance__schedule-card-punch-status mp-attendance__schedule-card-punch-status-normal">{{item.previousStatus}}</view>
          <view wx:if="{{item.isShowStatus && item.fromStatus === 1}}" class="mp-attendance__schedule-card-punch-status mp-attendance__schedule-card-punch-status-abnormal">{{item.previousStatus}}</view>
          <view wx:if="{{!item.isShowStatus}}" class="mp-attendance__schedule-card-punch-status mp-attendance__schedule-card-punch-status-blank"></view>
        </view>
        <view class="mp-attendance__schedule-card-punch-modify">
          <view class="mp-attendance__schedule-card-punch-modify-content">
            {{item.modifyContent}}
          </view>
          <view class="mp-attendance__schedule-card-punch-modify-time">
            {{item.managerName}}
          </view>
          <view class="mp-attendance__schedule-card-punch-modify-time">{{item.modifyTime}}</view>
        </view>
        <view class="mp-attendance__schedule-card-modify-icon">
          <image class="mp-attendance__schedule-card-modify-icon-image" src="../../../images/attendance/modify.png"></image>
        </view>
      </view>
    </view>
  </tab>
</view>
