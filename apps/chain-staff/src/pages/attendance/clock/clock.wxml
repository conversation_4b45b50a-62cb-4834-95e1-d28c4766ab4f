<view class="wp-page mp-attendance__container mp-attendance__clock-container">
  <view class="mp-attendance__header">
    <image class="mp-attendance__header-img" src="https://tower.im/assets/default_avatars/jokul.jpg"></image>
    <view class="mp-attendance__header-text">
      <text class="mp-attendance__header-name">{{userName}}</text>
      <text class="mp-attendance__header-warehouse">考勤门店: {{clockInfo.schedule.storeName}}</text>
    </view>
    <view class="mp-attendance__header-date-wrapper">
      <view class="mp-attendance__header-date">
        <picker mode="date" value="{{date}}" start="{{_start}}" end="{{_end}}" bindchange="bindDateChange">
          <text class="mp-attendance__header-date-text">{{ date }}</text>
          <icon class="icon-draw"></icon>
        </picker>
      </view>
    </view>
  </view>

  <view class="mp-attendance__body">
    <!-- 内容 -->
    <view class="mp-attendance__clock__content">
      <view class="mp-attendance__clock__title-tip">
        和可靠的伙伴们一起开心的工作！
      </view>

      <view class="mp-attendance__clock__vocation-list">
        <view class="mp-attendance__clock__vocation-item" wx:for="{{clockInfo.vocations}}" wx:key="vocation" wx:for-item="vocation">
          <view class="mp-attendance__clock__vocation-item__dot"></view>
          <view class="mp-attendance__clock__vocation-item__title">{{vocation.typeName}}</view>
          <view class="mp-attendance__clock__vocation-item__time">{{vocation.startTime + ' - ' + vocation.endTime}}</view>
        </view>
      </view>

      <view class="mp-attendance__clock__onduty">
        <view class="mp-attendance__clock__onduty__container">
          <view class="mp-attendance__clock__onduty__dot {{ clockInfo.schedule.id && duty.dutyType === dutyTypes.ON_DUTY ? 'mp-attendance__clock__onduty__dot--active' : '' }}"></view>
          <view class="mp-attendance__clock__onduty__line"></view>
          <view class="mp-attendance__clock__onduty__time">
            <text wx:if="{{clockInfo.schedule.id}}">上班时间 {{clockInfo.schedule.ondutyTime}}</text>
            <text wx:if="{{!clockInfo.schedule.id}}">今日暂未排班</text>
            <view class="mp-attendance__clock__onduty__status-wrapper" wx:if="{{(clockInfo.onduty.isClocked || clockInfo.schedule.id) && duty.dutyType !== dutyTypes.ON_DUTY}}">
              <view class="mp-attendance__clock__onduty__status" wx:if="{{clockInfo.onduty.clockStatus !== clockTypes.NORMAL}}">
                <text class="tag tag-warn" wx:for="{{clockInfo.onduty.abnormalTypes}}" wx:key="at" wx:for-item="at">{{ at }}</text>
              </view>
              <view class="mp-attendance__clock__onduty__status" wx:if="{{clockInfo.onduty.clockStatus === clockTypes.NORMAL}}">
                <text class="tag tag-primary">正常</text>
              </view>
              <view class="mp-attendance__clock__onduty__modified" wx:if="{{clockInfo.onduty.hasModified}}">
                <text class="tag tag-primary">{{clockInfo.onduty.modifiedInfo}}</text>
              </view>
            </view>
          </view>
          <view wx:if="{{clockInfo.schedule.id}}">
            <view wx:if="{{ clockInfo.onduty.isClocked }}">
              <view class="mp-attendance__clock__onduty__real-time" wx:if="{{clockInfo.onduty.clockTime}}">打卡时间 {{clockInfo.onduty.clockTime}}</view>
              <view class="mp-attendance__clock__onduty__real-location" wx:if="{{clockInfo.onduty.storeId || clockInfo.onduty.locationAddress}}">
                <image class="mp-attendance__clock__onduty__real-location-icon" src="/images/attendance/clock/location.svg" />
                <text wx:if="{{clockInfo.offduty.isScheduleLocation}}">({{clockInfo.onduty.locationName}})</text>
                {{clockInfo.onduty.locationAddress}}
              </view>
            </view>
            <view
              class="mp-attendance__clock__onduty__punch-again"
              data-type="{{dutyTypes.ON_DUTY}}"
              data-clock-type="update"
              bind:tap="onClock"
              wx:if="{{clockInfo.onduty.clockStatus === clockTypes.ABNORMAL && clockInfo.schedule.id && clockInfo.onduty.isClocked && !clockInfo.onduty.isAlreadyWork && isToday}}">
              更新打卡 >
            </view>
            <view class="mp-attendance__clock__onduty__abnormal-tip" wx:if="{{clockInfo.onduty.clockStatus === clockTypes.ABNORMAL && (clockInfo.schedule.id || clockInfo.onduty.isClocked)}}">{{clockInfo.onduty.abnormalClockTip}}</view>
            <view wx:if="{{ duty.dutyType === dutyTypes.ON_DUTY && !clockInfo.onduty.isClocked }}">
              <view class="mp-attendance__clock__onduty__button-group">
                <view
                  class="mp-attendance__clock__onduty__button {{duty.clockStatus === clockTypes.NORMAL ? 'mp-attendance__clock__onduty__button--in-location' : 'mp-attendance__clock__onduty__button--out-location'}}"
                  data-type="{{dutyTypes.ON_DUTY}}"
                  bind:tap="onClock">
                  <view class="mp-attendance__clock__onduty__button-text">{{duty.dutyText}}</view>
                  <view class="mp-attendance__clock__onduty__button-time">{{clockTime}}</view>
                </view>
                <view class="mp-attendance__clock__onduty__location">
                  <icon type="success" size="14" color="rgb(136, 205,58)" wx:if="{{duty.isScheduleLocation}}" />
                  <text class="mp-attendance__clock__onduty__location-info" wx:if="{{duty.isScheduleLocation}}">已进入考勤范围: ({{duty.locationName}}){{duty.locationAddress}}</text>
                  <text class="mp-attendance__clock__onduty__location-info" wx:if="{{!duty.isScheduleLocation}}">当前位置: <text wx:if="{{duty.storeId}}">({{duty.locationName}})</text>{{duty.locationAddress}}</text>
                  <text class="mp-attendance__clock__onduty__location-again" bind:tap="goToRelocation">去重新定位</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="mp-attendance__clock__offduty">
        <view class="mp-attendance__clock__offduty__container">
          <view class="mp-attendance__clock__offduty__dot {{ clockInfo.schedule.id && duty.dutyType === dutyTypes.OFF_DUTY ? 'mp-attendance__clock__offduty__dot--active' : '' }}"></view>
          <view class="mp-attendance__clock__offduty__time">
            <text wx:if="{{clockInfo.schedule.id}}">下班时间 {{clockInfo.schedule.offdutyTime}}</text>
            <text wx:if="{{!clockInfo.schedule.id}}">今日暂未排班</text>
            <view class="mp-attendance__clock__offduty__status-wrapper" wx:if="{{(clockInfo.offduty.isClocked || clockInfo.schedule.id) && duty.dutyType !== dutyTypes.ON_DUTY && duty.dutyType !== dutyTypes.OFF_DUTY}}">
              <view class="mp-attendance__clock__offduty__status" wx:if="{{clockInfo.offduty.clockStatus !== clockTypes.NORMAL}}">
                <text class="tag tag-warn" wx:for="{{clockInfo.offduty.abnormalTypes}}" wx:key="at" wx:for-item="at">{{ at }}</text>
              </view>
              <view class="mp-attendance__clock__offduty__status" wx:if="{{clockInfo.offduty.clockStatus === clockTypes.NORMAL}}">
                <text class="tag tag-primary">正常</text>
              </view>
              <view class="mp-attendance__clock__offduty__modified" wx:if="{{clockInfo.offduty.hasModified}}">
                <text class="tag tag-primary">{{clockInfo.offduty.modifiedInfo}}</text>
              </view>
            </view>
          </view>
          <view wx:if="{{duty.dutyType !== dutyTypes.ON_DUTY && clockInfo.schedule.id}}">
            <view wx:if="{{ clockInfo.offduty.isClocked}}">
              <view class="mp-attendance__clock__offduty__real-time" wx:if="{{clockInfo.offduty.clockTime}}">打卡时间 {{clockInfo.offduty.clockTime}}</view>
              <view class="mp-attendance__clock__offduty__real-location" wx:if="{{clockInfo.offduty.storeId || clockInfo.offduty.locationAddress}}">
                <image class="mp-attendance__clock__offduty__real-location-icon" src="/images/attendance/clock/location.svg" />
                <text wx:if="{{clockInfo.offduty.isScheduleLocation}}">({{clockInfo.offduty.locationName}})</text>
                {{clockInfo.offduty.locationAddress}}
              </view>
            </view>
            <view class="mp-attendance__clock__offduty__punch-again" bind:tap="onClock" data-type="{{dutyTypes.OFF_DUTY}}" data-clock-type="update" wx:if="{{clockInfo.schedule.id && clockInfo.offduty.isClocked && isToday}}">更新打卡 ></view>
            <view
              class="mp-attendance__clock__offduty__abnormal-tip"
              wx:if="{{clockInfo.offduty.clockStatus === clockTypes.ABNORMAL && (clockInfo.schedule.id || clockInfo.offduty.isClocked)}}">
              {{clockInfo.offduty.abnormalClockTip}}
            </view>
            <view wx:if="{{ duty.dutyType === dutyTypes.OFF_DUTY && !clockInfo.offduty.isClocked }}">
              <view class="mp-attendance__clock__offduty__button-group">
                <view
                  class="mp-attendance__clock__offduty__button {{duty.clockStatus === clockTypes.NORMAL ? 'mp-attendance__clock__offduty__button--in-location' : 'mp-attendance__clock__offduty__button--out-location'}}"
                  bind:tap="onClock"
                  data-type="{{dutyTypes.OFF_DUTY}}">
                  <view class="mp-attendance__clock__offduty__button-text">{{duty.dutyText}}</view>
                  <view class="mp-attendance__clock__offduty__button-time">{{clockTime}}</view>
                </view>
                <view class="mp-attendance__clock__offduty__location">
                  <icon type="success" size="14" color="rgb(136, 205,58)" wx:if="{{duty.isScheduleLocation}}" />
                  <text class="mp-attendance__clock__offduty__location-info" wx:if="{{duty.isScheduleLocation}}">已进入考勤范围: ({{duty.locationName}}){{duty.locationAddress}}</text>
                  <text class="mp-attendance__clock__offduty__location-info" wx:if="{{!duty.isScheduleLocation}}">当前位置: <text wx:if="{{duty.storeId}}">({{duty.locationName}})</text>{{duty.locationAddress}}</text>
                  <text class="mp-attendance__clock__offduty__location-again" bind:tap="goToRelocation">去重新定位</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<view class="mp-attendance__clock__success__page" wx:if="{{successContent.show}}">
  <view class="mp-attendance__clock__success__container">
    <image class="mp-attendance__clock__success__image" src="/images/attendance/clock/clock-success.svg" />
    <view class="mp-attendance__clock__success__clock-time">{{successContent.time}}</view>
    <view class="mp-attendance__clock__success__clock-type">{{successContent.title}}</view>
    <view class="mp-attendance__clock__success__clock-tip">{{successContent.content}}</view>

    <button class="mp-attendance__clock__success__button button button-primary" bind:tap="onIkown">我知道了</button>
  </view>
</view>
