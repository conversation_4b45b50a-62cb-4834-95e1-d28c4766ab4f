@import 'styles/app/attendance';

@mixin mp-attendance-clock__duty-container() {
  $dot-width: u(16);
  $margin-top: u(20);

  &__container {
    min-height: u(240);
    color: #575d6a;
    padding-left: u(40);
    position: relative;
  }

  &__dot {
    width: $dot-width;
    height: $dot-width;
    border-radius: 50%;
    background: #b0b0b0;
    position: absolute;
    z-index: 1;
    left: u(8);
    top: u(14);

    &--active {
      background: #23b9de;
    }
  }

  &__line {
    width: 2px;
    position: absolute;
    left: u(14);
    top: u(24);
    min-height: u(240);
    height: calc(100% + #{$margin-top});
    background: #ebebeb;
  }

  &__time {
    display: flex;
    font-size: u(28);
  }

  &__status {
    margin-left: u(16);

    &-wrapper {
      display: flex;
    }
  }

  &__modified {
    margin-left: u(16);
  }

  &__real-time {
    font-size: u(36);
  }

  &__real-location {
    color: #9397a2;
    font-size: u(24);
    display: flex;
    align-items: center;

    &-icon {
      width: u(20);
      height: u(26);
      margin-right: u(16);
    }
  }

  &__button {
    width: u(250);
    height: u(250);
    background-image: linear-gradient(-180deg, #37ddf3 13%, #2cb0d4 90%);
    box-shadow: 0 7px 18px 0 #b8e2ec;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: u(48);

    &-group {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-top: u(24);
    }

    &-text {
      color: #fff;
      font-size: u(36);
    }

    &-time {
      color: #a0ecff;
      font-size: u(32);
    }

    &--in-location {}

    &--out-location {
      background-image: linear-gradient(-180deg, #ffb543 10%, #fd7c32 92%);

      .mp-attendance__clock__onduty__button-time {
        color: #eaeaea;
      }

      .mp-attendance__clock__offduty__button-time {
        color: #eaeaea;
      }
    }
  }

  &__location {
    $location-width: u(520);
    $location-again-width: u(230);
    $icon-width: u(28);
    $icon-margin-right: u(16);
    $info-margin-right: u(16);

    width: $location-width;
    display: flex;
    align-items: center;
    justify-content: center;

    icon {
      margin-right: $icon-margin-right;
    }

    &-info {
      max-width: $location-width - $icon-width - $location-again-width - $icon-margin-right - $info-margin-right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: $info-margin-right;
    }

    &-again {
      color: #23b9de;
      font-size: u(24);
      width: $location-again-width;
    }
  }

  &__punch-again {
    color: #618eb9;
    margin-top: u(16);
    width: u(180);
  }

  &__abnormal-tip {
    color: #fb9e0f;
    margin-top: u(16);
  }
}

@mixin mp-attendance-clock-theme($theme) {
  .mp-attendance__clock {

    &-container {
    }

    &__content {
      padding: u(48) u(24);
    }

    &__title {
      &-tip {
        color: #9397a2;
        font-size: u(24);
      }
    }

    &__vocation {
      &-list {}

      &-item {
        position: relative;
        margin-bottom: u(16);

        &__dot {
          position: absolute;
          background: #fb9e0f;
          width: u(16);
          height: u(16);
          border-radius: 50%;
          left: 0;
          top: u(16);
        }

        &__title {
          font-size: u(28);
          color: #2f2f2f;
          margin-left: u(32);
        }

        &__time {
          font-size: u(28);
          color: #9397a2;
          margin-left: u(32);
        }
      }
    }

    &__onduty {
      min-height: u(240);
      margin-top: u(48);

      @include mp-attendance-clock__duty-container();
    }

    &__offduty {
      min-height: u(240);
      margin-top: u(30);

      @include mp-attendance-clock__duty-container();
    }

    &__success {
      &__page {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, .5);
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &__container {
        width: u(580);
        height: u(708);
        background: #fff;
        border-radius: u(10);
        padding: u(48);
        display: flex;
        align-items: center;
        flex-direction: column;
      }

      &__image {
        width: u(320);
        height: u(130);
        margin-top: u(32);
      }

      &__clock {
        &-time {
          font-size: u(84);
          color: #23b9de;
          line-height: u(84);
          margin-top: u(72);
          font-weight: 500;
        }

        &-type {
          font-size: u(36);
          color: #23b9de;
          line-height: u(36);
          margin-top: u(20);
        }

        &-tip {
          font-size: u(32);
          color: #9397a2;
          line-height: u(32);
          margin-top: u(48);
        }
      }

      &__button {
        margin-top: u(72);
      }
    }
  }
}

@include mp-app-attendance-theme($app-theme);
@include mp-attendance-clock-theme($app-theme);
