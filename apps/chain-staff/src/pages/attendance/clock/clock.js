import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  CONSTANT,
  getStorage,
} from '@petkit/redux';
import moment from 'moment';
import * as amap from 'utils/amap-wx';
import {
  getDistanceByLatAndLon
} from 'utils/getDistanceByLatAndLon';
import {
  setTimer,
} from 'utils/setTimer';
import {cloneDeep} from 'lodash-es';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    name: '邓波儿',
    warehouseName: '中华店',
    date: moment().format('YYYY.MM.DD'),

    _start: '2010-01-01',
    _end: moment().format('YYYY-MM-DD'),

    dutyTypes: CONSTANT.ATTENDANCE.DUTY_TYPE,
    clockTypes: CONSTANT.ATTENDANCE.CLOCK_TYPES,

    relocationTimer: null,

    clockTime: '',

    hasClockInfo: false,

    isClocking: false,

    isLocating: false,

    isOnduty: false,

    isToday: true,

    successContent: {
      show: false,
      time: 0,
      title: '',
      content: '',
    },

    timer: null,
  },

  onLoad() {
    this._initAmap();
  },

  onShow() {
    this._requestClockInfo();
    this._calculateCurrentTime();

    this._onSub();
  },

  onHide() {
    const duty = cloneDeep(this.data.duty);
    if (duty) {
      duty.dutyType = this.data.dutyTypes.NONE_DUTY;
      this.setData({
        duty,
      });
    }

    clearTimeout(this.data.timer);
    this.data.sub && this.data.sub();
    this.setData({
      hasClockInfo: false,
      isLocating: false,
      // duty: duty,
    });
  },

  onUnload() {
    const duty = cloneDeep(this.data.duty);
    duty.dutyType = this.data.dutyTypes.NONE_DUTY;
    this.data.sub && this.data.sub();
    this.setData({
      hasClockInfo: false,
      isLocating: false,
      duty: duty,
    });
  },

  _onSub() {
    this.data.sub = store.subscribe(() => {
      if (!this.data.userName) {
        this.setData({
          userName: '...',
        });
      }
      this._requestClockInfo();
    });
  },

  _requestClockInfo() {
    if (!this.data.hasClockInfo && this.data.userId) {
      this.setData({
        hasClockInfo: true,
      });
      this.requestClockInfo({
        // userId: 1401,
        userId: this.data.userId,
        day: moment(this.data.date.split('.').join('-'), 'Asia/Shanghai').valueOf(),
      }, {
        success: () => {
          // 等state变化过后，开始获取用户的当前位置
          // this._getEmployeeLocation();
          this. _getUserLocation();
        },
      });
    }
  },

  // 去重新定位
  goToRelocation() {
    const duty = this.data.duty;
    const clockInfo = this.data.clockInfo;
    this.saveDuty({duty});
    this.saveClockInfo({clockInfo});
    if (duty.latitude || duty.longitude) {
      wx.navigateTo({
        url: '/pages/attendance/clock-map/clock-map',
      });
    }
  },

  bindDateChange({
    detail: {
      value
    }
  }) {
    let isToday = false;
    const _date = moment(value).startOf('day').valueOf();
    const today = moment().startOf('day').valueOf();
    if (today === _date) {
      isToday = true;
    }
    this.setData({
      date: moment(value).format('YYYY.MM.DD'),
      isToday: isToday,
    });
    this.requestClockInfo({
      userId: this.data.userId,
      day: _date,
    });
  },

  onClock(ev) {
    const date = this.data.date.split('.').join('-');
    const type = ev.currentTarget.dataset.type;
    const dutyTypes = this.data.dutyTypes;
    const duty = this.data.duty;
    const clockInfo = this.data.clockInfo;
    if (this.data.duty.clockStatus !== this.data.clockTypes.ABNORMAL && ev.currentTarget.dataset.clockType !== 'update') {
      const param = {
        punchTime: moment().valueOf(),
        userId: clockInfo.employee.id,
        scheduleId: clockInfo.schedule.id,
        punchLatitude: duty.latitude,
        punchLongitude: duty.longitude,
        punchAddress: duty.locationAddress,
        punchRemark: duty.remark,
        punchType: 'CHECKIN',
      };

      if (type) {
        if (type === dutyTypes.ON_DUTY) {
          param.punchType = 'CHECKIN';
        } else {
          param.punchType = 'CHECKOUT';
        }
      } else {
        if (duty.dutyType === dutyTypes.ON_DUTY) {
          param.punchType = 'CHECKIN';
        } else {
          param.punchType = 'CHECKOUT';
        }
      }

      if (!param.punchLongitude || !param.punchLatitude) {
        this._getUserLocation();
        return;
      }

      this.requestPunch(param, {
        success: () => {
          this._setSuccessContent();

          this.requestClockInfo({
            userId: this.data.userId,
            day: moment(date, 'Asia/Shanghai').valueOf(),
          }, {
            success: () => {
              this._getEmployeeLocation();
            }
          });
        },
        failure: () => {
        },
      });
    } else {
      duty.dutyType = type;
      this.setData({
        duty,
      });
      this.goToRelocation();
    }
  },

  onIkown() {
    this._resetSuccessContent();
  },

  _setSuccessContent() {
    const successContent = this.data.successContent;
    const duty = this.data.duty;
    const dutyTypes = this.data.dutyTypes;
    const clockTime = this.data.clockTime;

    successContent.show = true;
    successContent.time = clockTime.split(':')[0] + ':' + clockTime.split(':')[1];
    if (duty.dutyType === dutyTypes.ON_DUTY) {
      successContent.title = '上班打卡成功';
      successContent.content = '迎着朝阳奋斗吧，少年！';
    } else if (duty.dutyType === dutyTypes.OFF_DUTY || duty.dutyType === dutyTypes.NONE_DUTY) {
      successContent.title = '下班打卡成功';
      successContent.content = '小佩和你一起成长！';
    } else {
      successContent.title = '未知打卡成功';
    }

    this.setData({
      successContent: successContent,
    });
  },

  _resetSuccessContent() {
    this.setData({
      successContent: {
        show: false,
        time: 0,
        title: '',
        content: '',
      }
    });
  },

  _getEmployeeLocation() {
    // this.setData({
    //   isLocating: true,
    // });

    // 该方法为 如果在调用该方法之前，且第一次使用该方法时，则会弹出授权请求提示框
    // 如果选择拒绝，则会走fail回调
    // 如果选择同意，则会走success回调
    // 如果以前就拒绝授权地理位置时，则不会弹出授权请求提示框，并走fail回调
    // 如果以前就同意授权地理位置时，则直接走success回调
    // 此处fail的话，需要提示用户去手动授权地理位置
    wx.authorize({
      scope: 'scope.userLocation',
      success: (res) => {
        console.log('authorize success: ', res);
        this._getLocation();
      },
      fail: (err) => {
        console.log('authorize fail: ', err);
        wx.openSetting({
          success: (res) => {
            this._getLocation();
          },
          fail: (err) => {
            this.showToast({
              message: '您已取消授权，无法定位当前位置！',
            });
          }
        });
      }
    });
    // this.data.relocationTimer = setTimer(() => {
    // }, 8000);
    // 先获取设备的当前位置信息
    // 在通过schedule中的考勤信息来判断是否是在打卡范围内
  },

  _getUserLocation() {
    const _this = this;
    wx.getSetting({
      success: (res) => {
        console.log(res);
        if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
          wx.showModal({
            title: '定位服务未授权',
            content: '请开启小程序定位授权',
            success: function (res) {
              if (res.cancel) {
                wx.showToast({
                  title: '拒绝授权',
                  icon: 'none',
                  duration: 1000
                });
              } else if (res.confirm) {
                //确定授权，通过wx.openSetting发起授权请求
                wx.openSetting({
                  success: function (res) {
                    if (res.authSetting['scope.userLocation'] == true) {
                      //再次授权，调用wx.getLocation的API
                      _this.data.amap.getRegeoWithWXLocation({
                        success: (res) => {
                          const result = res[0];
                          const duty = this.data.duty;
                          duty.latitude = result.latitude;
                          duty.longitude = result.longitude;
                          duty.locationAddress = result.name;
                          duty.storeId = 0;
                          duty.locationName = '';
                          _this._setLocation(duty);
                          // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
                          wx.removeStorage({key:"userLocation"});
                        },
                        fail: (info) => {
                          wx.showToast({
                            title: '定位服务未开启,请打开手机设置，确保定位服务已开启，并允许企业微信访问位置',
                            duration: 3000,
                          });
                          // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
                          wx.removeStorage({key:"userLocation"});
                        }
                      });
                    } else {
                      wx.showToast({
                        title: '授权失败',
                        icon: 'none',
                        duration: 500
                      });
                    }
                  }
                });
              }
            }
          });
        } else {
          _this._getLocation();
        }
      }
    });
  },

  _getLocation() {
    this.data.amap.getRegeoWithWXLocation({
      success: (res) => {
        console.log(res)
        const result = res[0];
        const duty = this.data.duty;
        duty.latitude = result.latitude;
        duty.longitude = result.longitude;
        duty.locationAddress = result.name;
        duty.storeId = 0;
        duty.locationName = '';

        this._setLocation(duty);
        // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
        wx.removeStorage({key:"userLocation"});
      },
      fail: (info) => {
        console.log(info)
        // 高德getRegeoWithWXLocation在成功时，会自动存储一次最后成功的定位
        wx.removeStorage({key:"userLocation"});
      }
    });
  },

  // 实时计算打卡处显示的时间
  _calculateCurrentTime() {
    const timer = setTimer(() => {
      let isOnduty = false;
      const clockTime = moment().format('HH:mm:ss');
      const dutyStart = this._getDutyTime('start');
      const duty = this.data.duty;
      isOnduty = (moment().valueOf() >= dutyStart);
      this.setData({
        clockTime: clockTime,
        isOnduty: isOnduty,
      });
      // console.log('222: ', this.data.duty, this.data.duty.latitude, this.data.duty.longitude);
      // console.log(duty.dutyType, CONSTANT.ATTENDANCE.DUTY_TYPE.ON_DUTY, duty.dutyType === CONSTANT.ATTENDANCE.DUTY_TYPE.ON_DUTY);
      if (duty && duty.dutyType === CONSTANT.ATTENDANCE.DUTY_TYPE.ON_DUTY) {
        this._setDutyStatus(this.data.duty);
      }
    }, 1000);

    this.setData({
      timer,
    });
  },

  // 收卡设置
  _setDutyStatus(duty) {
    const dutyStart = this._getDutyTime('start');
    const dutyEnd = this._getDutyTime('end');
    const dutyDuration = dutyEnd - dutyStart;
    const dutyDurationHour = moment.duration(dutyDuration).asHours();
    const dutyAlreadyDurationHour = moment.duration(dutyEnd - moment().valueOf()).asHours();
    // const duty = this.data.duty;
    const clockInfo = this.data.clockInfo;
    const clockTime = this.data.date.split('.').join('-') + ' ' + this.data.clockTime;

    // 获取当前的打卡状态

    // const attendanceTime =
    // 如果当前状态为上班打卡时
    if (clockInfo.vocations && clockInfo.vocations.length) {
      if (duty.dutyType === this.data.dutyTypes.ON_DUTY) {
        // 如果排班总时间 >= 8 小时，则4小时后，上班打卡变更为下班打卡
        if (dutyDurationHour >= 8 && dutyAlreadyDurationHour <= 4) {
          duty.dutyType = this.data.dutyTypes.OFF_DUTY;
          duty.dutyText = '下班打卡';

          clockInfo.onduty.clockStatus = this.data.clockTypes.ABNORMAL;
          clockInfo.onduty.abnormalTypes = ['缺卡'];
        }
        // 否则，上班打卡永远不变更为下班打卡
      } else if (duty.dutyType === this.data.dutyTypes.OFF_DUTY) { // 如果当前状态为下班打卡时
      }
    }

    // if (!clockInfo.onduty.isClocked && clockInfo.schedule._ondutyTime <= moment(clockTime).valueOf()) {
    //   duty.dutyText = (duty.dutyText === '外勤打卡' ? '外勤打卡' : '迟到打卡');
    // }

    // if (!clockInfo.onduty.isClocked && clockInfo.schedule._offdutyTime >= moment(clockTime).valueOf()) {
    //   duty.dutyText = (duty.dutyText === '外勤打卡' ? '外勤打卡' : '早退打卡');
    // }

    // if (clockInfo.schedule) {
    //   duty.dutyText = '外勤打卡';
    // }

    this.setData({
      clockInfo: cloneDeep(clockInfo),
    });
    this._setLocation(duty);
    // this._calculateCurrentTime();
  },

  _getDutyTime(type) {
    let dutyTime = 0;
    if (this.data.clockInfo) {
      if (type === 'start') {
        dutyTime = this.data.clockInfo.schedule._ondutyTime;
      } else if (type === 'end') {
        dutyTime = this.data.clockInfo.schedule._offdutyTime;
      }
    }
    return dutyTime;
  },

  _initAmap() {
    const _amap = new amap.AMapWX({key: 'bb5f8ee7dd62ebed24dc2ef2674866b9'});
    this.setData({
      amap: _amap,
    });
  },

  _setLocation(duty) {
    if (this.data.clockInfo) {
      const clockInfo = this.data.clockInfo;
      // const duty = this.data.duty;
      const currentLatitude = duty.latitude;
      const currentLongitude = duty.longitude;
      const scheduleLatitude = clockInfo.schedule.latitude;
      const scheduleLongitude = clockInfo.schedule.longitude;
      const _distance = getDistanceByLatAndLon(currentLatitude, currentLongitude, scheduleLatitude, scheduleLongitude) * 1000;
      // 在打卡范围内
      if (duty.dutyType !== this.data.dutyTypes.NONE_DUTY) {
        if (_distance && _distance <= clockInfo.schedule.range) {
          duty.isScheduleLocation = true;
          duty.locationName = clockInfo.schedule.storeName;
          duty.locationAddress = clockInfo.schedule.storeAddress;
          duty.storeId = clockInfo.schedule.storeId || duty.storeId;
          duty.latitude = duty.latitude;
          duty.longitude = duty.longitude;

          duty.dutyText = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).dutyText;
          duty.clockStatus = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).clockStatus;
          duty.abnormalTypes = duty.setLatedAndEarlyPunch(clockInfo.schedule._ondutyTime, clockInfo.schedule._offdutyTime).abnormalTypes;

          // duty.clockStatus = this.data.clockTypes.NORMAL;
          // if (duty.dutyType === this.data.dutyTypes.ON_DUTY) {
          //   duty.dutyText = '上班打卡';
          // } else if (duty.dutyType === this.data.dutyTypes.OFF_DUTY) {
          //   duty.dutyText = '下班打卡';
          // }
        } else {
          duty.isScheduleLocation = false;
          duty.dutyText = '外勤打卡';
          duty.clockStatus = this.data.clockTypes.ABNORMAL;
          duty.abnormalTypes = [CONSTANT.ATTENDANCE.ABNORMAL_TYPES.LEGWORK];
        }
      }

      this.setData({
        duty: cloneDeep(duty),
      });
    }
  }
};

const mapStateToData = state => {
  return {
    userId: state.user.loginWp.userId,
    userName: state.user.loginWp.userName,
    clockInfo: state.attendance.clock.clockInfo,
    duty: state.attendance.clock.duty,
  }
};

const mapDispatchToPage = dispatch => bindActionCreators({
  showToast: acs.global.toast.show,
  saveDuty: acs.attendance.clock.saveDuty,
  saveClockInfo: acs.attendance.clock.saveClockInfo,
  requestClockInfo: acs.attendance.clock.requestClockInfo,
  requestPunch: acs.attendance.clock.requestPunch,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
