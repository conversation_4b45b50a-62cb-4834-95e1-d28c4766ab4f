import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  getStorage,
} from '@petkit/redux';
import {
  getHost,
} from 'config/config';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    src: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const url = getHost() + '/m/check/#!';
    const token = getStorage().getItem('F-Session');
    const storeId = getStorage().getItem('storeId');

    const src = `${url}/redirect/${token}/${storeId}/check`;

    this.setData({
      src
    });
  },
  onUnload() {
    this.requestQuit({
      warehouseId: getStorage().getItem('storeId'),
    });
  },
};

const mapStateToData = state => ({
});

const mapDispatchToPage = dispatch => bindActionCreators({
  requestQuit: acs.inventory.check.requestQuit,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

