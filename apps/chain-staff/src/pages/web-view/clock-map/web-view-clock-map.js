import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  store,
} from '@petkit/redux';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    src: '',
  },

  onLoad() {
  },

  onBindLoad(ev) {
    console.log(ev);
  },

  onError(ev) {
    console.log(ev);
  }
};

const mapStateToData = state => ({

});

const mapDispatchToPage = dispatch => bindActionCreators({

}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
