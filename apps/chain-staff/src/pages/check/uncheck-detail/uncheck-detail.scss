@import 'config/theme.config';

.wp-uncheck-detail {
  &__container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__header {
    flex: 0 0;
    display: block;
  }

  &__content {
    flex: 1 0;
    overflow: auto;
  }

  &__select,
  &__time {
    width: 670rpx;
    height: 92rpx;
    line-height: 92rpx;
    padding: 0 20rpx;
    margin: 20rpx auto;
    background-color: #fff;
    font-size: 32rpx;

    &--disabled {
      color: #b0b0b0;
    }
  }

  &__select {
    display: flex;
    justify-content: space-between;
  }

  &__pet-card {
    width: 670rpx;
    padding: 0 20rpx 20rpx;
    margin: 90rpx auto 20rpx;
    background-color: #fff;
    font-size: 32rpx;

    &-info {
      width: 100%;

      &-image-container {
        text-align: center;
      }

      &-image-bg {
        display: inline-block;
        width: 116rpx;
        height: 116rpx;
        margin-top: -58rpx;
        border-radius: 90%;
        background-color: #fff;
      }

      &-image {
        width: 100rpx;
        height: 100rpx;
        display: block;
        margin: 8rpx;
      }

      &-header {
        text-align: center;
      }
    }

    &-status {
      margin-top: 40rpx;
      display: flex;
      padding-bottom: 20rpx;
    }

    &-unfinished,&-finished,&-uncheck{
      flex: 1;
      box-sizing: border-box;
      height: 92rpx;
      text-align: center;

      &-number {
        font-size: 40rpx;

        &--uncheck {
          @extend .wp-uncheck-detail__pet-card-finished-number;
          color: #fa6262;
        }
      }

      &-number-text {
        font-size: 20rpx;
        color: #9397A2;

        &--uncheck {
          @extend .wp-uncheck-detail__pet-card-finished-number-text;
          color: #fa6262;
        }
      }
    }
  }

  &__check-card {
    width: 630rpx;
    height: 180rpx;
    padding: 0 40rpx;
    margin: 20rpx auto;
    background-color: #fff;
    font-size: 32rpx;
    display: flex;
    align-items: center;

    &-status {
      color: #23b9de;
    }

    &-info {
      display: flex;
      flex-grow: 1;
      flex-wrap: nowrap;
      justify-content: flex-end;
      align-items: center;

      &-container {
        font-size: 24rpx;
        line-height: 32rpx;
        color: #747882;
        margin-right: 10rpx;
        text-align: right;

        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &-btn {
        line-height: u(64);
        font-size: u(28);
      }
    }
  }

  &__uncheck-card {
    width: 630rpx;
    height: 180rpx;
    line-height: 180rpx;
    padding: 0 40rpx;
    margin: 20rpx auto;
    display: flex;
    color: #fa6262;
    background-color: #fff;
    font-size: 32rpx;
    justify-content: space-between;

    &--uncheck {
      @extend .wp-uncheck-detail__uncheck-card;
      color: #9397a2;
    }
  }
}

