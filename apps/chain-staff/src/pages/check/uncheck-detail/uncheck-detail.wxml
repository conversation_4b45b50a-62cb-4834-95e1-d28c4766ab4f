<view class="wp-page">
  <view class="wp-uncheck-detail__container">
    <component-check-header class="wp-uncheck-detail__header"></component-check-header>
    <view class="wp-uncheck-detail__content">
      <view wx:if="{{ableCheck}}" class="wp-uncheck-detail__time br-4">
        检查时段：<text>{{currTime.phaseConfigStart}} - {{currTime.phaseConfigEnd}}</text>
      </view>
      <view wx:else class="wp-uncheck-detail__time  wp-uncheck-detail__time--disabled br-4">
        不在检查时段内
      </view>
      <view class="wp-uncheck-detail__pet-card br-4">
        <view class="wp-uncheck-detail__pet-card-info">
          <view class="wp-uncheck-detail__pet-card-info-image-container">
            <view class="wp-uncheck-detail__pet-card-info-image-bg">
              <image class="wp-uncheck-detail__pet-card-info-image" src="../../../images/check/cat-1.png"></image>
            </view>
          </view>
          <view class="wp-uncheck-detail__pet-card-info-header">
            {{state.petFoster.item.petName}} 的历史检查单
          </view>
        </view>
        <view class="wp-uncheck-detail__pet-card-status">
          <view class="wp-uncheck-detail__pet-card-finished">
            <view class="wp-uncheck-detail__pet-card-finished-number">{{state.finished}}</view>
            <view class="wp-uncheck-detail__pet-card-finished-number-text">已完成</view>
          </view>
          <view class="wp-uncheck-detail__pet-card-unfinished">
            <view class="wp-uncheck-detail__pet-card-finished-number">{{state.uncheck}}</view>
            <view class="wp-uncheck-detail__pet-card-finished-number-text">待检查</view>
          </view>
          <view class="wp-uncheck-detail__pet-card-uncheck">
            <view class="wp-uncheck-detail__pet-card-finished-number--uncheck">{{state.unfinished}}</view>
            <view class="wp-uncheck-detail__pet-card-finished-number-text--uncheck">未检查</view>
          </view>
        </view>
      </view>


      <picker
        mode="date"
        bindchange="bindPickerChange"
        value="{{datePicker.value}}"
        start="{{datePicker.start}}"
        end="{{datePicker.end}}"
        >
        <view class="wp-uncheck-detail__select br-4">
          <view class="wp-uncheck-detail__select-time">
            {{datePicker.value}}
          </view>
          <view class="wp-uncheck-detail__select-btn">选择</view>
        </view>
      </picker>

      <block wx:for="{{state.list}}">
        <block  wx:if="{{item.item.status == 2}}">
          <view class="wp-uncheck-detail__check-card br-4">
            <view class="wp-uncheck-detail__check-card-status">
              已检查
            </view>
            <view class="wp-uncheck-detail__check-card-info">
              <view class="wp-uncheck-detail__check-card-info-container">
                <view class="wp-uncheck-detail__check-card-info-time">{{item.item.phaseConfigStartTime}} - {{item.item.phaseConfigEndTime}}</view>
                <!-- <text class="wp-uncheck-detail__check-card-info-name">{{state.petFoster.item.checkerName}}</text> -->
                <text class="wp-uncheck-detail__check-card-info-name">{{item.item.checkerName}}</text>
              </view>
              <view>
                <button class="wp-uncheck-detail__check-card-info-btn bc-p color-white" bindtap="onCheckTap" data-detail="{{item}}">查看</button>
              </view>
            </view>
          </view>
        </block>
        <block wx:elif="{{item.item.status == 0}}">
          <view class="wp-uncheck-detail__uncheck-card--uncheck br-4">
            <view class="wp-uncheck-detail__uncheck-card-status">
              待检查
            </view>
            <view class="wp-uncheck-detail__uncheck-card-info">
              {{item.item.phaseConfigStartTime}} - {{item.item.phaseConfigEndTime}}
            </view>
          </view>
        </block>
        <block wx:else>
          <view class="wp-uncheck-detail__uncheck-card br-4">
            <view class="wp-uncheck-detail__uncheck-card-status">
              无人检查
            </view>
            <view class="wp-uncheck-detail__uncheck-card-info">
              {{item.item.phaseConfigStartTime}} - {{item.item.phaseConfigEndTime}}
            </view>
          </view>
        </block>
      </block>

      <view class="ta-c color-hint" wx:if="{{state.list.length === 0}}">
        暂无数据
      </view>
    </view>
  </view>

</view>
