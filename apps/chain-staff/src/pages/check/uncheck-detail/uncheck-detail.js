import {
  connect,
} from 'utils/weapp-redux';
import moment from 'moment';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    fosterId: 0,
    petId: 0,

    datePicker: {
      value: moment(new Date()).format('YYYY-MM-DD'),
      start: '1970-01-01',
      end: moment(new Date()).add(1, 'y').format('YYYY-MM-DD'),
    },
  },
  bindPickerChange({
    detail: {
      value,
    }
  }) {
    this.setData({
      datePicker: {
        value,
      }
    });
    this.requestData();
  },
  onCheckTap({
    target: {
      dataset: {
        detail,
      }
    }
  }) {
    this.setCheckDetail({
      detail,
    });
    // console.log(detail);
    wx.navigateTo({
      url: `/pages/check/check-detail/check-detail?id=${detail.item.id}&readonly=1`,
    });
  },
  getDateParams: function (date) {
    return {
      phaseConfigStart: moment(new Date(date)).format('YYYY-MM-DD 00:00:00'),
      phaseConfigEnd: moment(new Date(date)).format('YYYY-MM-DD 23:59:59'),
    };
  },
  requestData: function () {
    let {fosterId, petId} = this.data;

    let {
      phaseConfigStart,
      phaseConfigEnd,
    } = this.getDateParams(this.data.datePicker.value);

    this.getPetCheckList({
      fosterId,
      petId,
      phaseConfigStart,
      phaseConfigEnd,
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let {fosterId, petId} = options;

    this.setData({
      fosterId,
      petId,
    });

    this.requestData();
  },
};

const mapStateToData = state => ({
  state: state.foster.dailyCheck.check.petCheckList,
  currTime: state.foster.dailyCheck.date.currTime,
  ableCheck: state.foster.dailyCheck.date.ableCheck,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  getPetCheckList: acs.foster.dailyCheck.check.getPetCheckList,
  setCheckDetail: acs.foster.dailyCheck.check.setCheckDetail,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

