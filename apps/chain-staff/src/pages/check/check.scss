@import 'config/theme.config';

.wp-check {
  &__container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    position: relative;
  }

  &__exit-modal {
    position: absolute;
    top: u(400);
    left: u(56);
    right: u(56);
    
    border-radius: u(15);
    z-index: 99;

    &-mask {
      position: absolute;
      top: 0;
      left: 0;

      width: 100%;
      height: 100%;

      background-color: #000;
      opacity: .5;

      z-index: 90;
    }
  }

  &__fixed {
    display: block;
    flex: 0 0;
  }

  &__responsible {
    display: block;
    flex: 1 0;
  }

  &__panel-number {
    font-size: 44rpx;
    color: #30b9dc;
    height: 48rpx;
    margin-top:-6rpx;
  }

  &__card-text {
    margin-top: 10rpx;
  }

  &__panel-h1 {
    font-weight: bold;
    font-size: u(32);
  }

  &__item {
    flex: 0 0 174rpx;
    border-right: 1px solid #f1f1f1;
    box-sizing: border-box;
  }

  &__item:last-child {
    border-right: none;
  }

  &__show-panel{
    text-align: center;
    background-color: #fff;
    height: 64px;
    margin: 12px;
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
    box-sizing:border-box;

    .scroll_container {
      color: #fb9e0f;
      position: relative;
      height: 36px;
      line-height: 36px;
    }

    .notice_text {
      z-index: 99;
      position: absolute;
      font-size: 12px;
      width: 46px;
      height: 36px;
      font-weight: bold;
      background-color: #fff9e6;
    }

    .notice_text view{
      width: 46px;
      height: 18px;
      line-height: 15px;
      text-align: center;
      vertical-align: top;
      letter-spacing:1px
    }


  }

  &__check-panel {
    &-container {
      padding: 24rpx;
      background: #fff;
      width: 702rpx;
      margin: 0 auto 20rpx;
      box-sizing: border-box;
    }
    display: flex;
    justify-content: flex-start;

    &-item {
      flex: 0 0 160rpx;
      box-sizing: border-box;
      margin-top: 24rpx;
      text-align: center;
    }

    .icon-check, .icon-notice {
      width: 48px;
      height: 48px;
    }

  }

  &__scroll {
    position: relative;
    margin-top: -8rpx;

    &-fix {
      position: absolute;
      width: 100rpx;
      height: 70rpx;
      left: 0;
      top: 0;
      z-index: 10;
      color: #fb9e0f;
      font-weight: 500;
      background-color: #fef9e8;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;

      &-image {
        height: 48rpx;
        width: 48rpx;
      }
    }
  }

  &__marquee {

    @keyframes kf-marque-animation { 0% { transform: translateX(0); } 100% { transform: translateX(-750rpx); } }

    width: 750rpx;
    height: 70rpx;
    line-height: 70rpx;
    background: #e9eaea;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: clip;
    position: relative;
    background-color: #fef9e8;

    &-wrapper {
      display: block;
      margin-left: 100rpx;
    }

    &-content {
      display: inline-block;
      width: 100%;
      position: relative;
      padding-right: 0;
      white-space: nowrap;
      animation: kf-marque-animation 36s infinite linear;
      color: #fb9e0f;

      &:last-child {
        padding-left: 4em;
      }

      &-space {
        display: inline-block;
        width: .5em;
      }
    }
  }
}

