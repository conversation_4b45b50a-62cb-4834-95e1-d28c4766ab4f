<view class="wp-page">
  <view class="wp-check__container">
    <!-- 不是从企业微信进入的弹窗提示退出 -->
    <exit-modal class="wp-check__exit-modal" wx:if="{{isShowExitModal}}"></exit-modal>
    <view class="wp-check__exit-modal-mask" wx:if="{{isShowExitModal}}"></view>

    <check-header class="wp-check__fixed" bind:storechange="onStoreChange"></check-header>

    <!--头部-->
    <view class="wp-check__fixed weui-flex check-flex">
      <view class="weui-flex__item">
        <view>
          <image class="w100" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.EcCnH8mGby3v55fe865c2b9b5ff34eaadc82b073194f.png"></image>
        </view>
      </view>
    </view>

    <view class="wp-check__fixed">
      <view class="wp-check__scroll">
        <view class="wp-check__scroll-fix">
          <image class="wp-check__scroll-fix-image" src="../../images/check/special.png"></image>
          <!-- <view>特别</view> -->
          <!-- <view>关注</view> -->
        </view>
        <view class="wp-check__marquee">
          <view class="wp-check__marquee-wrapper br-4">
            <view class="wp-check__marquee-content">
              <block wx:if="{{ableCheck}}">
                已到检查时间段:<text class="wp-check_marquee-content-start">{{currTime.phaseConfigStart}}-{{currTime.phaseConfigEnd}}</text>
                <text class="wp-check__marquee-content-space"></text>
              </block>
              <block wx:else>
                当前不在检查时间段内
                <text class="wp-check__marquee-content-space"></text>
              </block>
              下次检查时间段:<text class="wp-check_marquee-content-end">{{nextTime.phaseConfigStart}}-{{nextTime.phaseConfigEnd}}</text>
              <text class="wp-check__marquee-content-space"></text>
            </view>
            <view class="wp-check__marquee-content">
              <block wx:if="{{ableCheck}}">
                已到检查时间段:<text class="wp-check_marquee-content-start">{{currTime.phaseConfigStart}}-{{currTime.phaseConfigEnd}}</text>
                <text class="wp-check__marquee-content-space"></text>
              </block>
              <block wx:else>
                当前不在检查时间段内，
                <text class="wp-check__marquee-content-space"></text>
              </block>
              下次检查时间段:<text class="wp-check_marquee-content-end">{{nextTime.phaseConfigStart}}-{{nextTime.phaseConfigEnd}}</text>
              <text class="wp-check__marquee-content-space"></text>
            </view>
          </view>
        </view>
      </view>
    </view>


    <view class="wp-check__fixed">
      <view class="wp-check__show-panel br-4">
        <view class="wp-check__item">
          <view class="check_in_number wp-check__panel-number">
            <text>{{lodging.lodingNum}}</text>
          </view>
          <view class="today_out_number wp-check__card-text">
            <text>入住中</text>
          </view>
        </view>
        <view class="wp-check__item">
          <view class="special_number wp-check__panel-number">
            <text>{{lodging.specialFocusNum}}</text>
          </view>
          <view class="today_out_number wp-check__card-text">
            <text>特别关注</text>
          </view>
        </view>
        <view class="wp-check__item">
          <view class="today_in_number wp-check__panel-number">
            <text>{{lodging.todayCheckInNum}}</text>
          </view>
          <view class="today_out_number wp-check__card-text">
            <text>今日将入住</text>
          </view>
        </view>
        <view class="wp-check__item">
          <view class="today_out_number wp-check__panel-number">
            <text>{{lodging.todayCheckOutNum}}</text>
          </view>
          <view class="today_out_number wp-check__card-text">
            <text>今日将退房</text>
          </view>
        </view>
      </view>
    </view>

    <view class="wp-check__responsible wp-check__check-panel-container br-4">
      <view class="wui-panel__check-panel-h1 wp-check__panel-h1">我的应用</view>
      <view class="wp-check__check-panel">
        <view class="wp-check__check-panel-item" bindtap="onTapRedirectToCheckList">
          <image class="icon-notice" src="../../images/check/foster-check.svg"></image>
          <view>
            <text>寄养检查</text>
          </view>
        </view>
        <!-- <view class="wp-check__check-panel-item" bindtap="onTapInventory">
          <image class="icon-notice" src="../../images/check/inventory.svg"></image>
          <view>
            <text>盘点</text>
          </view>
        </view> -->
        <view class="wp-check__check-panel-item" bindtap="onTapNotification">
          <image class="icon-notice" src="../../images/check/notice.svg"></image>
          <view>
            <text>通知公告</text>
          </view>
        </view>
        <view class="wp-check__check-panel-item" bindtap="onTapClock">
          <image class="icon-notice" src="../../images/check/attendance.svg"></image>
          <view>
            <text>考勤打卡</text>
          </view>
        </view>
        <view class="wp-check__check-panel-item"></view>
      </view>
    </view>

  </view>
</view>
