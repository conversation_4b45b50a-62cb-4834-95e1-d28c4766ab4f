import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';
import {
  merge,
  set,
} from 'lodash-es';

let pageConfig = {
  data: {
    // redux
    readonly: 0,
    state: {},
    submited: false,
    enableDefaultValue: 0,

    // edit
    form: {
      // 温湿度
      // 温度
      temperature: '',
      // 湿度
      humidity: '',

      // 喂食
      foodWeight: '',
      // -1 - 未选择
      // 0  - 无余粮
      // 1  - 有余粮
      hasFood: -1,
      // -1 - 未选择
      // 0  - 没喂罐头
      // 1  - 已喂罐头
      hasTin: -1,
      // -1 - 未选择
      // 0  - 没喂零食
      // 1  - 已喂零食
      hasSnacks: -1,
      // -1 - 未选择
      // 0  - 没喂营养膏
      // 1  - 已喂营养膏
      hasNutrition: -1,
      // 喂食备注
      foodRemark: '',

      // 饮水
      // -1 - 未选择
      // 0  - 异常
      // 1  - 正常
      hasDrink: -1,
      // 饮水备注
      drinkRemark: '',

      // 用药
      // -1 - 未选择
      // 0  - 未用药
      // 1  - 已用药
      hasDrug: -1,
      // 未用药理由
      drugRemark: '',

      // 便便
      // -1 - 未选择
      // 0  - 无便
      // 1  - 有便
      hasShit: -1,
      // 便便备注
      shitRemark: '',

      // 笼子清扫
      // -1 - 未选择
      // 0  - 无需清扫
      // 1  - 已清扫
      clean: -1,
      // -1 - 未选择
      // 0  - 未消毒
      // 1  - 已消毒
      hasDisinfect: -1,

      // 健康状态
      // 0-活跃 1-萎靡 2-安静 3-嗜睡
      health: -1,
      // 备注
      healthRemark: '',

      // 皮肤
      // -1 - 未选择
      // 0  - 无异常
      // 1  - 有异常
      skin: -1,
      // 备注
      skinRemark: '',

      // 特殊情况
      specialRemark: '',

      // 遛弯
      video: {
        // 是否开启遛弯摄像
        checked: false,
        // 视频url
        srcs: [],
      },

      // 拍照
      photo: {
        // 图片url
        srcs: [],
      },
    },


    swiperHeight: '',
    currentTab: 0, //预设当前项的值
    scrollLeft: 0, //tab标题的滚动条位置
  },

  onLoad: function (option) {
    let {id, readonly} = option;

    if (!readonly) {
      this.setCheckDetail({
        id,
      });

      this.putInspection({
        id,
      });
    } else {
      this.setData({
        form: this.data.state.checkResult,
        readonly: 1,
      });
    }

    this.resize();
  },

  onUnload() {
    if (!this.data.submited && !this.data.readonly) {
      let {id} = this.data.state.item;

      this.putReset({
        id,
      });
    }
  },

  updateFormValue({
    path,
    value,
  }) {
    if (!this.data.readonly) {
      let data = this.data;
      set(data, path, value);
      this.setData(data);
    }
  },

  onTapBtn({
    target: {
      dataset: {
        path,
        value,
      },
    }
  }) {
    this.updateFormValue({
      path,
      value,
    });
  },

  bindInput({
    detail: {
      value,
    },
    target: {
      dataset: {
        path,
      }
    }
  }) {
    this.updateFormValue({
      path,
      value,
    });
  },

  bindSwitchChange({
    detail: {
      value,
    },
    target: {
      dataset: {
        path,
      }
    }
  }) {
    this.updateFormValue({
      path,
      value,
    });
    this.resize();
  },

  onVideoUploaded({
    detail
  }) {
    const data = this.data;
    data.form.video.srcs = detail;
    this.setData(data);
  },

  onPhotoUploaded({
    detail
  }) {
    const data = this.data;
    data.form.photo.srcs = detail;
    this.setData(data);
  },

  onTapUploadVideo() {
    wx.chooseVideo({
      success: ({
        tempFilePath
      }) => {
        this.data.video.srcs.push({
          tempFilePath,
        });
      },
      fail: (res) => {
        console.log(res);
      },
    });
  },

  onAllNormalTap() {
    let defaultConfig = {
      temperature: '37',
      humidity: '22',

      hasFood: 1,
      hasTin: -1,
      hasSnacks: -1,
      hasNutrition: -1,

      hasDrink: 1,

      hasDrug: -1,

      hasShit: 0,

      clean: 0,
      hasDisinfect: 0,

      health: 0,

      skin: 0,
    };

    let form = merge(this.data.form, defaultConfig);

    this.setData({
      form,
      enableDefaultValue: 1,
    });
  },

  onTapFinished() {
    if (!this.data.form.temperature) {
      this.acShowToast({
        message: '请输入温度！',
      });
      return ;
    }
    if (!this.data.form.humidity) {
      this.acShowToast({
        message: '请输入湿度！',
      });
      return ;
    }
    if (this.data.form.hasFood < 0) {
      this.acShowToast({
        message: '请选择余粮！',
      });
      return ;
    }
    if (this.data.form.hasDrink < 0) {
      this.acShowToast({
        message: '请选择饮水！',
      });
      return ;
    }
    if (this.data.form.hasShit < 0) {
      this.acShowToast({
        message: '请选择便便！',
      });
      return ;
    }
    if (this.data.form.clean < 0) {
      this.acShowToast({
        message: '请选择笼子清扫！',
      });
      return ;
    }
    if (this.data.form.health < 0) {
      this.acShowToast({
        message: '请选择健康状态！',
      });
      return ;
    }
    if (this.data.form.skin < 0) {
      this.acShowToast({
        message: '请选择皮肤状况！',
      });
      return ;
    }
    if (this.data.form.photo.srcs.length === 0) {
      this.acShowToast({
        message: '请选择图片！',
      });
      return ;
    }

    let checkResult = JSON.stringify(this.data.form);
    let {
      id,
    } = this.data.state.item;
    let {
      enableDefaultValue: checkType,
    } = this.data;


    this.putComplete({
      checkResult,
      id,
      checkType,
      success: () => {
        this.setData({
          submited: true,
        });
        wx.navigateBack();
      },
    });
  },

  switchNav: function (e) {
    const curr = e.target.dataset.current;
    this.resize({curr});
    this.checkCor();
  },

  //判断当前滚动超过一屏时，设置tab标题滚动条。
  checkCor: function () {
    if (this.data.currentTab > 3 && this.data.currentTab <= 5) {
      this.setData({
        scrollLeft: 200
      });
    } else if (this.data.currentTab > 5) {
      this.setData({
        scrollLeft: 400
      });
    } else {
      this.setData({
        scrollLeft: 0
      });
    }
  },

  switchTab: function (e) {
    const curr = e.detail.current;
    this.resize({curr});
    this.checkCor();
  },

  resize({
    curr,
  } = {}) {
    if (curr === undefined) {
      curr = this.data.currentTab;
    }

    let query = wx.createSelectorQuery();
    query.selectAll('.wp-check-detail__scroll-tb').boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(res => {
      let top = res[0][curr].top;
      let minSwiper = (wx.getSystemInfoSync().windowHeight - wx.getSystemInfoSync().screenWidth / 750 * (top - 136))/2;
      let swiper= res[0][curr].height + 8;
      swiper<minSwiper ? swiper=minSwiper : swiper;
      this.setData({
        swiperHeight: swiper,
        currentTab: curr,
      });
    });
  },
};

const mapStateToData = state => ({
  state: state.foster.dailyCheck.check.checkDetail.state,
  currTime: state.foster.dailyCheck.date.currTime,
  ableCheck: state.foster.dailyCheck.date.ableCheck,
  // enableDefaultValue: state.foster.dailyCheck.date.enableDefaultValue,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  putReset: acs.foster.dailyCheck.check.putReset,
  putComplete: acs.foster.dailyCheck.check.putComplete,
  setCheckDetail: acs.foster.dailyCheck.check.setCheckDetail,
  putInspection: acs.foster.dailyCheck.check.putInspection,
  acShowToast: acs.global.toast.show,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
