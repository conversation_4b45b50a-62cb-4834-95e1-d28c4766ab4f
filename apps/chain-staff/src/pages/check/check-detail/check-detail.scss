// wp-tag 样式已加载进来
.wp-check-detail {
  width: 670rpx;
  padding: 20rpx;
  margin: 20rpx;
  background-color: #fff;

  &__tag {
    margin-right: 8rpx;
  }

  &__all-normal {
    background-color: #23b9de;
    color: white;
    float: right;
    display: inline-block;
    padding: 0 36rpx;
    margin-right: -20rpx;
  }

  &__time {
    width: 670rpx;
    height: 92rpx;
    line-height: 92rpx;
    padding:0 20rpx;
    margin: 20rpx auto;
    background-color: #fff;
    font-size: 32rpx;

    &--disabled {
      color: #b0b0b0;
    }
  }

  &__special {
    margin-top: 20rpx;
  }

  &__tab {
    width: 670rpx;
    padding: 20rpx;
    margin: 20rpx;
    background-color: #fff;
  }

  &__navbar {
    display: flex;
  }
  // 滚动导航栏
  &__scroll {
    width: 100%;
    width: 710rpx;
    margin: 20rpx auto;
    background-color: #fff;

    &-navbar {
      display: flex;

      &-container {
        display: flex;
      }
    }

    &-item {
      flex: 0 0 2em;
    }

    &-tb-item {
      padding: 20rpx;

      input {
        border-bottom: 2rpx solid #f1f1f1;
        font-size: 28rpx;
        color: #9397a2;
        padding-left: 20rpx;
      }

      &-select {
        display: flex;
      }

      &-option{
        flex: 0 0 320rpx;
        width: 320rpx;
        height: 92rpx;
        margin-left: 15rpx;
        margin-top: 15rpx;
        line-height: 92rpx;
        border: 2rpx solid #e3e3e3;
        color: #23b9de;
        font-size: 36rpx;
        border-radius: 10rpx;
        text-align: center
      }

      &-head {
        font-size: 28rpx;
        color: #2f2f2f;

        &-switch {
          float: right;
        }
      }

      &-ins {
        color: #fb9e0f;
        font-size: 28rpx;
        margin-bottom: 36rpx;
      }
    }
  }

  &__btn-selected {
    background-color: #23b9de;
    color: #fff;
    border: none;
  }
}
// 滚动栏
.tab-h {
  height: 92rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 92rpx;
  background: #f7f7f7;
  font-size: 16px;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 2rpx solid #f1f1f1;
  border-top-right-radius: 8rpx;
  border-top-left-radius: 8rpx;
}

.tab-item {
  margin: 0 20rpx;
  display: inline-block;
  text-align: center;
}

.tab-item.active {
  color: #23b9de;
  position: relative;
  // position: relative;
}

.tab-item.active:after {
  content: '';
  display: block;
  height: 8rpx;
  width: 100%;
  background: #23b9de;
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 16rpx;
}

.item-ans {
  width: 100%;
  display: flex;
  flex-grow: row no-wrap;
  justify-content: space-between;
  padding: 30rpx;
  box-sizing: border-box;
  height: 180rpx;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  padding-right: 30rpx;
}

.avatar.img {
  width: 100%;
  height: 100%;
}

.avatar.doyen {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  bottom: -2px;
  right: 20rpx;
}

.expertInfo {
  font-size: 12px;
  flex-grow: 3;
  color: #b0b0b0;
  line-height: 1.5em;
}

.expertInfo .name {
  font-size: 16px;
  color: #000;
  margin-bottom: 6px;
}

.askBtn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 14px;
  border-radius: 60rpx;
  border: 1px solid #4675f9;
  color: #4675f9;
}

.scoll-h {
  height: 100%;
}

.component-check-header-container {
  margin: 20rpx 20rpx 0;
}

