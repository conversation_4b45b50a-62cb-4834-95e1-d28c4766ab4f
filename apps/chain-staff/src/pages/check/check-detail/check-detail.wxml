<view class="wp-page">
  <component-check-header></component-check-header>
  <view wx:if="{{ableCheck}}" class="wp-check-detail__time br-4">
    检查时段：{{currTime.phaseConfigStart}} - {{currTime.phaseConfigEnd}}
    <view wx:if="{{state && state.item && state.item.enableDefaultValue && !readonly}}" ontap="onAllNormalTap" class="wp-check-detail__all-normal">全部正常</view>
  </view>
  <view wx:else class="wp-check-detail__time wp-check-detail__time--disabled br-4">
    不在检查时段内
  </view>
  <view class="component-check-header-container">
    <component-check-list item="{{state.item}}"></component-check-list>
  </view>
  <view class="wp-check-detail br-4">
    <view class="wp-tag__container">
      <view class="wp-tag wp-check-detail__tag" wx:for="{{state.detail.extraArticles}}">{{item.name}}</view>
    </view>
    <view class="wp-check-detail__special">
      特殊需求：
      <text class="wp-check0-detail__special-content">{{state.detail.specialRequirement || '--'}}</text>
    </view>
  </view>
  <view class="wp-check-detail__scroll br-4">
    <scroll-view scroll-x="true" class="tab-h" scroll-left="{{scrollLeft}}">
      <view class="tab-item {{currentTab==0?'active':''}}" data-current="0" bindtap="switchNav">
        温湿度
      </view>
      <view class="tab-item {{currentTab==1?'active':''}}" data-current="1" bindtap="switchNav">
        喂食
      </view>
      <view class="tab-item {{currentTab==2?'active':''}}" data-current="2" bindtap="switchNav">
        饮水
      </view>
      <view class="tab-item {{currentTab==3?'active':''}}" data-current="3" bindtap="switchNav">
        用药
      </view>
      <view class="tab-item {{currentTab==4?'active':''}}" data-current="4" bindtap="switchNav">
        便便
      </view>
      <view class="tab-item {{currentTab==5?'active':''}}" data-current="5" bindtap="switchNav">
        笼子清扫
      </view>
      <view class="tab-item {{currentTab==6?'active':''}}" data-current="6" bindtap="switchNav">
        健康状态
      </view>
      <view class="tab-item {{currentTab==7?'active':''}}" data-current="7" bindtap="switchNav">
        皮肤
      </view>
      <view class="tab-item {{currentTab==8?'active':''}}" data-current="8" bindtap="switchNav">
        特殊情况
      </view>
      <view class="tab-item {{currentTab==9?'active':''}}" data-current="9" bindtap="switchNav">
        遛弯
      </view>
      <view class="tab-item {{currentTab==10?'active':''}}" data-current="10" bindtap="switchNav">
        拍照
      </view>
    </scroll-view>

    <swiper class="tab-content" current="{{currentTab}}" duration="300" bindchange="switchTab" style="height: {{swiperHeight}}px">
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.temperature}}"
              bindinput="bindInput"
              data-path="form.temperature"
              placeholder="温度(必填)"
              />
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.humidity}}"
              bindinput="bindInput"
              data-path="form.humidity"
              placeholder="湿度(必填)"
              />
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">有无余粮（必填）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasFood === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasFood"
                >
                有
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasFood === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasFood"
                >
                无
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.foodWeight}}"
              bindinput="bindInput"
              data-path="form.foodWeight"
              placeholder="喂食克数"
              />
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <view>罐头</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasTin === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasTin"
                >
                已喂
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasTin === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasTin"
                >
                未喂
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <view>零食</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasSnacks === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasSnacks"
                >
                已喂
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasSnacks === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasSnacks"
                >
                未喂
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <view>营养膏</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasNutrition === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasNutrition"
                >
                已喂
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasNutrition === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasNutrition"
                >
                未喂
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.foodRemark}}"
              bindinput="bindInput"
              data-path="form.foodRemark"
              placeholder="备注"
              />
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">饮水（必填）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasDrink === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasDrink"
                >
                正常
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasDrink === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasDrink"
                >
                异常
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.drinkRemark}}"
              bindinput="bindInput"
              data-path="form.drinkRemark"
              placeholder="异常情况"
              />
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view wx:for="{{state.detail.drugInstruction}}" class="wp-check-detail__scroll-tb-item-ins">{{item}}</view>
            <view class="wp-check-detail__scroll-tb-item-head">用药</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasDrug === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasDrug"
                >
                已用
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasDrug === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasDrug"
                >
                未用
              </view>

            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.drugRemark}}"
              bindinput="bindInput"
              data-path="form.drugRemark"
              placeholder="未用理由"
              />

          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">便便（必填）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasShit === 1 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasShit"
                >
                有便
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{form.hasShit === 0 && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasShit"
                >
                无便
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.shitRemark}}"
              bindinput="bindInput"
              data-path="form.shitRemark"
              placeholder="备注"
              />
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">笼子清扫（必填）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.clean === 0) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.clean"
                >
                无需打扫
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.clean === 1) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.clean"
                >
                已打扫
              </view>

            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <view>消毒(早晚各一次)</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.hasDisinfect === 1) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="form.hasDisinfect"
                >
                已消毒
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.hasDisinfect === 0) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="form.hasDisinfect"
                >
                未消毒
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">健康状态（必填）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.health === 0) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="{{'form.health'}}"
                >
                活跃
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.health === 1) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="{{'form.health'}}"
                >
                萎靡
              </view>

            </view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.health === 2) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{2}}"
                data-path="{{'form.health'}}"
                >
                安静
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.health === 3) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{3}}"
                data-path="{{'form.health'}}"
                >
                嗜睡
              </view>
            </view>
          </view>
          <view class="wp-check-detail__scroll-tb-item">
            <input
              disabled="{{readonly}}"
              class="weui-input"
              value="{{form.healthRemark}}"
              bindinput="bindInput"
              data-path="form.healthRemark"
              placeholder="备注"
              />
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">皮肤（早晚各一次）</view>
            <view class="wp-check-detail__scroll-tb-item-select">
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.skin === 1) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{1}}"
                data-path="{{'form.skin'}}"
                >
                有异常
              </view>
              <view
                class="wp-check-detail__scroll-tb-item-option {{(form.skin === 0) && 'wp-check-detail__btn-selected'}}"
                bindtap="onTapBtn"
                data-value="{{0}}"
                data-path="{{'form.skin'}}"
                >
                无异常
              </view>
            </view>
            <view class="wp-check-detail__scroll-tb-item">
              <input
                disabled="{{readonly}}"
                class="weui-input"
                value="{{form.skinRemark}}"
                bindinput="bindInput"
                data-path="form.skinRemark"
                placeholder="备注"
                />
            </view>
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">特殊情况</view>
            <view class="wp-check-detail__scroll-tb-item">
              <input
                disabled="{{readonly}}"
                class="weui-input"
                value="{{form.specialRemark}}"
                bindinput="bindInput"
                data-path="form.specialRemark"
                placeholder="特殊情况（选填）"
                />
            </view>
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">
              遛弯（早晚各一次）
              <switch
                disabled="{{readonly}}"
                class="wp-check-detail__scroll-tb-item-head-switch"
                bindchange="bindSwitchChange"
                data-path="form.video.checked"
                checked="{{form.video.checked}}"
                />
            </view>
            <view
              class="wp-check-detail__scroll-tb-item"
              wx:if="{{form.video.checked}}"
              >
              <component-upload
                editable="{{!readonly}}"
                hintText="上传视频"
                type="video"
                urls="{{form.video.srcs}}"
                bind:uploaded="onVideoUploaded"
                ></component-upload>
              <view wx:if="{{form.video.srcs.length === 0 && readonly}}" class="color-hint ta-c">无</view>
            </view>
          </view>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="wp-check-detail__scroll-tb" id="tab-{{index}}">
          <view class="wp-check-detail__scroll-tb-item">
            <view class="wp-check-detail__scroll-tb-item-head">拍照(必填)</view>
            <view class="wp-check-detail__scroll-tb-item">
              <view>
                <component-upload
                  editable="{{!readonly}}"
                  hintText="上传图片"
                  urls="{{form.photo.srcs}}"
                  bind:uploaded="onPhotoUploaded"
                  ></component-upload>
              </view>
              <view class="mt-16">
                <button class="weui-btn bc-p color-white {{form.photo.srcs.length === 0 ? 'bc-disabled' : ''}}" wx:if="{{state.item.status == 1}}" bindtap="onTapFinished">检查完成</button>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</view>
