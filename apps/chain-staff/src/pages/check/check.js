import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  store,
  getStorage,
} from '@petkit/redux';
import {
  pollingService,
} from 'services/polling.service';

const pageConfig = {
  data: {
    isShowExitModal: false,
  },

  onLoad(options) {
    // 进入小程序的时候获取到 storeId
    const storeId = options.storeId ? options.storeId : '';
    if (!isNaN(storeId) && Number(storeId)) {
      getStorage().setItem('storeId', storeId);
    }
  },

  onShow() {
    this.dispatchLodging();
  },

  dispatchLodging() {
    let {storeId} = this.data;
    if (storeId !== undefined) {
      this.getLodging({
        storeId: storeId,
      });
    }
  },

  onReady() {
    const isfromWechatWork = getStorage().getItem('isFromWechatWork');
    this.setData({
      isShowExitModal: !isfromWechatWork,
    });

    if (this.data.isShowExitModal) {
      wx.hideTabBar();
    }

    pollingService.add(this.dispatchLodging, {
      interval: 1500,
    });

    this.sub = store.subscribe(() => {
      let state = store.getState();
      let pageState = state.user.loginWp.pageState;
      if (pageState !== 3) {
        wx.redirectTo({
          url: '/pages/login/login',
          success: () => {
            if (this.sub) {
              this.sub();
            }
          }
        });
      }
    });
  },

  onUnload() {
    pollingService.remove(this.dispatchLodging);
  },

  onTapClock() {
    wx.switchTab({
      url: '/pages/attendance/clock/clock',
    });
  },

  onTapNotification() {
    this.acShowToast({
      message: '暂未开放～',
    });
  },

  onTapRedirectToCheckList() {
    wx.navigateTo({
      url: '/pages/check/check-list/check-list',
    });
  },

  onTapInventory() {
    const warehouseId = Number(getStorage().getItem('storeId'));
    if (isNaN(warehouseId) || warehouseId <= 0) {
      this.acShowToast({
        message: '请选择门店！',
      });
    } else {
      this.requestCheckingUser({
        warehouseId,
        mobile: this.data.mobile,
      }, {
        success: () => {
          wx.navigateTo({
            url: '/pages/web-view/food/web-view-food',
          });
        }
      });
    }
  },

  onStoreChange({
    detail: {
      storeId,
    },
  }) {
    this.setData({
      storeId: storeId,
    });
    this.getLodging({
      storeId: storeId,
    });
  },
};

const mapStateToData = state => {
  return {
    lodging: state.foster.dailyCheck.check.lodging,
    currDateTime: state.foster.dailyCheck.date.currDateTime,
    currTime: state.foster.dailyCheck.date.currTime,
    nextTime: state.foster.dailyCheck.date.nextTime,
    ableCheck: state.foster.dailyCheck.date.ableCheck,
    mobile: state.user.loginWp.mobile,
  }
};

const mapDispatchToPage = dispatch => bindActionCreators({
  getLodging: acs.foster.dailyCheck.check.getLodging,
  acShowToast: acs.global.toast.show,
  requestCheckingUser: acs.inventory.check.requestCheckingUser,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

