import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';
import {
  pollingService,
} from 'services/polling.service';

const pageConfig = {
  onShow() {
    this.dispatchGetCheckList();
  },
  dispatchGetCheckList() {
    if (this.data.storeId !== undefined) {
      this.getCheckList({
        storeId: this.data.storeId,
      });
    }
  },
  onReady() {
    pollingService.add(this.dispatchGetCheckList);
  },
  onUnload() {
    pollingService.remove(this.dispatchGetCheckList);
  },
  onTapCheckListItem({
    target,
  }) {
    const that = this;
    let item = target.dataset.item;
    try {
      let {id, status, checkerId} = item;
      this.getCheckUserWorkStatus({
        id,
        userId: checkerId
      }, {
        success: (res) => {
          if (that.data.checkStatus) { // 无人检查 可以进入
            if (status == 1 && checkerId != that.data.userId) {
              that.showError({
                message: '正在被别人检查中...',
                duration: 3000,
              });
              return;
            }
            if (status == 0 || status == 1) {
              wx.navigateTo({
                url: `/pages/check/check-detail/check-detail?id=${id}`,
              });
            } else {
              let {fosterId, petId} = item;

              wx.navigateTo({
                url: `/pages/check/uncheck-detail/uncheck-detail?fosterId=${fosterId}&petId=${petId}`,
              });
            }
          } else {
            that.showError({
              message: '正在被别人检查中...',
              duration: 3000,
            });
          }
        }
      });
    } catch (e) {
      console.log(e);
    }
  },

  onStoreChange({
    detail: {
      storeId,
    },
  }) {
    this.setData({
      storeId: storeId,
    });
    this.getCheckList({
      storeId: storeId,
    });
  },
  onReady() {
    this.getImLogin({client: 'weixin'});
  },
};

const mapStateToData = state => ({
  webImMsg: state.web.im.message,
  userId: state.user.loginWp.userId,
  list: state.foster.dailyCheck.check.checkList.list,
  currTime: state.foster.dailyCheck.date.currTime,
  currDateTime: state.foster.dailyCheck.date.currDateTime,
  ableCheck: state.foster.dailyCheck.date.ableCheck,
  checkStatus: state.foster.dailyCheck.check.checkStatus,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  getCheckList: acs.foster.dailyCheck.check.getCheckList,
  putInspection: acs.foster.dailyCheck.check.putInspection,
  showError: acs.global.toast.showError,
  getImLogin: acs.web.im.getImLogin,
  // 检查是否有员工在工作
  getCheckUserWorkStatus: acs.foster.dailyCheck.check.getCheckUserWorkStatus,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

