<view class="wp-page">
  <view class="wp-check-list">
    <component-check-header bind:storechange="onStoreChange"></component-check-header>
    <view wx:if="{{ableCheck}}" class="wp-check-list__time br-4">
      检查时段：<text class="wp-check-list__time-zone">{{currTime.phaseConfigStart}} - {{currTime.phaseConfigEnd}}</text>
    </view>
    <view wx:else class="wp-check-list__time wp-check-list__time--disabled br-4">
      不在检查时段内
    </view>
    <view class="wp-check-list__list">
      <component-check-list
        class="wp-check-list__item br-4"
        wx:for="{{list}}"
        wx:key="{{index}}"
        item="{{item.item}}"
        bindtap="onTapCheckListItem"
        data-item="{{item.item}}"
      ></component-check-list>
    </view>

    <view class="ta-c color-hint" wx:if="{{list.length === 0}}">暂无数据</view>
  </view>
</view>
