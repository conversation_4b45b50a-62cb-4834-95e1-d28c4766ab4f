import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';

const pageConfig = {
  data: {
  },
  onLoad(option) {
    wx.redirectTo({
      url: '/pages/check/check'
    });
  },
};

const mapStateToData = state => ({
});

const mapDispatchToPage = dispatch => bindActionCreators({
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

