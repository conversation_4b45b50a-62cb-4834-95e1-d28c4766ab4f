@import 'config/theme.config';

@mixin wp-login-theme($theme) {
  .wp-login {
    &__login-btn {
      margin-top: u(180);
      transition: background-color .5s;

      &--disabled {
        background-color: #ddd;
      }
    }

    &__background-image {
      width: 100%;
      height: 100%;
      display: block;
    }

    &__warn {
      font-size: u(28);
      text-align: left;
      margin-top: u(16);
      opacity: 0;
      transition: opacity .5s;
    }

    &__container {
      background-color: #fff;
    }

    &__wrapper {
      margin: 0 u(80);
    }

    &__logo {
      margin: u(100) auto u(200);
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      &-img{
        width: u(412);
        height: u(92);
      }
      &-text{
        font-size: u(28);
        margin-top: u(16);
        color: red;
      }
    }

    &__input {
      &-mobile-container {
        display: flex;
        align-items: flex-end;
      }

      &-mobile {
        width: u(400);
        flex-grow: 1;
      }
    }

    &__validation {
      width: u(180);
      white-space: nowrap;
      font-size: u(32);
      margin: 0 0 0 u(16);
      color: #23b9de;

      &--disabled {
        color: rgba(0, 0, 0, .38);
      }
    }
    &__tip-text{
      width: 100%;
      text-align: center;
      font-size: u(28);
      color: red;
      margin-top: u(16);
    }
  }
}


@mixin wp-home-theme($theme) {
  .wp-home {
    &__swiper {
      height: 100%;
      text-align: center;

      &-image {
        margin: u(120) auto u(80);
        width: u(500);
        height: u(500);
      }

      &-title {
        font-size: u(36);
        color: #2e4153;
        font-weight: bold;
      }

      &-text {
        margin-top: u(24);
        font-size: u(32);
        color: #9397a2;
      }

      &-btn {
        margin: u(100) u(70);
      }
    }
  }
}


@include wp-home-theme($app-theme);
@include wp-login-theme($app-theme);
