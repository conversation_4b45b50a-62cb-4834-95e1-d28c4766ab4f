import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  store,
  acs,
} from '@petkit/redux';
import MDInput from 'utils/md/mdinput/mdinput.js';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    mobile: '',
    validation: '',
    remainTime: 0,
    inputs: [{
      mdInput: {
        mdi_num_range: '11',
        mdi_float_label: '输入手机号',
        style_mdi_float_up: 'color: #23b9de;',
        style_mdi_border_focus: 'border-bottom: 1px solid #23b9de;',

        hideFooter: 'true',

        onMDInput: 'onColumn0MDInput',
        onMDIBlur: 'onColumn0MDIBlur',
        bindInput: 'bindMobileInput',
      }
    }, {
      mdInput: {
        mdi_num_range: '4',
        mdi_float_label: '输入验证码',
        style_mdi_border_focus: 'border-bottom: 1px solid #23b9de;',
        style_mdi_float_up: 'color: #23b9de;',

        hideFooter: 'true',

        onMDInput: 'onColumn1MDInput',
        onMDIBlur: 'onColumn1MDIBlur',
        bindInput: 'bindValidationInput',
      }
    }]
  },
  onLoad() {
    MDInput.putData(this.data.inputs);
    let redirected = false;
    if (this.data.redirectTo === 3) {
      wx.redirectTo({
        url: '/pages/check/check'
      });
    }
    this.sub = store.subscribe(() => {
      let state = store.getState();
      let pageState = state.user.loginWp.pageState;
      if (pageState === 3 && !redirected) {
        this.onTapToHome()
        redirected = true;
      }

      if (pageState === 2) {
        // wx.setNavigationBarTitle({
        //   title: '手机验证码登录',
        // });
      }
      if (pageState === 1) {
        // wx.setNavigationBarTitle({
        //   title: '',
        // });
      }
    });
  },
  // input
  bindMobileInput({detail}) {
    this.setData({
      mobile: detail.value,
      isNotStaff: 0,
    });
  },
  bindValidationInput({detail}) {
    this.setData({
      validation: detail.value,
      isErrValidation: 0,
    });
  },
  // 元气满满
  onTapGetPhoneNumber(e) {
    console.log(e);
    let {
      encryptedData,
      iv,
    } = e.detail;

    if (encryptedData && iv) {
      this.loginByWeappMobile({
        encryptedData: encodeURIComponent(encryptedData) || null,
        iv: encodeURIComponent(iv) || null,
        sessionKeyToken: this.data.sessionKeyToken,
      });
    } else {
      wx.showToast({
        title: '已拒绝授权，请重新授权手机号！',
        icon: 'none',
        duration: 1500
      });
    }
  },
  // tap
  onTapGetValidation() {
    if (this.data.remainTime === 0) {
      let value = MDInput.getValue();

      this.loginGetValidation({
        mobile: value[0],
      });

      this.setData({
        remainTime: 60,
      });
      this.decValidationNumber();
    }
  },
  decValidationNumber() {
    setTimeout(() => {
      this.setData({
        remainTime: this.data.remainTime - 1,
      });

      if (this.data.remainTime > 0) {
        this.decValidationNumber();
      }
    }, 1000);
  },

  onTapLogin() {
    if (!this.data.isNotStaff) {

      let value = MDInput.getValue();

      this.loginByMobile({
        mobile: value[0],
        validation: value[1],
        sessionKeyToken: this.data.sessionKeyToken,
      });
    } else {
      this.acShowToast({
        message: '该手机号码不是小佩员工，请更换手机号码！',
      });
    }
  },

  onTapToHome() {
    wx.switchTab({
      url: '/pages/check/check',
      success: (info) => {this.sub();},
      fail: (err) => {console.log('fail', err);},
    });
  },
};

const mapStateToData = state => ({
  pageState: state.user.loginWp.pageState,
  isNotStaff: state.user.loginWp.isNotStaff,
  isErrValidation: state.user.loginWp.isErrValidation,
  sessionKeyToken: state.user.loginWp.sessionKeyToken,
  name: state.user.loginWp.userName,
  userId: state.user.loginWp.userId,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  loginWeapp: acs.user.loginWp.weapp,
  loginByWeappMobile: acs.user.loginWp.byWeappMobile,
  loginGetValidation: acs.user.loginWp.getValidation,
  loginByMobile: acs.user.loginWp.byMobile,
  acShowToast: acs.global.toast.show,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
