<import src="../../component/md/mdinput/mdinput.wxml"/>

<view class='wp-page wp-login__container'>
  <view class="wp-login__wrapper" wx:if="{{pageState === 2}}">
    <view class='wp-login__logo'>
      <image src='https://img3.petkit.cn/images/aa9d6aaa8e63400d949fcd68d4d1a540' class='wp-login__logo-img'></image>
      <text class="wp-login__logo-text">仅限小佩宠物门店员工内部使用</text>
    </view>

    <view class="wp-login__input-mobile-container">
      <view class="wp-login__input-mobile">
        <template is="mdInput" data="{{...inputs[0].mdInput}}"/>
      </view>
      <view
        class="wp-login__validation {{ remainTime !== 0 ? 'wp-login__validation--disabled' : ''}}"
        bindtap="onTapGetValidation"
        >{{remainTime === 0 ? '获取验证码' : '重新发送(' + remainTime + ')'}}
      </view>
    </view>

    <view style="opacity: {{isNotStaff && mobile ? 1 : 0}}" class="color-w wp-login__warn">
      请输入企业微信绑定的手机号
    </view>

    <template is="mdInput" data="{{...inputs[1].mdInput}}"/>

    <view style="opacity: {{isErrValidation && validation ? 1 : 0}}" class="color-w wp-login__warn">
      验证码输入错误
    </view>

    <button
      class="bc-p color-white wp-login__login-btn {{isNotStaff || isErrValidation || !validation || !mobile ? 'wp-login__login-btn--disabled' : ''}}"
      bindtap="onTapLogin"
    >登录</button>
   <view class="wp-login__tip-text">
     <text >非门店员工留档手机号无法登录</text>
   </view>
  </view>

  <block wx:if="{{pageState === 1}}">
    <swiper
      class="wp-home__swiper"
      indicator-color="#ebebeb"
      indicator-active-color="#23b9de"
      indicator-dots="true"
      duration="300"
    >
      <swiper-item>
        <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.yYfZoStqIxYT0d02bea00eff079e87a0295cfa7d25c4.png" class="wp-home__swiper-image"/>
        <view class="wp-home__swiper-title">
          专心提供精致的服务
        </view>
        <view class="wp-home__swiper-text">
          让顾客放心，萌宠欢心
        </view>
      </swiper-item>
      <swiper-item>
        <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.PhqHbNX3KhW824a338cee76e8170bc6196f4477dda73.png" class="wp-home__swiper-image"/>
        <view class="wp-home__swiper-title">
          精心挑选海量的商品
        </view>
        <view class="wp-home__swiper-text">
          给顾客暖心推荐，诚信安利
        </view>
        <button
          class="wp-home__swiper-btn bc-p color-white"
          open-type="getPhoneNumber"
          bindgetphonenumber="onTapGetPhoneNumber"
        >开启元气满满的一天</button>
      </swiper-item>
    </swiper>
  </block>
</view>

