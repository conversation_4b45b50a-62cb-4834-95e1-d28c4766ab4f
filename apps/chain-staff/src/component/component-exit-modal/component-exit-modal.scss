@import 'config/theme.config';

@mixin wp-crown-component-exit-modal-theme($theme) {
  .wp-crown-component-exit-modal {
    &__wrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      height: u(240);
      padding-top: u(60);
      border-radius: u(8);

      background-color: #fff;
    }

    &__msg {
      text-align: center;
      font-size: u(32);
      color: #2f2f2f;
    }

    &__button {
      display: flex;
      justify-content: center;
      align-items: center;

      height: u(85);
      border-top: u(2) solid #ebebeb;

      font-size: u(32);
      color: #2f2f2f;
    }




  }
}

@include wp-crown-component-exit-modal-theme($app-theme);