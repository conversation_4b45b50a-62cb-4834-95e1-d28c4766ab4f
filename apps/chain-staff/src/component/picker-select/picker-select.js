import {
  isNumber,
} from 'lodash-es';

Component({
  properties: {
    mode: {
      type: String,
      // 'selector' | 'multiSelector'
      value: 'selector',
    },
    value: {
      type: Number,
      value: '',
      observer(nV, oV) {
        if (nV && nV !== oV) {
          this.setData({
            value: nV,
          });

          this.valueChange(this.data.value, 'auto');
        }
      },
    },
    range: {
      type: Array,
      value: [],
      observer(nV) {
        if (nV) {
          this.setData({
            range: nV,
          });

          this.valueChange(this.data.value, 'auto');
        }
      },
    },
    rangeKey: {
      type: String,
      value: null,
    },
    placeholder: {
      type: String,
      value: '',
    },
    disabled: {
      type: Boolean,
      value: false
    },
  },
  data: {
    text: '',
  },
  ready() {
    if (isNumber(this.data.value)) {
      this.valueChange(this.data.value);
    }
  },
  methods: {
    valueChange(value, type) {
      const data = this.data;

      if (data.range.length > 0 && value > -1) {
        const row = data.range[value];
        data.text = (data.rangeKey ? row[data.rangeKey] : row) || '';
        data.value = value;

        this.triggerEvent('change', {value, type});
      } else {
        data.text = '';
        data.value = -1;
      }
      this.setData(data);


    },
    onPickerChange({
      detail: {
        value,
      }
    }) {
      this.valueChange(value, 'manual');
    },
  }
});

