@import 'config/theme.config';
@import '~@petkit/style/icon/iconfont';

@mixin wp-comp-upload-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .wp-comp-upload {
    &__container {
      display: block;
      overflow: auto;
    }

    &__item {
      &-container {
        $width: u(280);
        display: inline-block;
        float: left;
        width: $width;
        height: $width;
        border: 1px solid map-get($foreground, divider);
        margin: u(8);

        &--small {
          width: 40px;
          height: 40px;
        }
      }

      &-video {
        width: 100%;
        height: 100%;
      }

      &-camera-img {
        width: 40px;
        height: 40px;
      }

      &-create {
        @extend .wp-comp-upload__item-container;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icon-add {
          font-size: u(56);
        }

        &--small {
          position: relative;
          width: 40px;
          height: 40px;

          image {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

@include wp-comp-upload-theme($app-theme);
@include iu-icon-iconfont-theme($app-theme);

