<view class="wp-comp-upload__container">
  <view wx:for="{{items}}" class="wp-comp-upload__item-container {{type === 'imgFromCamera' ? 'wp-comp-upload__item-container--small' : ''}}">
    <block wx:if="{{type === 'video'}}">
      <video class="wp-comp-upload__item-video" src="{{item.tempFilePath || item.url}}"></video>
    </block>
    <block wx:elif="{{type === 'imgFromCamera'}}">
      <image class="wp-comp-upload__item-camera-img" src="{{item.tempFilePath || item.url}}"></image>
    </block>
    <block wx:else>
      <image class="wp-comp-upload__item-video" src="{{item.tempFilePath || item.url}}"></image>
    </block>
  </view>
  <view wx:if="{{smallImg && !items.length}}" class="wp-comp-upload__item-create--small" bindtap="onCreateTap">
    <image src="../../images/attendance/clock-map/upload-camera-img.svg" />
  </view>
  <view wx:if="{{editable}}" class="wp-comp-upload__item-create" bindtap="onCreateTap">
    <text class="PETKIT icon-add"></text>
    {{hintText}}
  </view>
</view>

