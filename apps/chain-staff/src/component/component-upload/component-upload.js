// import {
//   getClient
// } from 'utils/ali-oss';
//
import {
  acs,
  store,
} from '@petkit/redux';

Component({
  properties: {
    type: {
      type: String,
      value: 'image',
    },
    editable: {
      type: Boolean,
      value: true,
    },
    hintText: {
      type: String,
      value: '添加',
    },
    urls: {
      type: Array,
      value: [],
    },
    smallImg: {
      type: Boolean,
      value: false,
    }
  },
  data: {
    items: [],
  },
  ready() {
    this.setData({
      items: this.data.urls,
    });
  },

  created() {
    store.dispatch(acs.global.oss.getOssEnv());
  },

  methods: {
    policyIsNull(policy, accessKeyId, signature, host) {
      if ( !policy || !accessKeyId || !signature || !host ) {
        wx.showModal({
          title: '提示',
          content: '网络异常，请稍后再试',
          success (res) {
            if (res.confirm) {
              console.log('用户点击确定')
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        });
        return true;
      } else {
        return false;
      }
    },
    uploadFile({
      tempFilePath,
    }) {
      console.log(tempFilePath);
      const ossEnv = store.getState().global.oss;
      const {policy, accessKeyId, signature, dir, host} = ossEnv;

      if(this.policyIsNull(policy, accessKeyId, signature, host)) return;
      // let host = 'http://img3.petkit.com';
      // let host = 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com';
      // let policy = 'eyJleHBpcmF0aW9uIjoiMjAyMC0wMS0wMVQxMjowMDowMC4wMDBaIiwiY29uZGl0aW9ucyI6W1siY29udGVudC1sZW5ndGgtcmFuZ2UiLDAsMTA0ODU3NjAwMF1dfQ==';

      // let keyId =  'LTAIsS7QJff2EDV6';
      // let signature = 'rTNqoK2xWUniLWYyCRa9YmPYzOg=';
      let fileName = tempFilePath.slice(tempFilePath.lastIndexOf('/') + 1);
      let key = `${dir}${fileName}`;

      wx.uploadFile({
        url: host,
        filePath: tempFilePath,
        name: 'file',
        formData: {
          name: fileName,
          key,
          policy,
          OSSAccessKeyId: accessKeyId,
          success_action_status: '200',
          signature,
        },
        success: (res) => {
          let url = `${host}/${dir}${fileName}`;


          if(res.statusCode !== 200) {
            wx.showModal({
              title: '提示',
              content: '暂时不能更改头像，请稍后再试',
              success (res) {
                if (res.confirm) {
                  console.log('用户点击确定')
                } else if (res.cancel) {
                  console.log('用户点击取消')
                }
              }
            })
            return;
          }

          this.setData({
            items: [
              ...this.data.items, {
                tempFilePath,
                url,
              }
            ]
          });

          this.triggerEvent('uploaded', this.data.items.map(v => ({url: v.url})));
        },
        fail: (res) => {
          console.log('fail', res);
          wx.showModal({
            title: '提示',
            content: '网络异常，请稍后再试',
            success (res) {
              if (res.confirm) {
                console.log('用户点击确定')
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            }
          })
        }
      });
    },

    onCreateTap() {
      switch (this.data.type) {
        case 'video': {
          wx.chooseVideo({
            success: ({
              tempFilePath
            }) => {
              this.uploadFile({
                tempFilePath,
              });
            },
            fail: (res) => {
              console.log(res);
            },
          });
          break;
        }
        case 'imgFromCamera':
          wx.chooseImage({
            count: 1,
            sizeType: ['original'],
            sourceType: ['camera'],
            success: ({
              tempFilePaths
            }) => {
              tempFilePaths.map(tempFilePath => this.uploadFile({
                tempFilePath,
              }));
            },
            fail: (res) => {
              console.log('chooseImage fail', res);
            },
          });
          break;
        default: {
          wx.chooseImage({
            success: ({
              tempFilePaths
            }) => {
              tempFilePaths.map(tempFilePath => this.uploadFile({
                tempFilePath,
              }));
            },
            fail: (res) => {
              console.log(res);
            },
          });
        }
      }
    },
  }
});

