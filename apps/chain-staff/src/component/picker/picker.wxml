<view class="mp-picker">

  <view class="mp-picker__input" bindtap="pickerHandler">{{value}}</view>

  <view class="mp-picker__view-container {{pickerShow}}">
    <view class="mp-picker__title">
      <view class="mo-picker__cancel" bindtap="pickerCancel"> 取消 </view>
      <view class="mp-picker__finish" bindtap="pickerFinish"> 确定</view>
    </view>
    <picker-view
      class="mp-picker__picker-view"
      indicator-class="mp-picker__indicator"
      value="{{number}}"
      bindchange="bindChange">
      <picker-view-column>
        <view
          wx:for="{{range}}"
          wx:key="{{index}}"
          class="mp-picker__column">
          {{item}}
        </view>
      </picker-view-column>
    </picker-view>
  </view>
  <view class="mp-picker__mask {{pickerShow}}" bindtap="pickerHandler"></view>

</view>
