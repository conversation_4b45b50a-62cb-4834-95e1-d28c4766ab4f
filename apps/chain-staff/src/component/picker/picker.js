Component({
  properties: {
    pickerShow: {
      type: Boolean,
      value: false,
    },
    range: {
      type: Array,
      value: []
    },
    number: {
      type: Array,
      value: []
    },
    value: {
      type: String,
      value: '',
    }
  },
  ready() {
    this.data.range.forEach(item => {
      if (item.value === this.data.value);
    });
    let range = this.data.range;
    let number = 0;
    for (let index in range) {
      if (range[index] === this.data.value) {
        number = index;
      }
    }
    this.setData({
      number: [number],
    });
  },
  methods: {
    pickerHandler() {
      this.setData({pickerShow: !this.data.pickerShow});
    },
    bindChange(e) {
      const index = e.detail.value[0];
      this.setData({value: this.data.range[index]});
    },
    pickerFinish() {
      this.triggerEvent('finish', {value: this.data.value});
      this.pickerHandler();
    },
    pickerCancel() {
      this.pickerHandler();
      this.triggerEvent('cancel', {});
    }
  }
});
