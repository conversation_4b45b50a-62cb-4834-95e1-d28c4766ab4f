import moment from 'moment';

Component({
  properties: {
    value: {
      type: String,
      // 格式 YYYY-MM-DD
      value: moment().format('YYYY-MM-DD'),
      observer(nV, oV) {
        if (nV && nV !== oV) {
          this.valueChange(nV);
        }
      }
    },
    displayFormat: {
      type: String,
      value: 'MM.DD',
    }
  },
  data: {
  },
  ready() {
    if (!this.data.value) {
      this.valueChange(this.data.value);
    }
  },
  methods: {
    valueChange(value) {
      this.setData({
        value,
        displayValue: moment(value).format(this.data.displayFormat),
      });

      this.triggerEvent('change', value);
    },
    onValueChange({
      detail: {
        value,
      }
    }) {
      this.valueChange(value);
    },
    onPrevTap() {
      const value = moment(this.data.value).subtract(1, 'd').format('YYYY-MM-DD');

      this.valueChange(value);
    },
    onNextTap() {
      const value = moment(this.data.value).add(1, 'd').format('YYYY-MM-DD');

      this.valueChange(value);
    }
  }
});

