<view class="mp-table__container" bind:touchstart="touchS" bind:touchmove="touchM" bind:touchend="touchE">
  <view class="mp-table__def">
    <view class="mp-table__def-col">
      {{options.def.col}}
    </view>
    <view class="mp-table__def-row">
      {{options.def.row}}
    </view>
    <view class="mp-table__def-divider"></view>
  </view>

  <view class="mp-table__col-wrapper">
    <view class="mp-table__col" style="transform: translateX({{touch.X + touch.offsetX}}px) translateZ(0);">
      <view class="mp-table__col-item" wx:key="{{item + index}}" wx:for="{{options.cols}}" bind:tap="onColTap" data-col="{{item}}">
        {{item}}
      </view>
    </view>
  </view>

  <view class="mp-table__row-wrapper">
    <view class="mp-table__row" style="transform: translateY({{touch.Y + touch.offsetY}}px) translateZ(0);">
      <view class="mp-table__row-item" style="{{options.cell.rowStyle || ''}}" wx:key="{{item + index}}" wx:for="{{options.rows}}" bind:tap="onRowTap" data-row="{{item}}">
        <view class="mp-table__row-item-text">{{item.text || item}}</view>
        <view wx:if="{{item.week}}" class="mp-table__row-item-text"> {{item.week}}</view>
        <image class="mp-table__row-item-triangle" wx:if="{{item.hasTriangle && item.type === 'abnormal'}}" src="../../images/attendance/schedule/triangle-abnormal.svg"></image>
        <image class="mp-table__row-item-triangle" wx:if="{{item.hasTriangle && item.type === 'normal'}}" src="../../images/attendance/schedule/triangle.svg"></image>
        <view class="mp-table__row-item-bar" wx:if="{{item.hasBar}}"></view>
      </view>
    </view>
  </view>

  <view class="mp-table__data-wrapper">
    <view class="mp-table__data" style="transform: translate({{touch.X + touch.offsetX}}px, {{touch.Y + touch.offsetY}}px) translateZ(0);">
      <view class="mp-table__data-row" wx:key="{{row + rowIndex}}" wx:for="{{data}}" wx:for-item="row" wx:for-index="rowIndex">
        <view
          class="mp-table__data-col {{col.class}}"
          style="background-color: {{col.disabled ? '#DDD' : col.bgc}}"
          wx:key="{{col + colIndex}}"
          wx:for="{{row}}"
          wx:for-item="col"
          wx:for-index="colIndex"
          bind:tap="onCellTap"
          data-cell="{{col}}"
        >
          <block wx:if="{{!col.disabled}}">
            <block wx:if="{{options.cell && options.cell.editOption}}">
              <block wx:if="{{options.cell.editOption.type === 'picker'}}">
                <template is="picker-cell" data="{{...options.cell.editOption, col: col}}"/>
              </block>
              <block wx:else>
                <template is="cell" data="{{...col, timeline}}"/>
              </block>
            </block>
            <block wx:else>
              <template is="cell" data="{{...col, timeline}}"/>
            </block>
          </block>
        </view>
      </view>
    </view>
  </view>

</view>

<template name="cell">
  <block wx:if="{{timeline}}">
    <view class="mp-table__data-cell-timeline-container {{type === 'abnormal' && 'mp-table__data-cell-timeline-container--abnormal'}}">
      <view class="mp-table__data-cell-timeline-line">
        <view class="mp-table__data-cell-timeline-prev" wx:if="{{hasPrev}}"></view>
        <view class="mp-table__data-cell-timeline-dot" wx:if="{{hasDot}}"></view>
        <view class="mp-table__data-cell-timeline-next" wx:if="{{hasNext}}"></view>
      </view>
      <view class="mp-table__data-cell-timeline-text">
        {{content}}
      </view>
      <view class="mp-table__data-cell-timeline-graphic">
        <view class="mp-table__data-cell-timeline-changed" wx:if="{{isChanged}}"></view>
        <image class="mp-table__data-cell-timeline-triangle" wx:if="{{hasTriangle}}" src="../../images/attendance/schedule/triangle.svg"></image>
      </view>
    </view>
    <view class="mp-table__data-cell-timeline-container" wx:if="{{hasEatTime}}">
      <view class="mp-table__data-cell-timeline-line">
         <view class="mp-table__data-cell-timeline-prev {{hasEatTime === true ? 'mp-table__data-cell-timeline-prev--eat-line' : ''}}"></view>
         <view class="mp-table__data-cell-timeline-dot {{hasEatTime === true ? 'mp-table__data-cell-timeline-dot--eat-dot' : ''}}"></view>
        <view class="mp-table__data-cell-timeline-next {{hasEatTime === true ? 'mp-table__data-cell-timeline-next--eat-next' : ''}}"></view>
      </view>
      <view class="mp-table__data-cell-timeline-text {{hasEatTime === true ? 'mp-table__data-cell-timeline-text--eat-word' : ''}}">
        吃饭
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="mp-table__data-cell">
      <view wx:if="{{type}}" class="mp-table__data-cell-type {{type.class || ''}}">{{type.content || ''}}</view>
      {{content}}
    </view>
  </block>
</template>

<template name="picker-cell">
  <picker
    mode="{{mode}}"
    value="{{value}}"
    range="{{range}}"
    range-key="{{rangeKey}}"
    data-test="{{value}}"
    disabled="{{!col.editable}}"
    bindchange="pickerChange"
    bindcolumnchange="pickerColumnChange"
    bindcancel="pickerCancel"
    data-cell="{{col}}"
  >
    <template is="cell" data="{{...col}}"/>
  </picker>
</template>
