import {cloneDeep} from 'lodash-es';
Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    options: {
      type: Object,
      value: {
        def: {
          row: '',
          col: '',
        },
        // string[]
        rows: [],
        // string[]
        cols: [],
        cell: {
          // {
          //   type,
          //   mode,
          //   value,
          //   range,
          //   rangeKey
          // }
          editOption: {},
          rowStyle: '',
        },
      },
    },
    timeline: {
      type: Boolean,
      value: false,
    },
    animation: {
      type: Object,
      value: {
        // 'page' | 'frame'
        type: 'page',
      },
    },
    data: {
      type: Array,
      // {
      //   content,
      //   // 是否可编辑啊
      //   editable,
      //   // 启用禁用
      //   disabled,
      //   // cell类
      //   class,
      //   // cell背景
      //   bgc,
      //   // cell类型，小图标
      //   type: {
      //     class,
      //     content,
      //   }
      // }

      // {
      //   type: 'normal' | 'abnormal';
      //   isChanged: boolean;
      //   hasTriangle: boolean;
      //   hasPrev: boolean;
      //   hasNext: boolean;
      //   hasDot: boolean;
      // }
      value: [],
    }
  },
  data: {
    touch: {
      colWidth: 140,
      rowHeight: 78,

      X: 0,
      Y: 0,
      offsetX: 0,
      offsetY: 0,
    },
    split: 20,
    cellWidth: 70,
    cellHeight: 39,
  },
  ready() {
    const initQuery = () => {
      wx.createSelectorQuery().in(this).select('.mp-table__data-wrapper').boundingClientRect(rect => {
        if (!rect) {
          setTimeout(() => initQuery(), 100);
          return;
        }

        const wrapperWidth = rect.width;
        const wrapperHeight = rect.height;

        this.setData({
          wrapperWidth,
          wrapperHeight,
        });
      }).exec();

      wx.createSelectorQuery().in(this).select('.mp-table__data-col').boundingClientRect(rect => {
        if (!rect) {
          setTimeout(() => initQuery(), 100);
          return;
        }

        const cellWidth = rect.width;
        const cellHeight = rect.height;

        this.setData({
          cellWidth,
          cellHeight,
        });
      }).exec();

      const {
        options,
      } = this.data;

      let indexRow = 0;
      if (options) {
        options.rows.forEach((item, key) => {
          if (item.hasBar) {
            indexRow = key;
          }
        });
      }
      // 每一个屏幕的个数
      const screenHeightCount = Math.floor(this.data.wrapperHeight / this.data.cellHeight) - 1;
      // 所在屏幕
      const indexHeightCount = Math.ceil(indexRow / screenHeightCount);
      // 偏移的 Y = (所在屏幕 - 1) * 每一个屏幕偏移的高度
      const Y = indexRow > 0 ? (indexHeightCount - 1) * (screenHeightCount - 1) * this.data.cellHeight : 0;

      let touch = this.data.touch;
      touch.Y = -Y;
      this.setData({
        touch,
      });
    };

    initQuery();
  },
  methods: {
    // 点击事件
    onCellTap({
      currentTarget: {
        dataset: {
          cell,
        }
      }
    }) {
      if (!cell.disabled) {
        this.triggerEvent('celltap', cell);
      }
    },
    onRowTap({
      currentTarget: {
        dataset: {
          row,
        }
      }
    }) {
      this.triggerEvent('rowtap', row);
    },
    onColTap({
      currentTarget: {
        dataset: {
          col,
        }
      }
    }) {
      this.triggerEvent('coltap', col);
    },

    // picker
    pickerChange({
      currentTarget: {
        dataset: {
          cell
        }
      },
      detail: {
        value,
      }
    }) {
      this.triggerEvent('pickerchange', {cell, index: value});
    },
    pickerColumnChange({
      currentTarget: {
        dataset: {
          cell
        }
      }
    }) {
      this.triggerEvent('pickercolumnchange', cell);
    },
    pickerCancel({
      currentTarget: {
        dataset: {
          cell
        }
      }
    }) {
      this.triggerEvent('pickercancel', cell);
    },


    touchS: function ({
      touches
    }) {
      const {
        animation,
      } = this.data;

      switch (animation.type) {
        case 'frame':
          this._frameS(touches);
          break;
        case 'page': {
          this._pageS(touches);
          break;
        }
        default:
          break;
      }
    },
    //触摸时触发，手指在屏幕上每移动一次，触发一次
    touchM: function ({
      touches
    }) {
      const {
        animation,
      } = this.data;

      switch (animation.type) {
        case 'frame': {
          this._frameM(touches);
          break;
        }
        case 'page':
          this._pageM(touches);
          break;
        default:
          return;
      }
    },
    touchE: function ({
      changedTouches,
    }) {
      const {
        animation,
      } = this.data;

      switch (animation.type) {
        case 'frame': {
          this._frameE(changedTouches);
          break;
        }
        case 'page': {
          this._pageE(changedTouches);
          break;
        }
        default:
          break;
      }
    },
    _frameS(touches) {
      if (touches.length == 1) {
        const touch = touches[0];

        let startX = touch.clientX;
        let startY = touch.clientY;

        this.setData({
          startX,
          startY,
        });
      }
    },
    _frameM(touches) {
      if (touches.length==1) {
        const _touch = touches[0];
        //记录触摸点位置的X坐标
        let moveX = _touch.clientX;
        let moveY = _touch.clientY;
        //计算手指起始点的X坐标与当前触摸点的X坐标的差值
        let offsetX = moveX - this.data.startX;
        let offsetY = moveY - this.data.startY;

        const touch = this.data.touch;

        touch.offsetX = offsetX;
        touch.offsetY = offsetY;

        this.setData({
          touch,
        });
      }
    },
    _frameE(changedTouches) {
      if (changedTouches.length==1) {
        const changedTouch = changedTouches[0];
        //手指移动结束后触摸点位置的X坐标
        let endX = changedTouch.clientX;
        let endY = changedTouch.clientY;
        //触摸开始与结束，手指移动的距离
        let offsetX = endX - this.data.startX;
        let offsetY = endY - this.data.startY;

        const touch = this.data.touch;
        // cell size
        const w = touch.colWidth;
        const h = touch.rowHeight;

        // round
        touch.X += Math.round(offsetX / w) * w;
        touch.Y += Math.round(offsetY / h) * h;

        // reset
        touch.offsetX = 0;
        touch.offsetY = 0;

        // check
        if (touch.X > 0) {
          touch.X = 0;
        }
        if (touch.Y > 0) {
          touch.Y = 0;
        }

        this.setData({
          touch,
        });
      }
    },
    _pageS(touches) {
      if (touches.length == 1) {
        const touch = touches[0];

        let startX = touch.clientX;
        let startY = touch.clientY;

        this.isScrolled = false;

        this.setData({
          startX,
          startY,
        });
      }
    },
    _pageM(touches) {
      if (this.isScrolled) {
        return;
      }

      if (touches.length==1) {
        const _touch = touches[0];
        //记录触摸点位置的X坐标
        let moveX = _touch.clientX;
        let moveY = _touch.clientY;
        //计算手指起始点的X坐标与当前触摸点的X坐标的差值
        let offsetX = moveX - this.data.startX;
        let offsetY = moveY - this.data.startY;

        if (Math.abs(offsetX) > this.data.split || Math.abs(offsetY) > this.data.split) {
          this._pageE(touches);
        }
      }
    },
    _pageE(changedTouches) {
      if (this.isScrolled) {
        return;
      }

      const {
        cellWidth,
        cellHeight,
        wrapperWidth,
        wrapperHeight,
        options,
      } = this.data;
      let rowCounts = options.rows.length;
      let colCounts = options.cols.length;

      // 每一个屏幕高度能放几个完整的格子
      let screenHeightCount = Math.floor(wrapperHeight / cellHeight);
      // 每一个屏幕能显示几个完整的宽度格子
      let screenWidthCount = Math.floor(wrapperWidth / cellWidth);
      let totalOffsetHeight = (Math.ceil(rowCounts / (screenHeightCount-1)) - 1) * (screenHeightCount * cellHeight);
      let totalOffsetWidth =  (Math.ceil(colCounts / (screenWidthCount -1)) - 1) * (screenWidthCount * cellWidth);

      if (changedTouches.length == 1) {
        const changedTouch = changedTouches[0];
        //手指移动结束后触摸点位置的X坐标
        let endX = changedTouch.clientX;
        let endY = changedTouch.clientY;
        //触摸开始与结束，手指移动的距离
        let offsetX = endX - this.data.startX;
        let offsetY = endY - this.data.startY;
        const touch = this.data.touch;
        let temp = cloneDeep(touch);

        let X = Math.abs(offsetX) < this.data.split - 5 ? 0 : offsetX;
        let Y = Math.abs(offsetY) < this.data.split - 5 ? 0 : offsetY;
        X = X === 0 ? 0 : X / Math.abs(X);
        Y = Y === 0 ? 0 : Y / Math.abs(Y);

        if (X !== 0 || Y !== 0) {
          wx.showLoading();
        }

        temp.Y += Y * (screenHeightCount - 1) * cellHeight;
        temp.X += X * (screenWidthCount - 1) * cellWidth;
        temp.Y = isNaN(temp.Y) || Number(temp.Y) >= 0 ? 0 : temp.Y;
        temp.X = isNaN(temp.X) || Number(temp.X) >= 0 ? 0 : temp.X;

        if (Math.abs(temp.Y) <= totalOffsetHeight) {
          touch.Y = temp.Y;
        } else {
          wx.hideLoading();
        }
        if (Math.abs(temp.X) <= totalOffsetWidth) {
          touch.X = temp.X;
        } else {
          wx.hideLoading();
        }

        // check
        if (touch.X > 0) {
          touch.X = 0;
        }
        if (touch.Y > 0) {
          touch.Y = 0;
        }

        this.isScrolled = true;

        // set state
        this.setData({
          touch,
        });
      }

      wx.hideLoading();
    }
  }
});

