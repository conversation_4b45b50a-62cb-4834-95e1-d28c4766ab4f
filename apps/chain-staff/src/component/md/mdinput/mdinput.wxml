<template name="mdInput">
<view class="mdi-main" style="{{style_mdi_main}}">
    <view class="mdi-header" style="{{style_mdi_border}}">
        <view class="mdi-float-label" style="{{style_mdi_float}}" >{{mdi_float_label}}</view>
       <input class= "mdi-input"  placeholder-class="mdi-input-ph"  bindinput="{{onMDInput}}" bindblur="{{onMDIBlur}}"/>
    </view>
    <view class="mdi-footer" style="{{style_mdi_footer}}">
        <view class="mdi-helper" style="{{style_mdi_helper}}" wx:if="{{showHelperText}}">
            {{mdi_helper_text}}
        </view>
        <view class="mdi-number">
            <text style="{{style_mdi_num_input}}" >{{mdi_num_input}}</text>
            <text style="{{style_mdi_num_range}}">/{{mdi_num_range}}</text>
        </view>
    </view>
</view>
</template>
