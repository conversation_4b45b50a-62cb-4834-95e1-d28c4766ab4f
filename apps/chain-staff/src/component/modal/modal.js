/**
 * event: cancel, contenttap
 */
Component({
  options: {
    // multipleSlots: true,
  },
  properties: {
    hasBackdrop: {
      type: Boolean,
      value: true,
    },
    hidden: {
      type: Boolean,
      value: true,
      observer(newValue) {
        this.setData({
          hidden: newValue,
        });

        if (newValue === true) {
          this.modalHidden();
        }
      }
    },
  },
  data: {
  },
  methods: {
    onContainerTap() {
      this.setData({
        hidden: true,
      });
      this.triggerEvent('cancel');
    },
    catchContentTap() {
      this.triggerEvent('contenttap');
    },

    modalHidden() {
      this.triggerEvent('hide');
    },
  }
});

