Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    // 这里定义了innerText属性，属性值可以在组件使用时指定
    selected: {
      type: Number,
      value: 0,
    },
    // {
    //   text,
    //   id,
    // }
    items: {
      type: Array,
      value: [],
    },

    isTop46: {
      type: <PERSON><PERSON>an,
      value: null
    }
  },
  data: {
  },
  ready() {
    this.setData({
      _selected: this.data.selected,
    });
  },
  methods: {
    onTapTabItem({
      currentTarget: {
        dataset: {
          tab
        }
      }
    }) {
      this.setData({
        _selected: tab,
      });
      this.triggerEvent('tabtap', {
        item: this.data.items.find(item => item.id === tab),
      });
    },
  }
});

