<view class="mp-tab__wrapper {{ isTop46 ? 'mp-tab__top-46' : ''}}">
  <view class="mp-tab__container">
    <view wx:for="{{items}}" wx:key="{{index}}" class="mp-tab__item {{_selected === item.id ? 'mp-tab__item--active' : ''}}" bindtap="onTapTabItem" data-tab="{{item.id}}">
      <view class="mp-tab__item-text">
        {{item.text}}
      </view>
    </view>
  </view>

  <view class="mp-tab__body-wrapper">
    <view wx:for="{{items}}" wx:key="{{item}}" class="mp-tab__body {{_selected === item.id ? 'mp-tab__body--active' : ''}}">
      <slot name="{{item.id}}"></slot>
    </view>
  </view>
</view>

