<!--component/check-list/check-list.wxml-->
<view class='wp-pet-card__container'>
  <view class="wp-pet-card__header">
    <view class="wp-pet-card__header-tag">
      <view class="wp-pet-card__header-tag-hair"> {{item.breed}} </view>
      <view class="wp-pet-card__header-tag-sterilization"> {{item.gender || '未知'}} </view>
      <view wx:if="{{item.specialFocus}}" class="wp-pet-card__header-tag-vip"> 特别关注 </view>
    </view>
    <view
      class="{{'wp-pet-card__header-info ' + ((item.status === 1) && 'wp-pet-card__header-info--checking ' || '') + ((item.status === 2) && 'wp-pet-card__header-info--finished ' || '') + ((item.status === 3) && 'wp-pet-card__header-info--timeout ' || '')}}"
    >
      <text class="wp-pet-card__header-info-text">{{item.statusText}}</text>
      <view class="wp-pet-card__circle-icon"></view>
    </view>
  </view>
  <view class='wp-pet-card__body'>
    <image wx:if="{{item.petType === 'Dog'}}" class="wp-pet-card__body-pic" src="../../images/check/dog-1.png"></image>
    <image wx:if="{{item.petType === 'Cat'}}" class="wp-pet-card__body-pic" src="../../images/check/cat-1.png"></image>
    <view class='wp-pet-card__body-info'>
      <view class="wp-pet-card__body-info-pet">
        <text class="wp-pet-card__body-info-pet-name">{{item.petName || '未知'}}</text>
        <text class="wp-pet-card__body-info-pet-time">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="wp-pet-card__body-info-user">
        <text class="wp-pet-card__body-info-user-name">{{item.username}}</text>
        <text class="wp-pet-card__body-info-user-phone">{{item.mobile}}</text>
      </view>
    </view>
  </view>
</view>
