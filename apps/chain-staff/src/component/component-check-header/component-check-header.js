import {
  store,
  acs,
  getStorage,
  state,
} from '@petkit/redux';
import {
  isArray,
} from 'lodash-es';

const compConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
  },

  /**
   * 组件的初始数据
   */
  data: {
    pickerList: [],
    initialIndex: -1,
    name: '',
    index: -1,
    storeName: '...',
  },

  ready() {
    this.subscribe();
    this.setData({
      unsubscribe: store.subscribe(() => {
        this.subscribe();
      }),
    });
  },


  detached() {
    if (this.data.unsubscribe) {
      this.data.unsubscribe();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    update() {
      let store = this.data.pickerList[this.data.index];
      if (store === undefined) {
        return;
      }

      let localStoreId = getStorage().getItem('storeId');
      let {
        storeId,
      } = store;

      if (storeId !== localStoreId) {
        let index = this.data.pickerList.findIndex(v => v.storeId === localStoreId);
        this.onWarehouseChange(index);
      }
    },
    subscribe() {
      this.update();

      let state = store.getState();
      // let list = state.foster.dailyCheck.warehouse.list;
      let list = state.user.loginWp.storeAndLeaderInfo;
      let pickerList = list;

      let {userName} = state.user.loginWp;
      this.setData({
        pickerList,
        name: userName,
      });

      if (!list || list.length == 0) {
        return;
      }

      // 判断是否是第一次登录
      if (!getStorage().getItem('storeId')) {
        let initialIndex = -1,
          {storeAndLeaderInfo} = state.user.loginWp;
        if (isArray(storeAndLeaderInfo)) {
          let warehouseId = storeAndLeaderInfo[0];
          initialIndex = list.findIndex(v => v.storeId == warehouseId);
        }

        if (initialIndex !== this.data.initialIndex) {
          this.setData({
            initialIndex,
          });
          this.onWarehouseChange(initialIndex);
        }
      } else {
        if (this.data.index < 0) {
          this.onWarehouseChange(list.findIndex(v => v.storeId == getStorage().getItem('storeId')));
        }
      }
    },
    onWarehouseChange(index) {
      // 解决：当用户进入页面后，权限发生变化的情况
      // 找不到的时候默认选择第一个
      if (index === -1) {
        index = 0;
      }
      let {
        storeName,
        storeId,
        isLeader,
        businessModel,
      } = this.data.pickerList[index];

      this.setData({
        index,
        storeName,
      });

      getStorage().setItem('storeId', storeId);
      getStorage().setItem('storeLeader', isLeader);
      getStorage().setItem('businessModel', businessModel);

      this.triggerEvent('storechange', {
        storeId,
        storeName,
        isLeader,
        businessModel
      });
    },
    bindPickerChange(e) {
      let index = e.detail.value;
      let userId = store.getState().user.loginWp.userId;
      let currentStoreId = this.data.pickerList[index].storeId;
      store.dispatch(acs.user.loginWp.getUserStoreInfo({userId: userId}, {
        success: () => {
          let list = store.getState().user.loginWp.storeAndLeaderInfo;
          this.onWarehouseChange(list.findIndex(v => v.storeId == currentStoreId));
        }
      }));
    }
  }
};

Component(compConfig);

