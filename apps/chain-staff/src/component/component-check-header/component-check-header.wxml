<!--component/check-header/check-header.wxml-->
<check-header class="wp-check__hd">
  <picker bindchange="bindPickerChange" value="{{index}}" range="{{pickerList}}" range-key="storeName">
    <view class="wp-check__hd-left">
      <view><icon class="icon icon-store"></icon></view>
      <view class="wp-check__hd-left-option">
        <view type="default" class="replace-btn">{{storeName}}</view>
      </view>
      <view><icon class="icon icon-draw"></icon></view>
    </view>
  </picker>
  <view class="wp-check__hd-right">
    <view>
      <icon class="icon icon-female"></icon>
    </view>
    <view>
      <text class="user_name">{{name}}</text>
    </view>
  </view>
</check-header>

