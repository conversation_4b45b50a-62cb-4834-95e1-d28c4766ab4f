Component({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
    pageState: {
      type: Number,
      value: ''
    },
      currencySymbol:{
          type:String,
          value:'￥'
      }
  },
  data: {
    // 组件内部的数据
    pageState: 0,
    registerName: null,
    vipIcon: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.ZowXlrWzTZSy7838fc0a2470870dd6c9fd8d64bb9b6e.svg',
  },
  options: {
    addGlobalClass: true
  },

  methods: {
    // 自定义方法
    onAddToShoppingCartTap(ev) {
      const detail = ev.currentTarget.dataset.detail;
      this.triggerEvent('addTap', {
        data: detail,
        touches: ev.touches,
      });
    },

    onPreventTap() {
      this.triggerEvent('hint');
    },

    _onPreventTap() {
      return false;
    },

    onGetPhoneNumberTap(ev) {
      this.triggerEvent('registry', {data: ev});
    },
  }
});
