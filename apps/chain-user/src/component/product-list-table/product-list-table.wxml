<view class="wp-crown-shopping-product-list-table__wrapper {{detail.type && detail.type === 'search' ? 'wp-crown-shopping-product-list-table__wrapper-search' : ''}}" wx:if="{{!detail.add_type}}">
  <view class="wp-crown-shopping-product-list-table__image-container skeleton-rect">
    <image lazy-load="true" src="{{detail.image}}?x-oss-process=image/resize,w_168/quality,Q_80/format,jpg" />
    <image lazy-load="true" class="wp-crown-shopping-product-list-table__image-container-sold-out-img"
      wx:if="{{!detail.isQuantityEnough}}"
      src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/edb6485990c340d7a13f13f1430e1d5e?nsukey=hA%2Fw%2F13oKumKoYjVkUPkETTVVmRyLjgkKv7ST7J5nUYCKDHVSqbNQo8fuPX7SQrupOF1UTW7r0rEiwXxOz3loIIxl0Hw6GPcRi0VAaf2ITeC%2FgYWgv6mrtCvqxCU%2FKp%2Fi7R5IliUS%2Bi7XoOFdY2hlQ0bZsamwtbc7HGhU0mb9Hrbu99CeKmlt8Du6rz2AcRB6a2cgGceJkfFij3ehxyscQ%3D%3D"></image>
  </view>
  <view class="wp-crown-shopping-product-list-table__main-content">
    <view class="wp-crown-shopping-product-list-table__main-content-title skeleton-rect">
      <image
        class="wp-crown-shopping-product-list-table__main-content-title-oversea"
        src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.Vf6MbWxnnBcg50b12d6c32d8a8322b012f977d86ad9f.png"
        wx:if="{{detail._crossBorder}}"
      >
      </image>
      <view class="{{detail._crossBorder ? 'wp-crown-shopping-product-list-table__main-content-title--hasoversea' : 'wp-crown-shopping-product-list-table__main-content-title-font'}}">{{detail.name}}</view>
    </view>
    <view class="wp-crown-shopping-product-list-table__main-content__subtitle">
      {{detail.subTitle || ''}}
    </view>
    <view class="wp-crown-shopping-product-list-table__main-content__tags">
      <view class="wp-crown-shopping-product-list-table__main-content__tags-show" wx:if="{{detail.vipPrice !== null && detail.vipPrice !== detail.price && detail.vipDiscountTag}}">
        <image class="wp-crown-shopping-product-list-table__main-content-footer-price-vip-icon" src="{{vipIcon}}"/>
        <view class="wp-crown-shopping-product-list-table__main-content-footer-price-vip-background">{{detail.vipDiscountTag}}折</view>
      </view>
      <view class="wp-crown-shopping-product-list-table__main-content-tags" wx:if="{{detail.tags}}">
        <block wx:for="{{detail.tags}}" wx:key="item">
          <view>{{item}}</view>
        </block>
      </view>
    </view>
    <!-- <view class="wp-crown-shopping-product-list-table__main-content-paid-number-info">
      205人付款
    </view> -->
    <!-- <view class="wp-crown-shopping-product-list-table__main-content-discount-info-container">
      <view class="wp-crown-shopping-product-list-table__main-content-discount-info-item">买一赠一</view>
      <view class="wp-crown-shopping-product-list-table__main-content-discount-info-item">明日送达</view>
    </view> -->
    <view class="wp-crown-shopping-product-list-table__main-content-footer-container">
      <view class="wp-crown-shopping-product-list-table__main-content-footer-price-info skeleton-rect">
        <block wx:if="{{detail.vipPrice !== null}}">
          <block wx:if="{{detail.vipPrice !== detail.price}}">
            <!-- <view class="wp-crown-shopping-product-list-table__main-content-footer-price__vip">
              <image class="wp-crown-shopping-product-list-table__main-content-footer-price-vip-icon" src="{{vipIcon}}"/>
              <view class="wp-crown-shopping-product-list-table__main-content-footer-price-vip-background">{{detail.vipDiscountTag}}折</view>
            </view> -->
            <view class="wp-crown-shopping-product-list-table__main-content-footer-price-current">{{currencySymbol}}{{detail.price}}</view>
          </block>
          <block wx:if="{{detail.vipPrice === detail.price}}">
            <view class="wp-crown-shopping-product-list-table__main-content-footer-price-current">{{currencySymbol}}{{detail.price}}</view>
          </block>
          <block wx:if="{{detail.activityPriceFlag}}">
            <view class="product-sku-list-vip-activity-price">
              <view class="left"><text class="activity-price-unit">{{currencySymbol}}</text>{{detail.activityPrice}}</view>
              <view class="right">
                <image src="https://img3.petkit.cn/images/156ee0c713434d31886a1cd3ede98b12"></image>
              </view>
            </view>
          </block>
        </block>
        <block wx:if="{{detail.vipPrice === null}}">
          <view class="wp-crown-shopping-product-list-table__main-content-footer-price-current" wx:if="{{detail.price !== null}}">{{currencySymbol}}{{detail.price}}</view>
          <block wx:if="{{detail.activityPriceFlag}}">
            <view class="product-sku-list-vip-activity-price">
              <view class="left"><text class="activity-price-unit">{{currencySymbol}}</text>{{detail.activityPrice}}</view>
              <view class="right">
                <image src="https://img3.petkit.cn/images/156ee0c713434d31886a1cd3ede98b12"></image>
              </view>
            </view>
          </block>
          <view class="wp-crown-shopping-product-list-table__main-content-footer-price-original" wx:if="{{detail.marketPrice !== null}}">{{currencySymbol}}{{detail.marketPrice || 0}}</view>
        </block>
        <!-- <view class="wp-crown-shopping-product-list-table__main-content-footer-price-result">{{detail.discountInfo}}</view> -->
        <!-- <view class="wp-crown-shopping-product-list-table__main-content-footer-price-original" wx:if="{{detail.vipPrice === null && detail.marketPrice !== detail.price && detail.marketPrice !== null}}">{{currencySymbol}}{{detail.marketPrice || 0}}</view>
        <view class="wp-crown-shopping-product-list-table__main-content-footer-price-vip" wx:if="{{detail.vipPrice !== null && detail.vipPrice !== detail.price}}">{{currencySymbol}}{{detail.vipPrice}}</view>
        <image src="{{vipIcon}}" class="wp-crown-shopping-product-list-table__main-content-footer-price-vip-icon" wx:if="{{detail.vipPrice !== null && detail.vipPrice !== detail.price}}"></image> -->
      </view>
      <block wx:if="{{detail.isQuantityEnough}}">
        <view class="wp-crown-shopping-product-list-table__main-content-footer-icon"
              wx:if="{{pageState === 1 || pageState === 3 }}"
              catchtap="_onPreventTap">
          <text class="wp-crown-shopping-product-list-table__main-content-footer-icon-tap skeleton-radius icon-cart PETKIT"></text>
        </view>

        <view class="wp-crown-shopping-product-list-table__main-content-footer-icon"
              wx:else
              catchtap="onAddToShoppingCartTap"
              data-detail="{{detail}}">
          <text class="wp-crown-shopping-product-list-table__main-content-footer-icon-tap skeleton-radius icon-cart PETKIT"></text>
        </view>
      </block>
      <view class="wp-crown-shopping-product-list-table__main-content-footer-icon-wrapper"
            wx:if="{{!detail.isQuantityEnough}}"
            catchtap="onPreventTap">
        <text class="wp-crown-shopping-product-list-table__main-content-footer-icon-tap disabled skeleton-radius icon-cart PETKIT"></text>
      </view>
    </view>
  </view>
</view>
