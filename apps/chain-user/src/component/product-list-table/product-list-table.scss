@import 'config/theme';

@mixin wp-crown-shopping-product-list-table-theme($theme) {
  @include wp-crown-shopping-product-list-table-basic-theme($theme);
  @include wp-crown-shopping-product-list-table-image-theme($theme);
  @include wp-crown-shopping-product-list-table-main-content-theme($theme);
}

@mixin wp-crown-shopping-product-list-table-basic-theme($theme) {
  .wp-crown-shopping-product-list-table {
    &__wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      padding: 0 u(28) u(24);

      &-search {
        padding: 0 u(32) u(26);
      }
    }
  }
}

@mixin wp-crown-shopping-product-list-table-image-theme($theme) {
  $primary: map-get($theme, primary);
  $foreground: map-get($theme, foreground);
  .wp-crown-shopping-product-list-table {
    &__image {
      &-container {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-right: u(24);
        background-color: #fcfcfc;

        image {
          width: u(168);
          height: u(184);
          border-radius: u(8);
        }

        &-sold-out-img {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}

@mixin wp-crown-shopping-product-list-table-main-content-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $foreground: map-get($theme, foreground);
  .wp-crown-shopping-product-list-table {
    &__main-content {
      display: flex;
      flex-direction: column;
      justify-content: center;

      flex-grow: 1;
      width: u(346);

      &__subtitle {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        height: u(40);
        color: #B0B0B0;
        font-size: u(24);
        line-height: u(40);
        margin: u(4) 0 u(2);
      }

      &__tags {
        display: flex;
        position: relative;
        width: u(288);
        overflow: hidden;
        height: u(40);

        &-show {
          display: flex;
          align-items: center;
          width: u(140);
          height: u(32);
          margin-right: u(8);
        }
      }

      &-title {
        overflow: hidden;
        display: flex;
        align-items: center;
        line-height: u(40);
        font-size: u(28);
        color: #3a3f4a;

        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        width: 100%;

        &-oversea {
          line-height: u(40);
          height: u(24);
          width: u(72);
          margin-right: u(8);
          position: relative;
        }

        &--hasoversea {
          width: calc(100% - #{u(80)});
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &-font {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      &-tags {
        display: flex;
        height: u(40);
        flex-wrap: wrap;
        overflow: hidden;

        & > view {
          height: u(24);
          border-radius: u(5);
          border: u(1) solid mat-color($primary);
          font-size: u(16);
          color: mat-color($primary);
          padding: u(2) u(4);
          margin-right: u(4);
          margin-bottom: u(100);
          line-height: u(24);
        }
      }

      &-paid-number-info {
        font-size: u(18);
        color: #9da7ba;
      }

      &-discount-info {
        &-container {
          display: flex;
        }

        &-item {
          margin-right: u(4);
          padding: u(2) u(4);
          border: u(2) solid mat-color($primary);
          border-radius: u(4);

          font-size: u(16);
          color: mat-color($primary);
        }
      }

      &-footer {
        &-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          margin-top: u(16);
          height: u(44);
          position: relative;
        }

        &-price {
          &-info {
            display: flex;
            align-items: flex-end;
          }

          &-current {
            //color: mat-color($primary);
            color: #000;
            font-size: u(28);
            margin-right: u(12);
          }

          &__vip {
            display: flex;
            align-items: center;
            position: relative;
          }

          &-original {
            transform: translateY(u(-4));
            color: #c4c9d2;
            font-size: u(20);
            text-decoration: line-through;
          }

          &-vip {
            color: rgba(64, 51, 42, 1);
            font-size: u(20);
            line-height: u(44);

            &-background {
              width: u(60);
              height: u(24);
              background: linear-gradient(316deg, rgba(245, 222, 194, 1) 0%, rgba(245, 213, 173, 1) 100%, rgba(239, 214, 183, 1) 100%);
              border-top-right-radius: u(4);
              border-bottom-right-radius: u(4);
              color: #47372C;
              font-size: u(18);
              text-align: center;
              font-weight: 500;
              line-height: u(24);
            }
          }

          &-vip-icon {
            line-height: u(44);
            height: u(24);
            width: u(64);
          }
        }

        &-icon {
          position: absolute;
          width: u(68);
          height: u(68);
          padding: u(20);
          text-align: center;
          line-height: u(68);
          right: u(-20);
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;

          &--button {
            display: block;
            border: none;
            outline: none;
            background: transparent;
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;

            &::after {
              border: none;
            }
          }

          &-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          &-tap {
            height: u(48);
            width: u(48);
            display: inline-block;
            line-height: u(48);
            text-align: center;
            font-size: u(32);
            font-weight: 500;
            border-radius: 50%;
            background-color: mat-color($primary);
            color: #fff;

            &.disabled {
              background-color: #E7E7E7;
              color: #5C5C5C;
            }
          }

          .icon-ic_screen1 {
            font-size: u(11);
          }
        }
      }
    }
  }
}

.product-sku-list-vip-activity-price{
  height: 100%;
  font-size: u(20);
  font-weight: 500;
  color: #EA4336;
  line-height: u(20);
  display: flex;
  margin-bottom: u(10);
  margin-right: u(10);
  justify-content: center;
  align-items: center;
  .right{
    width: u(38);
    height: u(17);
    .activity-price-unit{
      font-size: u(16);
    }
    image{
      width: 100%;
      height: 100%;
    }
  }
}

@include wp-crown-shopping-product-list-table-theme($lucky-theme);
