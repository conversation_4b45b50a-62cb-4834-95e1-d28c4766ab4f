@import 'config/theme';
@import 'styles/variable/z-index';

@mixin mp-component-animation-dot-parabola($theme) {
  $accent: map-get($theme, accent);
  .mp-crown-component-animation-dot-parabola {
    $duration: .725s;

    &__dot {
      $selector: &;

      $width: u(32);

      position: absolute;
      left: 0;
      top: 0;
      z-index: $animation-dot-parabola-z-index;

      width: $width;
      height: $width;

      border-radius: 50%;

      transition: transform $duration, opacity $duration;

      &#{$selector}--fade-in {
        opacity: 1;
      }

      &-x {
        opacity: 0;

        transform-origin: 50% 50% 0;
        transition-timing-function: linear;
      }

      &-y {
        background-color: mat-color($accent);

        transform-origin: 50% 50% 0;
        transition-timing-function: cubic-bezier(.5, -.5, 1, 1);
      }
    }
  }
}

@include mp-component-animation-dot-parabola($lucky-theme);

