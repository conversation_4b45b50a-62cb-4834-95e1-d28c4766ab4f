Component({
  properties: {
    originX: {
      type: Number,
      value: -100,
    },
    originY: {
      type: Number,
      value: -100,
    },
    targetX: {
      type: Number,
      value: -100,
    },
    targetY: {
      type: Number,
      value: -100,
    },
  },
  observers: {
    'originX, originY': function(originX, originY) {
      if (this.data.animated) {
        return;
      }

      let {
        targetX,
        targetY,
      } = this.data;

      this.setData({
        translateX: originX,
        translateY: originY,
        animated: true,
      }, () => {
        this.setData({
          translateX: targetX,
          translateY: targetY,
          fadeIn: true,
        }, () => {
          setTimeout(() => {
            this.setData({
              animated: false,
              fadeIn: false,
              translateX: -100,
              translateY: -100,
            });
          }, 725);
        });
      });
    }
  },
  data: {
    // animated: false,
    animated: true,
    fadeIn: false,
    translateX: -100,
    translateY: -100,
  },
  ready() {
  },
  methods: {
  }
});

