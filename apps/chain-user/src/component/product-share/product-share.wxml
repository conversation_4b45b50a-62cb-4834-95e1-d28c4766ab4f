
<view class="wp-shopping-product-detail__share">
  <!--分享蒙版-->
  <view class="wp-shopping-product-detail__share__mask {{isShowShare?'active':''}}"
        bindtap="maskClickHandler"></view>
  
  <!--分享按钮popup-->
  <view class="wp-shopping-product-detail__share__share-popup {{isShowShare?'active':''}}">
    <view class="wp-shopping-product-detail__share__share-popup__content">
      <view class="wp-shopping-product-detail__share__share-popup__content__title">
        分享
      </view>
      <view class="wp-shopping-product-detail__share__share-popup__content__share">
        <view class="wp-shopping-product-detail__share__share-popup__content__share__item">
          <!--          <view class="wp-shopping-product-detail__share__share-popup__content__share__item__icon" open-type="share"></view>-->
          <button class="wp-shopping-product-detail__share__share-popup__content__share__item__icon"
                  open-type="share">
            <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.fQ7t7idoYwvFfec9077b3e2cf09969b1f378b2d24966.svg" />
          </button>
          <view class="wp-shopping-product-detail__share__share-popup__content__share__item__name">分享给好友</view>
        </view>
        <view class="wp-shopping-product-detail__share__share-popup__content__share__item" bindtap="saveProductPicture">
          <view class="wp-shopping-product-detail__share__share-popup__content__share__item__icon">
            <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.tqNwmF3JyAUL1aa6e6758c65c8a4219dd5271942914b.svg" />
          </view>
          <view class="wp-shopping-product-detail__share__share-popup__content__share__item__name">保存图片分享</view>
        </view>
      </view>
    </view>
    <view class="wp-shopping-product-detail__share__share-popup__footer"
          bindtap="closeSharePanel">
      <text class="wp-shopping-product-detail__share__share-popup__footer__cancel">取消</text>
    </view>
  </view>
  
  <view class="wp-shopping-product-detail__share__share-image {{isShowShare?'active':''}}">
    <canvas class="wp-shopping-product-detail__share__share-image__panel"
            canvas-id="sharePanelCanvas"></canvas>
  </view>
</view>
