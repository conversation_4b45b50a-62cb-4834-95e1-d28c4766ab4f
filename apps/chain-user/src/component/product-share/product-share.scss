@import 'config/theme';

@keyframes slide-up {
  0% {
    transform: translateY(100%);
  }
  20% {
    transform: translateY(80%);
  }
  40% {
    transform: translateY(60%);
  }
  60% {
    transform: translateY(40%);
  }
  80% {
    transform: translateY(20%);
  }
  100% {
    transform: translateY(0);
  }
}


@mixin wp-shopping-product-detail-share-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  view {
    box-sizing: border-box;
  }

  .wp-shopping-product-detail {
    &__share {
      position: relative;
      background: #fff;
      overflow: hidden;

      &__mask {
        display: none;
        position: fixed;
        z-index: 99;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        background-color: #000;
        opacity: .48;
      }

      &__mask.active {
        display: block;
      }

      &__share-popup {
        display: none;
        width: 100%;
        position: fixed;
        bottom: 0;
        z-index: 100;
        background-color: #fff;
        color: #2F2F2F;
        font-family: PingFangSC;
        box-sizing: border-box;
        animation: slide-up .2s ease-in-out;

        &__content {
          box-sizing: border-box;
          width: 100%;
          padding: u(16) u(24) u(32);
          border-bottom: u(2) solid #EBEBEB;

          &__title {
            font-size: u(32);
            font-weight: 400;
            text-align: left;
          }

          &__share {
            height: u(194);
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: u(124);
            padding-right: u(104);

            &__item {
              display: flex;
              flex-direction: column;
              align-items: center;

              &__icon {
                height: u(96);
                width: u(96);
                border-radius: 50%;
                border: u(2) solid #E3E3E3;
                margin-bottom: u(16);
                padding: u(20);
                background-color: #fff;
                line-height: 0;

                image {
                  height: 100%;
                  width: 100%;
                }
              }
            }
          }
        }

        &__footer {
          text-align: center;
          height: u(96);
          line-height: u(96);

          &__cancel {
            font-size: u(32);
            font-weight: 400;
          }
        }
      }

      &__share-popup.active {
        display: block;
      }

      &__share-image {
        display: none;
        left: 50%;
        bottom: u(326);
        transform: translateX(-50%);
        z-index: 101;
        position: fixed;
        height: u(850);
        width: u(476);
        border-radius: u(16);
        box-shadow: 0 u(4) u(20) 0 rgba(0, 0, 0, 0.25);
        animation: ui-fade-in .2s linear;
        font-family: PingFangSC;
        //background-color: #fff;

        &__panel {
          position: relative;
          display: block;
          height: 100%;
          width: 100%;
        }

        &__title {
          height: u(64);
          border-radius: u(20) u(20) 0 0;
          background: linear-gradient(117deg, #5A69FF 0, #76B6FF 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding-bottom: u(10);

          .icon-LOGO {
            color: #fff;
          }

          &__text {
            font-size: u(18);
            color: #fff;
            display: inline-block;
            height: u(50);
            line-height: u(44);
          }
        }

        &__content {
          width: 100%;
          height: u(788);
          border-radius: u(20) u(20) 0 0;
          background-color: #fff;
          padding: u(14) u(10) 0 u(10);
          margin-top: u(-14);

          &__image {
            width: 100%;

            image {
              display: block;
              margin: 0 auto;
              width: u(318);
              height: u(436);
            }
          }

          &__product {
            margin-top: u(32);
            padding: 0 u(10);

            &__desc {
              font-size: u(20);
              font-weight: 400;
              line-height: u(30);
              color: #3A3F4A;
            }

            &__labels {
              margin: u(14) 0;
              line-height: u(14);

              &__item {
                width: u(48);
                height: u(14);
                border-radius: u(2);
                opacity: .9;
                border: u(2) solid #2034B5;
                background-color: #fff;
                font-size: u(10);
                font-weight: 300;
                line-height: u(14);
                text-align: center;
                color: #2034B5;
                display: inline-block;
                margin-right: u(10);
              }
            }

            &__price {
              color: #FF587D;
              font-weight: 500;

              &__flag {

                font-size: u(28);
              }

              &__number {
                margin-left: u(2);
                font-size: u(30);
              }

            }
          }

          &__footer {

            margin-top: u(10);
            padding: 0 u(50);
            display: flex;
            justify-content: center;
            align-items: center;

            &__QR--code {
              image {
                width: u(168);
                height: u(168);
              }
            }

            &__right {
              margin-left: u(10);

              &__name {
                font-size: u(24);
                font-weight: 500;
                color: #3A3F4A;
              }

              &__sub-name {
                font-size: u(18);
                font-weight: 400;
                color: #9397A2;
              }
            }

          }
        }
      }

      &__share-image.active {
        display: block;
      }
    }
  }
}

@include wp-shopping-product-detail-share-theme($lucky-theme);
