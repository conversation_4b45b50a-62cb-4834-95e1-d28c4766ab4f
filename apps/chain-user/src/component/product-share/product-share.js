import {store} from '@petkit/redux';
Component({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
    isShowShare: {
      type: Boolean,
      value: false
    },
      currencySymbol:{
        type:String,
          value:'￥'
      }
  },
  data: {
    // canvas导出文件路径
    canvasFilePath: '',
    repUnit: 1, // 设备像素比
    systemInfo: {}, // 设备信息
  },
  ready() {
    // 系统信息
    const systemInfo = store.getState().global.env.systemInfo;
    this.setData({
      systemInfo
    });
  },
  observers: {
    detail: function (detail) {
      if (detail.codeUrl) {
        this.shareProduct();
      }
    }
  },
  methods: {
    // 蒙版点击
    maskClickHandler() {
      this.closeSharePanel();
    },

    // 关闭方法
    closeSharePanel() {
      this.setData({
        isShowShare: false
      })
    },

    // 生成图片
    shareProduct() {
      //canvas绘图
      this.canvasDrawSharePicture();
    },

    // 重新唤起授权
    showAuthModel() {
      wx.showModal({
        content: '请求授权当前文件存储权限',
        success: (res) => {
          if (res.cancel) {
            wx.showToast({
              title: '拒绝授权',
              icon: 'none',
              duration: 1000
            });
          } else if (res.confirm) {
            wx.openSetting({
              success: (res) => {
                if (res.authSetting['scope.writePhotosAlbum'] == true) {
                  this._saveImageToPhotosAlbum();
                } else {
                  wx.showToast({
                    title: '授权失败',
                    icon: 'none',
                    duration: 1000
                  });
                }
              }
            });
          }
        }
      });
    },

    // 保存图片分享
    saveProductPicture() {
      //授权
      if (this.data.canvasFilePath) {
        wx.getSetting({
          success: (res) => {
            if (!res.authSetting['scope.writePhotosAlbum']) {
              wx.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => {
                  this._saveImageToPhotosAlbum();
                },
                fail: () => {
                  this.showAuthModel();
                }
              })
            } else {
              this._saveImageToPhotosAlbum();
            }
          }
        })
      }
    },
    _saveImageToPhotosAlbum() {
      this.closeSharePanel();
      wx.saveImageToPhotosAlbum({
        filePath: this.data.canvasFilePath,
        success: () => {
          wx.showToast({
            title: '图片已保存至手机相册',
            icon: 'none',
            duration: 2000,
          })
        },
        fail: () => {
          wx.showToast({
            title: '图片保存失败，请重新尝试',
            icon: 'none',
            duration: 2000,
          })
        },
      })
    },
    /**
     * @description:网络图片转本地
     * @param{string}url 图片地址
     * @returns{Promise}
     */

    _getImageInfo(url) {
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: url,
          success(res) {
            resolve(res)
          },
          fail(err) {
            reject({...err, url: url})
          }
        })
      })
    },
    // canvas 绘画分享图片
    canvasDrawSharePicture() {
      // 产品描述
      let text = this.data.detail.name;

      // 产品数据状态
      let productParamsStatus = {
        isEmptyTag: !this.data.detail.tags || this.data.detail.tags.length === 0,
        isEmptyDesc: Boolean(this.data.detail.name)
      };
      // canvas属性
      let canvasOptions = {
        width: 238,
        height: 425
      };
      // 设备像素比
      let unit = this.data.systemInfo.screenWidth / 375;
      this.setData({
        repUnit: unit
      });

      // 图片列表 需要先调用微信方法下载到本地
      const pictureImageConfig = [
        // 顶部title 固定值
        {
          url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.GNRzFdfu4n6Zfb6480c7375a09f0bd86ff645961eb22.png',
          option: {
            dx: 0,
            dy: -unit,
            dWidth: unit * canvasOptions.width,
            dHeight: unit * 30
          },
          type: 'LOGO_IMAGE'
        },
        // 产品图片
        {
          url: this.data.detail.image ? this.data.detail.image + '?x-oss-process=image/resize,w_750/quality,Q_80/format,jpg' : '',
          option: {
            dx: 0,
            dy: unit * 30,
            dWidth: unit * 240,
            dHeight: unit * 240
          },
          type: 'PRODUCT_IMAGE'
        },
        // 小程序二维码
        {
          url: this.data.detail.codeUrl,
          option: {
            dx: unit * 34,
            dy: unit * 336,
            dWidth: unit * 84,
            dHeight: unit * 84
          },
          type: 'MINIAPP_IMAGE'
        }
      ];
      // 图片列表
      let picturePromise = [];

      // ctx对象
      const ctx = wx.createCanvasContext('sharePanelCanvas', this);
      //转换所有图片
      pictureImageConfig.forEach((imgConfig) => {
        // 如果是空的产品图片 跳过  兼容没有设置产品图片生成二维码
        if (imgConfig.type === 'PRODUCT_IMAGE' && !imgConfig.url) {
          picturePromise.push({
            path: ''
          })
        } else {
          picturePromise.push(this._getImageInfo(imgConfig.url));
        }
      });

      //把canvas绘制成白底解决导出图片没底色问题
      ctx.setFillStyle('#fff');
      this.fillRoundRect(ctx, 0, 0, canvasOptions.width * unit, canvasOptions.height * unit, 20 * unit, '#fff');
      ctx.save();

      // 图片下载完成
      Promise.all(picturePromise).then((values) => {
        values.forEach((item, index) => {
          if (item.path) {
            let option = pictureImageConfig[index].option;
            ctx.drawImage(item.path, option.dx, option.dy, option.dWidth, option.dHeight);
          }
        });
        // 绘制产品描述
        ctx.save();
        ctx.setFillStyle('#3A3F4A');
        // 返回当前描述的高度
        let descTextHeight = this.drawText(
          ctx,
          text,
          unit * 10,
          unit * 285,
          0,
          unit * (canvasOptions.width - 10),
          unit * 10
        );

        // 绘制产品标签
        ctx.save();
        ctx.setFontSize(unit * 5);
        ctx.lineWidth = unit;
        let distanceLeft = 12; // 标签左边距
        let distanceTop = descTextHeight === 0 ? 290 : 310; // 距离顶部
        let isSecond = false;

        this.data.detail.tags && this.data.detail.tags.forEach((tag, index) => {
          // 宽度
          let width = ctx.measureText(tag).width + 2;
          // 判断是否换行
          if (!isSecond && distanceLeft + width >= canvasOptions.width - 10) {
            distanceTop += 14;
            distanceLeft = 12; //重置左边距离
            isSecond = true;
          }
          // 填充文字
          ctx.setFillStyle('#2034B5');
          ctx.fillText(tag, unit * (distanceLeft + 3), unit * (distanceTop + 7));
          // 画边框

          this.roundRect(ctx, unit * (distanceLeft), unit * distanceTop, unit * (width + 3), unit * 10, unit * 2, '#2034B5');
          // 边距
          distanceLeft += (width + 5);
          ctx.save();
        });


        // 绘制价格
        ctx.save();
        ctx.setFillStyle('#FF587D');
        ctx.setFontSize(unit * 15);
        let priceTop = 330;
        // if (isSecond) {
        //   priceTop = 345;
        // } else if (productParamsStatus.isEmptyTag) {
        //   priceTop = 305;
        // }

        ctx.fillText(this.data.currencySymbol + this.data.detail.price, unit * 5, priceTop * unit);


          if (this.data.detail.activityPriceFlag) {
              this.drawActivityPriceIcon(ctx, this.data.detail, unit, unit * 15 * (this.data.detail.price.toString().length + 1), priceTop - 13)
          }




        // 小佩购物小程序
        ctx.save();
        ctx.setFillStyle('#3A3F4A');
        ctx.setFontSize(unit * 12);
        let pet = '小佩购物小程序';
        ctx.fillText(pet, unit * 132, unit * 375);

        ctx.save();

        // 长按识别·喵呜～
        ctx.setFillStyle('#9397A2');
        ctx.setFontSize(unit * 9);
        ctx.fillText('长按识别·喵呜～', unit * 132, unit * 390);
        ctx.save();
        // 绘制完成生成图片
        ctx.draw(false, () => {
          wx.canvasToTempFilePath({
            x: 0,
            y: 0,
            width: unit * canvasOptions.width,
            height: unit * canvasOptions.height,
            fileType: 'png',
            quality: 1,
            canvasId: 'sharePanelCanvas',
            success: (res) => {
              this.setData({
                canvasFilePath: res.tempFilePath
              })
            }
          }, this)
        });
      }, (err) => {
        wx.showToast({
          title: '图片生成失败！',
          icon: 'none',
          duration: 2000,
        })
      });
    },
    /**
     * @description:canvas绘制文字换行解决方案 measureText同步并且方法性能堪忧，只用作于渲染多行文本，单行文本还是用fillText;
     */
    drawText(ctx, str, left, top, height, canvasWidth, fontSize) {
      let lineWidth = 0;
      let lastSubStrIndex = 0;
      ctx.setFontSize(fontSize);
      for (let i = 0; i < str.length; i++) {
        lineWidth += ctx.measureText(str[i]).width;
        if (lineWidth > canvasWidth) {
          ctx.fillText(str.substring(lastSubStrIndex, i), left, top); //绘制截取部分
          top += 16; //16为字体的高度
          lineWidth = 0;
          lastSubStrIndex = i;
          height += 30;
        }
        if (i === str.length - 1) { //绘制剩余部分
          ctx.fillText(str.substring(lastSubStrIndex, i + 1), left, top);
        }
      }
      return height;
    },
    /**
     *
     * @param {wx.CanvasContext} ctx canvas上下文
     * @param {number} x 圆角矩形选区的左上角 x坐标
     * @param {number} y 圆角矩形选区的左上角 y坐标
     * @param {number} w 圆角矩形选区的宽度
     * @param {number} h 圆角矩形选区的高度
     * @param {number} r 圆角的半径
     * @param{string} c 边框颜色
     */
    roundRect(ctx, x, y, w, h, r, c) {  //绘制圆角矩形(无填充色))
      ctx.save();
      if (w < 2 * r) {
        r = w / 2;
      }
      if (h < 2 * r) {
        r = h / 2;
      }
      ctx.beginPath();
      ctx.setStrokeStyle(c);
      ctx.setLineWidth(1);
      ctx.moveTo(x + r, y);
      ctx.arcTo(x + w, y, x + w, y + h, r);
      ctx.arcTo(x + w, y + h, x, y + h, r);
      ctx.arcTo(x, y + h, x, y, r);
      ctx.arcTo(x, y, x + w, y, r);
      ctx.stroke();
      ctx.closePath();
    },
    // 绘制圆角矩形
    drawRoundRectPath(cxt, width, height, radius) {
      cxt.beginPath(0);
      //从右下角顺时针绘制，弧度从0到1/2PI
      cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

      //矩形下边线
      cxt.lineTo(radius, height);

      //左下角圆弧，弧度从1/2PI到PI
      cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

      //矩形左边线
      cxt.lineTo(0, radius);

      //左上角圆弧，弧度从PI到3/2PI
      cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

      //上边线
      cxt.lineTo(width - radius, 0);

      //右上角圆弧
      cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

      //右边线
      cxt.lineTo(width, height - radius);
      cxt.closePath();
    },
    // 绘制带背景的矩形
    fillRoundRect(cxt, x, y, width, height, radius, fillColor) {
      //圆的直径必然要小于矩形的宽高
      if (2 * radius > width || 2 * radius > height) {
        return false;
      }
      cxt.save();
      cxt.translate(x, y);
      //绘制圆角矩形的各个边
      this.drawRoundRectPath(cxt, width, height, radius);
      cxt.fillStyle = fillColor || "#fff";
      cxt.fill();
      cxt.restore();
    },

      drawActivityPriceIcon(ctx,info,unit,x,y){
          ctx.save();
          this.fillOneSideRoundRect(ctx,unit * x,y * unit,34 * unit,13 * unit,2 * unit,'#40332A','left')
          ctx.save();
          ctx.setFillStyle('#F5D5AE');
          ctx.setFontSize(unit * 10);
          let text1 = '会员价';
          ctx.fillText(text1, unit * (x + 2), unit * (y + 10));
          ctx.save()
          let text2 = `${this.data.currencySymbol}${info.activityPrice}`;
          this.fillOneSideRoundRect(ctx,unit * (x + 34),y * unit,(text2.length * 10 + 4) * unit,13 * unit,2 * unit,'#F1B368','right');
          ctx.save();
          ctx.setFillStyle('#40332A');
          ctx.setFontSize(unit * 10);
          ctx.fillText(text2, unit * (x + 4 + 34), unit * (y + 10));
          ctx.save()
      },

      // 绘制带背景的矩形
      fillOneSideRoundRect(cxt, x, y, width, height, radius, fillColor,type = 'right') {
          //圆的直径必然要小于矩形的宽高
          if (2 * radius > width || 2 * radius > height) {
              return false;
          }
          cxt.save();
          cxt.translate(x, y);
          //绘制圆角矩形的各个边
          this.drawOneSideRoundRectPath(cxt, width, height, radius,type);
          cxt.fillStyle = fillColor || "#fff";
          cxt.fill();
          cxt.restore();
      },

    // 绘制一侧带圆角的矩形
      drawOneSideRoundRectPath(cxt, width, height, radius,type = 'right'){
          cxt.beginPath(0);
          if(type === 'right'){
              //从右下角顺时针绘制，弧度从0到1/2PI
              cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);
              //矩形下边线
              cxt.lineTo(0, height);
              //矩形左边线
              cxt.lineTo(0, 0);

              //上边线
              cxt.lineTo(width, 0);

              //右上角圆弧
              cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

              //右边线
              cxt.lineTo(width, height - radius);
          }
          if(type === 'left'){
              //矩形下边线
              cxt.lineTo(0, height);

              //左下角圆弧，弧度从1/2PI到PI
              cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

              //矩形左边线
              cxt.lineTo(0, radius);

              //左上角圆弧，弧度从PI到3/2PI
              cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

              //上边线
              cxt.lineTo(width, 0);
              //右边线
              cxt.lineTo(width, height);
          }



          cxt.closePath();
      }

  }
});
