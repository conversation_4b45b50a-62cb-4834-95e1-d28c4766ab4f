/**
 * @description:业务组件-联系商家
 * @interface:
 *  serviceInfo {
 *    telephone：对应门店联系方式1
      mobile：对应门店联系方式2
 *  }
 * **/
Component({
  properties: {
    serviceInfo: {
      type: Object,
      value: {}
    },
    show: {
      type: Boolean,
      value: false
    }
  },
  methods: {
    onMaskTap() {
      this.setData({
        show: false
      })
    },

    onPhoneCallTap(evt) {
      const phoneNumber = evt.currentTarget.dataset.number;
      wx.makePhoneCall({
        phoneNumber
      })
    }
  }
});
