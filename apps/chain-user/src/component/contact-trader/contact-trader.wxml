<view class="mp-common-contact-trader--container {{show ? 'show' : ''}}" wx:if="{{show}}" catchtouchmove="true">
  <view class="mp-common-contact-trader--container__mask" catchtap="onMaskTap"></view>
  
  <!-- 联系客服 -->
  <view class="mp-common-contact-trader--container__services" wx:if="{{serviceInfo.mobile || serviceInfo.telephone}}">
    <view class="mp-common-contact-trader--container__services--header">联系客服</view>
    <view class="mp-common-contact-trader--container__services--infos">
      <view class="mp-common-contact-trader--container__services--infos--text">客服热线：</view>
      <view class="mp-common-contact-trader--container__services--infos--telephone">
        <view bindtap="onPhoneCallTap"
              wx:if="{{serviceInfo.telephone}}"
              data-number="{{serviceInfo.telephone}}"
              class="mp-common-contact-trader--container__services--infos--telephone__number">
          {{serviceInfo.telephone}}
        </view>
        <view bindtap="onPhoneCallTap"
              wx:if="{{serviceInfo.mobile}}"
              data-number="{{serviceInfo.mobile}}"
              class="mp-common-contact-trader--container__services--infos--telephone__number">
          {{serviceInfo.mobile}}
        </view>
      </view>
    </view>
    <view class="mp-common-contact-trader--container__services--times">
      门店营业时间：全天
      <text>{{serviceInfo.start}} - {{serviceInfo.end}}</text>
    </view>
  </view>
  
  <!-- 门店未配置 -->
  <view wx:else class="mp-common-contact-trader--container__empty">
    <view class="mp-common-contact-trader--container__empty--close" catchtap="onMaskTap">X</view>
    <view class="mp-common-contact-trader--container__empty--tips">
      商家暂时没有留下联系方式哦～
    </view>
  </view>
</view>
