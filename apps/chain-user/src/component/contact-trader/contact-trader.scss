@import "config/theme";

@mixin mp-component-contact-trader-theme($theme) {
  $primary: map-get($theme, primary);

  .mp-common-contact-trader {
    &--container {
      position: fixed;
      z-index: 9999;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

      $container: &;

      &.show {
        #{$container}__mask {
          display: block;
        }
      }

      &__mask {
        position: absolute;
        height: 100%;
        width: 100%;
        background-color: #2E2E2E;
        opacity: 0.35;
        display: none;
      }

      &__services {
        padding: u(44) 0 u(28);
        text-align: center;
        background-color: #FFFFFF;
        border-radius: u(22);
        position: absolute;
        left: 50%;
        top: u(308);
        transform: translateX(-45%);
        animation: fadeIn .3s linear;
        width: u(464);

        &--header {
          text-align: center;
          font-size: u(32);
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #3C3C3C;
          line-height: u(44);
        }

        &--infos {
          margin-top: u(24);
          display: flex;
          justify-content: center;

          &--text {
            width: u(160);
            text-align: center;
            font-size: u(24);
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #3C3C3C;
            line-height: u(44);
          }

          &--telephone {
            text-align: left;

            &__number {
              font-size: u(28);
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: mat-color($primary);
              line-height: u(44);
              margin-bottom: u(24);
              text-decoration: underline;
            }
          }
        }

        &--times {
          font-size: u(24);
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #B0B0B0;
          line-height: u(44);
          text-align: center;
        }
      }

      &__empty {
        width: u(464);
        padding: u(24) u(32);
        background-color: #fff;
        border-radius: u(22);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -45%);
        animation: fadeIn .3s linear;

        &--close {
          text-align: right;
        }

        &--tips {
          font-size: u(28);
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #444444;
          line-height: u(44);
          text-align: center;
          margin-top: (16);
        }
      }
    }
  }
}

@include mp-component-contact-trader-theme($lucky-theme);
