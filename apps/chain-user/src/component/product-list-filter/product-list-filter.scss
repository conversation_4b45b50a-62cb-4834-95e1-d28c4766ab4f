@import 'config/theme';
@import 'styles/variable/common';
@mixin wp-crown-shopping-product-list-filter-theme($theme) {
  @include wp-crown-shopping-product-list-filter-basic-theme($theme);
  @include wp-crown-shopping-product-list-filter-conditions-theme($theme);
  @include wp-crown-shopping-product-list-filter-buttons-theme($theme);
}

@mixin wp-crown-shopping-product-list-filter-basic-theme($theme) {
  $accent: map-get($theme, accent);
  .wp-crown-shopping-product-list-filter {
    &__backdrop {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 5;

      width: 100%;
      height: 100%;

      background-color: #000;

      opacity: .5;
    }

    &__main-container {
      position: fixed;
      right: 0;
      top: 0;
      width: u(654);
      height: 100%;
      border-radius: u(24) 0 0 u(24);
      background-color: #fff;
      z-index: 10;

      &--scroll {
        overflow-y: scroll;
        height: 100%;
        position: absolute;
        width: 100%;
        padding-bottom: u(128);
        box-sizing: border-box;
      }
    }

    &__main-container.useTabBar {

      height: calc(100% - #{u($customTabBarHeight)} - env(safe-area-inset-bottom) + 20rpx);
    }

    &__line {
      width: u(40);
      height: u(2);
      margin: 0 u(10);

      background-color: #d4d4d4;
    }

    &__input {
      &-container {
        width: u(280);
        height: u(64);
        border-radius: u(32);

        background-color: #f1f1f1;

        display: flex;
        align-items: center;
      }

      &-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;

        font-size: u(22);
        color: #b0b0b0;
        // border: 1px solid #b0b0b0;
      }

      &-text {
        text-align: center;
      }

      &-focus {
        width: u(280);
        height: u(64);
        border-radius: u(32);
        border: 1px solid mat-color($accent);
      }
    }
  }
}

@mixin wp-crown-shopping-product-list-filter-conditions-theme($theme) {
  $foreground: map-get($theme, foreground);
  $accent: map-get($theme, accent);
  $background: map-get($theme, background);
  .wp-crown-shopping-product-list-filter {
    &__conditions {
      // flex-grow: 1;

      padding: u(22) u(30);
      border-bottom: u(2) solid #f1f1f1;

      &-price {
        display: flex;
        flex-direction: column;

        margin-top: u(24);
        margin-bottom: u(24);

        &-title {
          font-size: u(28);
          color: map-get($foreground, hint-text);
          font-weight: 600;
          font-family: PingFangSC-Semibold, PingFang SC;
        }

        &-inputs {
          display: flex;
          align-items: center;
          justify-content: space-between;

          margin-top: u(42);
        }
      }
    }

    &__info {
      &-filter {
        padding: u(0) u(30);
        border-bottom: u(2) solid #f1f1f1;

        &-item {
          display: flex;
          align-items: center;
          justify-content: space-between;

          margin-top: u(48);
          margin-bottom: u(32);

          &-title {
            font-size: u(28);
            color: map-get($foreground, hint-text);
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
          }
        }

        &__arrow {
          width: u(32);
          height: u(32);

          &-image {
            width: u(32);
            height: u(32);
          }

          &--active {
            transform: rotate(180deg);
          }
        }
      }
    }

    &__scroll {
      height: u(256);

      &--more {
        height: auto;
        overflow: visible;
      }

      &--height {
        height: u(290);
        overflow: hidden;
      }

      &__region {
        height: u(256);
        overflow: hidden;

        &--more {
          height: auto;
          overflow: visible;
        }

        &--height {
          height: u(290);
          overflow: hidden;
        }
      }
    }

    &__selectors {
      display: flex;
      flex-wrap: wrap;
      font-size: u(24);

      &-item {
        display: flex;
        height: u(64);
        background: #F1F1F1;
        border-radius: u(36);
        line-height: u(64);
        padding: 0 u(30);
        margin-right: u(16);
        margin-bottom: u(32);
        border: 1px solid #f1f1f1;
        color: map-get($foreground, hint-text);

        &--selected {
          color: mat-color($accent);
          border: 1px solid mat-color($accent);
          background-color: map-get($background, background);
        }

        &--inactive {
          color: #B0B0B0;
        }
      }
    }

    &__hint {
      font-size: u(24);
      color: #B0B0B0;
    }
  }
}

@mixin wp-crown-shopping-product-list-filter-buttons-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  .wp-crown-shopping-product-list-filter {
    &__buttons {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: u(24);

      height: u(136);
      padding: u(16) u(30);
      margin-bottom: u(20);
      position: absolute;
      width: 100%;
      bottom: 0;
      box-sizing: border-box;
      background-color: #fff;

      &-reset {
        display: flex;
        justify-content: center;
        align-items: center;

        width: u(270);
        height: u(80);
        border-radius: u(40);

        color: #444444;
        border: 1px solid #B0B0B0;

      }

      &-confirm {
        display: flex;
        justify-content: center;
        align-items: center;

        width: u(270);
        height: u(80);
        border-radius: u(40);

        color: #fff;
        background: mat-color($primary);
      }
    }
  }
}

@include wp-crown-shopping-product-list-filter-theme($lucky-theme);
