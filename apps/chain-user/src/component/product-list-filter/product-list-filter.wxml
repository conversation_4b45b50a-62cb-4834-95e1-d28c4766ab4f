<view class="wp-crown-shopping-product-list-filter__wrapper">
  <view class="wp-crown-shopping-product-list-filter__backdrop" bindtap="onCloseTap" catchtouchmove="true"></view>
  <view class="wp-crown-shopping-product-list-filter__main-container {{canUseTabBar ? 'useTabBar' : ''}}">
    <view class="wp-crown-shopping-product-list-filter__main-container--scroll">
      <view class="wp-crown-shopping-product-list-filter__conditions">
        <view class="wp-crown-shopping-product-list-filter__conditions-price" style="padding-top: {{statusBarHeight}}rpx">
          <view class="wp-crown-shopping-product-list-filter__conditions-price-title">点击下方筛选商品</view>
          <view class="wp-crown-shopping-product-list-filter__conditions-price-inputs">
            <view class="wp-crown-shopping-product-list-filter__input-container">
              <input type="number" placeholder="最低价"
                placeholder-class="wp-crown-shopping-product-list-filter__input-placeholder"
                class="wp-crown-shopping-product-list-filter__input-text {{minInputFocus ? 'wp-crown-shopping-product-list-filter__input-focus' : ''}}"
                bindfocus="onMinInputFocus"
                bindblur="onMinInputBlur"
                value="{{item.minPrice}}"
                bindinput="onMinPriceChange"
              />
            </view>

            <view class="wp-crown-shopping-product-list-filter__line"></view>

            <view class="wp-crown-shopping-product-list-filter__input-container">
              <input type="number" placeholder="最高价"
                placeholder-class="wp-crown-shopping-product-list-filter__input-placeholder"
                class="wp-crown-shopping-product-list-filter__input-text {{maxInputFocus ? 'wp-crown-shopping-product-list-filter__input-focus' : ''}}"
                bindfocus="onMaxInputFocus"
                bindblur="onMaxInputBlur"
                value="{{item.maxPrice}}"
                bindinput="onMaxPriceChange"
              />
            </view>
          </view>
        </view>
      </view>
    <!--品牌 产地筛选-->
      <view class="wp-crown-shopping-product-list-filter__info-filter">
        <view class="wp-crown-shopping-product-list-filter__info-filter-item wp-crown-shopping-product-list-filter__info-filter-item-brand"
              bindtap="onShowMoreBrandList"
              wx:if="{{categoryType !== 'PACKAGE'}}" data-type="brand">
          <view class="wp-crown-shopping-product-list-filter__info-filter-item-title">
            品牌
          </view>
          <view class="wp-crown-shopping-product-list-filter__info-filter__arrow {{isShowMoreBrandList ? 'wp-crown-shopping-product-list-filter__info-filter__arrow--active': ''}}">
            <image class="wp-crown-shopping-product-list-filter__info-filter__arrow-image" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.GNIuHbhNA5CP95d1aa9b80d0865ee222abcad7ee4e65.svg" />
          </view>
        </view>
        <view class="wp-crown-shopping-product-list-filter__scroll {{isShowMaxHeightOfBrand ? 'wp-crown-shopping-product-list-filter__scroll--height' : ''}} {{isShowMoreBrandList ? 'wp-crown-shopping-product-list-filter__scroll--more': ''}}">
          <view class="wp-crown-shopping-product-list-filter__selectors">
            <block wx:for="{{brandDetail}}" wx:key="brand" wx:for-item="brand">
              <view class="wp-crown-shopping-product-list-filter__selectors-item {{!brand.available || !brand.selectable ? 'wp-crown-shopping-product-list-filter__selectors-item--inactive' : ''}} {{brand.selected ? 'wp-crown-shopping-product-list-filter__selectors-item--selected' : ''}}"
                data-item="{{brand}}"
                data-type="brand"
                bindtap="onSelectTapBrand">
                {{brand.name}}
              </view>
            </block>
            <view class="wp-crown-shopping-product-list-filter__hint" wx:if="{{brandDetail.length === 0}}">暂无数据</view>
          </view>
        </view>
      </view>
      <view class="wp-crown-shopping-product-list-filter__info-filter">
        <view class="wp-crown-shopping-product-list-filter__info-filter-item"
              bindtap="onShowMoreRegionList"
              wx:if="{{categoryType !== 'PACKAGE'}}"
              data-type="region">
          <view class="wp-crown-shopping-product-list-filter__info-filter-item-title">
            产地
          </view>
          <view class="wp-crown-shopping-product-list-filter__info-filter__arrow {{isShowMoreRegionList ? 'wp-crown-shopping-product-list-filter__info-filter__arrow--active' : ''}}">
            <image class="wp-crown-shopping-product-list-filter__info-filter__arrow-image" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.GNIuHbhNA5CP95d1aa9b80d0865ee222abcad7ee4e65.svg" />
          </view>
        </view>
        <view class="wp-crown-shopping-product-list-filter__scroll__region {{isShowMaxHeightOfRegion ? '' : ''}} {{isShowMoreRegionList ? 'wp-crown-shopping-product-list-filter__scroll__region--more': ''}}">
          <view class="wp-crown-shopping-product-list-filter__selectors">
            <block wx:for="{{regionList}}" wx:key="brand" wx:for-item="region">
              <view class="wp-crown-shopping-product-list-filter__selectors-item {{!region.available || !region.selectable ? 'wp-crown-shopping-product-list-filter__selectors-item--inactive' : ''}} {{region.selected ? 'wp-crown-shopping-product-list-filter__selectors-item--selected' : ''}}"
                data-item="{{region}}"
                data-type="region"
                bindtap="onSelectTapRegion">
                {{region.name}}
              </view>
            </block>
          <view class="wp-crown-shopping-product-list-filter__hint" wx:if="{{regionList.length === 0}}">暂无数据</view>
        </view>
      </view>
      </view>
    </view>

    <view class="wp-crown-shopping-product-list-filter__buttons">
      <view class="wp-crown-shopping-product-list-filter__buttons-reset" bindtap="onResetTap">重置</view>
      <view class="wp-crown-shopping-product-list-filter__buttons-confirm" bindtap="onFilterTap" data-item="{{item}}">应用</view>
    </view>
  </view>
</view>
