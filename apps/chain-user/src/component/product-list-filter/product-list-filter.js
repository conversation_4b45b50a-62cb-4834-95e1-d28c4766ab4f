Component({
  properties: {
    item: {
      type: Object,
      value: {},
    },
    regionList: {
      type: Array,
      value: [],
    },
    brandDetail: {
      type: Array,
      value: [],
    },
    type: {
      type: String,
      value: ''
    }
  },
  data: {
    // 组件内部的数据
    minPrice: null,
    maxPrice: null,
    minInputFocus: false,
    maxInputFocus: false,

    isSelected: false,
    isShowMaxHeightOfBrand: false,
    isShowMaxHeightOfRegion: false,
    isShowMoreRegionList: false,
    isShowMoreBrandList: false,
    canUseTabBar: true,
    isIphone: false,
    statusBarHeight: 20
  },
  ready() {

    const {
      canUseTabBar,
      isIphone
    } = getApp().globalData;

    if (this.data.brandDetail.length && this.data.brandDetail.length >= 8) {
      this.setData({
        isShowMaxHeightOfBrand: !this.data.isShowMaxHeightOfBrand,
        canUseTabBar,
        isIphone
      });
    } else if (this.data.regionList.length && this.data.regionList.length >= 8) {
      this.setData({
        isShowMaxHeightOfRegion: !this.data.isShowMaxHeightOfRegion,
        canUseTabBar,
        isIphone
      });
    }

    wx.getSystemInfo({
      success: (res) => {
        const statusBarHeight = res.statusBarHeight;
        this.setData({
          statusBarHeight
        })
      }
    })

  },

  methods: {
    // 自定义方法
    onMinPriceChange(ev) {
      if (ev.detail.value && ev.detail.value.trim()) {
        this.setData({
          minPrice: ev.detail.value.trim()
        });
      }
    },

    onMaxPriceChange(ev) {
      if (ev.detail.value && ev.detail.value.trim()) {
        this.setData({
          maxPrice: ev.detail.value.trim()
        });
      }
    },

    onMinInputFocus() {
      this.setData({
        minInputFocus: true,
      })
    },

    onMinInputBlur() {
      this.setData({
        minInputFocus: false,
      })
    },

    onMaxInputFocus() {
      this.setData({
        maxInputFocus: true,
      })
    },

    onMaxInputBlur() {
      this.setData({
        maxInputFocus: false,
      })
    },

    onResetTap() {
      // 避免筛选价格等相关信息为空时，重置触发接口
      const isSelectedBrand = this.data.brandDetail.find(item => item.selected);
      const isSelectedRegion = this.data.regionList.find(item => item.selected);
      const item = this.data.item;
      if (this.data.item.minPrice || this.data.item.maxPrice || isSelectedBrand || isSelectedRegion) {
        this.triggerEvent('reset', {item, isSelectedBrand, isSelectedRegion});
      } else {
        let item = {
          minPrice: null,
          maxPrice: null
        };

        this.data.brandDetail.forEach(brand => {
          brand.selected = false;
          brand.selectable = true;
          brand.available = true;
        });

        this.data.regionList.forEach(region => {
          region.selected = false;
          region.selectable = true;
          region.available = true;
        });

        this.setData({
          ...item,
          item,
          brandDetail: this.data.brandDetail,
          regionList: this.data.regionList
        });
      }
    },

    onCloseTap() {
      this.triggerEvent('close');
    },

    onFilterTap(ev) {
      const item = ev.currentTarget.dataset.item;
      if (!this.data.minPrice && !this.data.maxPrice) {
        this.triggerEvent('filter', item)
      } else {
        this.triggerEvent('filter', {
          ...this.data,
          minPrice: !this.data.minPrice ? item.minPrice : this.data.minPrice,
          maxPrice: !this.data.maxPrice ? item.maxPrice : this.data.maxPrice
        });
      }
      this.onCloseTap();
    },

    onSelectTapBrand(ev) {
      const item = ev.currentTarget.dataset.item;
      const type = ev.currentTarget.dataset.type;
      if (item.available && item.selectable) {
        this.triggerEvent('brand', {item, type});
      }
    },

    onSelectTapRegion(ev) {
      const item = ev.currentTarget.dataset.item;
      const type = ev.currentTarget.dataset.type;
      if (item.available && item.selectable) {
        this.triggerEvent('region', {item, type});
      }
    },

    onShowMoreBrandList() {
      if (this.data.brandDetail && this.data.brandDetail.length >= 8) {
        this.setData({
          isShowMoreBrandList: !this.data.isShowMoreBrandList,
          isShowMaxHeightOfBrand: !this.data.isShowMaxHeightOfBrand,
        });
      }
    },

    onShowMoreRegionList() {
      if (this.data.regionList && this.data.regionList.length >= 8) {
        this.setData({
          isShowMoreRegionList: !this.data.isShowMoreRegionList,
          isShowMaxHeightOfRegion: !this.data.isShowMaxHeightOfRegion,
        });
      }
    },
  }
});
