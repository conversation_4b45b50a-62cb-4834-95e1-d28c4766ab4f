// 业务组件：联系客服功能
Component({
  properties: {
    serviceInfo: {
      type: Object,
      value: {}
    },
    show: {
      type: Boolean,
      value: false
    }
  },
  lifetimes: {
    ready: function() {
      // 在组件实例进入页面节点树时执行
    },
  },
  methods: {
    // 拨打电话
    _onModalConfirm() {
      const phoneNumber = String(this.data.serviceInfo.mobile);
      wx.makePhoneCall({
        phoneNumber,
      })
    },
  }
})
