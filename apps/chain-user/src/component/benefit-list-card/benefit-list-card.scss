@import 'config/theme';

@mixin mp-crown-health-benefit-list-card-component($theme) {
  .mp-crown-health-benefit-card-list {
    &__card {
      &-wrapper {
        display: flex;
        justify-content: space-between;

        width: u(700);
        height: u(220);

        margin-top: u(24);
        border-radius: u(8);
        background-color: #fff;
        box-shadow: 0 u(4) u(12) 0 rgba(0,0,0,0.1);

        &__inactive__image{
          height: 100%;
          width: 100%;
        }
      }
      &-wrapper.inactive{
        height: u(148);
      }



      &-main-content {
        margin-top: u(32);
        margin-left: u(32);

        &-title {
          margin-bottom: u(10);

          color: #2f2f2f;
          font-size: u(32);

          height: u(48);
          line-height: u(48);
        }

        &-subtitle {
          font-size: u(28);
          color: #9397a2;
          height: u(44);
          line-height: u(44);
        }
      }

      &-image {
        position: relative;

        width: u(176);
        height: u(220);

        &-wrapper-mask {
          position: relative;

          width: 100%;
          height: 100%;

          border-radius: 0 u(8) u(8) 0;

          &--inactive {
            background: linear-gradient(180deg, #fb9e0f 0%, #ff6b49 100%);
          }

          &--invalid {
            background: linear-gradient(180deg, #cdcdcd 0%, #9397a2 100%);
          }

          &::after {
            position: absolute;
            top: 0;
            left: 0;

            width: 100%;
            height: 100%;

            content: '';
            background-color: #000;
            opacity: .2;
          }

        }

        &-wrapper {
          position: relative;

          width: 100%;
          height: 100%;

          border-radius: 0 u(8) u(8) 0;

          &--inactive {
            background: linear-gradient(180deg, #fb9e0f 0%, #ff6b49 100%);
          }

          &--invalid {
            background: linear-gradient(180deg, #cdcdcd 0%, #9397a2 100%);
          }
        }

        &-text {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          position: absolute;
          top: 50%;
          left: 50%;

          width: u(176);
          padding-right: u(80);

          transform: translate(-50%, -50%);

          color: #fff;
          font-size: u(32);
          font-weight: 500;
        }

        &-title {
          display: flex;
          justify-content: center;
        }

        &-days {
          &-icon {
            position: absolute;
            top: u(25);
            right: u(-30);

            font-size: u(24);
          }

          &-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-top: u(-12);
          }

          &-number {
            display: flex;
            align-items: baseline;

            // margin-right: u(14);

            font-size: u(48);

            &-text {
              transform: translateY(u(-4));
              margin-left: u(8);

              font-size: u(28);
            }
          }
        }
      }
    }
  }
}

@include mp-crown-health-benefit-list-card-component($app-theme);
