<block wx:for="{{list}}" wx:for-item="card" wx:key="card">
  <!-- 未激活 -->
  <view wx:if="{{card.status==='INACTIVE'}}"
        data-card="{{card}}"
        bindtap="onItemTap"
        class="mp-crown-health-benefit-card-list__card-wrapper inactive">
    <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.BHgvAUtf8y3F510b4e5d9d7ac714ff8a9b21962e2d91.png"
           class="mp-crown-health-benefit-card-list__card-wrapper__inactive__image"/>
  </view>
  <!--已激活-->
  <view class="mp-crown-health-benefit-card-list__card-wrapper"
        wx:else
        data-card="{{card}}"
        bind:tap="onItemTap">
    <view class="mp-crown-health-benefit-card-list__card-main-content">
      <view class="mp-crown-health-benefit-card-list__card-main-content-title">
        {{card.cardName}}
      </view>
      <view class="mp-crown-health-benefit-card-list__card-main-content-subtitle">
        保障期：{{card.effectiveDateText}} 至 {{card.expirationDateText}}
      </view>
    </view>
    <view class="mp-crown-health-benefit-card-list__card-image">
      <!-- 观察期与生效期 -->
      <image lazy-load="true" wx:if="{{card.isShowDaysInfo}}" src="{{card.image}}" class="mp-crown-health-benefit-card-list__card-image-wrapper-mask" />
      <!-- 失效 -->
      <view wx:if="{{card.status === 'EXPIRED'}}" class="mp-crown-health-benefit-card-list__card-image-wrapper mp-crown-health-benefit-card-list__card-image-wrapper--inactive"></view>
      <!-- 作废 -->
      <view wx:if="{{card.status === 'INVALID'}}" class="mp-crown-health-benefit-card-list__card-image-wrapper mp-crown-health-benefit-card-list__card-image-wrapper--invalid"></view>
      
      <view class="mp-crown-health-benefit-card-list__card-image-text">
        <view class="mp-crown-health-benefit-card-list__card-image-title">{{card.statusText}}</view>
        <view class="mp-crown-health-benefit-card-list__card-image-days-wrapper" wx:if="{{card.isShowDaysInfo}}">
          <view class="mp-crown-health-benefit-card-list__card-image-days-number">
            {{card.status === 'OBSERVATION' ? card.observationDays : card.effectivedDays}}
            <view class="mp-crown-health-benefit-card-list__card-image-days-number-text">天</view>
          </view>
          <view class="PETKIT icon-right-arrow iconfont mp-crown-health-benefit-card-list__card-image-days-icon"></view>
        </view>
      </view>
    </view>
  </view>
</block>
