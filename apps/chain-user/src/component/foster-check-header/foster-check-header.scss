@import "config/theme";

@mixin wp-component-foster-check-header-theme($theme) {
  $primary: map-get($theme, primary);
  .wp-component-foster-check-header {
    height: u(64);
    position: relative;
    display: flex;
    align-items: center;
    font-family: PingFangSC-Semibold, PingFang SC;

    &--date {
      position: absolute;
      z-index: 2;
      left: 0;
      height: 100%;
      background-color: #FEFEFE;
      padding: 0 u(16);

      font-size: u(48);
      font-weight: 600;
      color: #2F2F2F;
      line-height: u(64);
      font-family: PingFangSC-Semibold, PingFang SC;

      &-number {
        font-size: u(48);
      }

      &-font {
        margin-left: u(4);
        font-size: u(32);
      }
    }

    &--line {
      width: 100%;
      height: u(2);
      background-color: #F1F1F1;
      border-radius: u(4)
    }
  }
}

@include wp-component-foster-check-header-theme($lucky-theme);
