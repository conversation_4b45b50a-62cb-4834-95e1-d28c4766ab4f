Component({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
      currencySymbol:{
          type:String,
          value:'￥'
      }
  },
  data: {
    // 组件内部的数据
  },
  ready() {
  },

  methods: {
    // 自定义方法
    onAddToShoppingCartTap(ev) {
      const detail = ev.currentTarget.dataset.detail
      this.triggerEvent('addTap', {
        data: detail,
        touches: ev.touches,
      });
    },
    onPreventTap() {
      this.triggerEvent('hint');
    }
  }
});
