<view class="wp-crown-shopping-product-package-list-card__wrapper">
  <view class="wp-crown-shopping-product-package-list-card__image-container  skeleton-rect">
    <image src="{{detail.image}}?x-oss-process=image/resize,w_168/quality,Q_80/format,jpg" />
    <image class="wp-crown-shopping-product-package-list-card__image-container-sold-out-img"
      wx:if="{{!detail.isQuantityEnough}}"
      src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/edb6485990c340d7a13f13f1430e1d5e?nsukey=hA%2Fw%2F13oKumKoYjVkUPkETTVVmRyLjgkKv7ST7J5nUYCKDHVSqbNQo8fuPX7SQrupOF1UTW7r0rEiwXxOz3loIIxl0Hw6GPcRi0VAaf2ITeC%2FgYWgv6mrtCvqxCU%2FKp%2Fi7R5IliUS%2Bi7XoOFdY2hlQ0bZsamwtbc7HGhU0mb9Hrbu99CeKmlt8Du6rz2AcRB6a2cgGceJkfFij3ehxyscQ%3D%3D" />
  </view>
  <view class="wp-crown-shopping-product-package-list-card__main-content">
    <view class="wp-crown-shopping-product-package-list-card__main-content-title skeleton-rect">
      {{detail.name}}
    </view>
    <view class="wp-crown-shopping-product-package-list-card__main-content-discount-info-container">
      <view class="wp-crown-shopping-product-package-list-card__main-content-discount-info-item" wx:if="{{detail.tags && detail.tags.length}}" wx:for="{{detail.tags}}" wx:key="index">{{item}}</view>
    </view>
    <view class="wp-crown-shopping-product-package-list-card__main-content-footer-container">
      <view class="wp-crown-shopping-product-package-list-card__main-content-footer-price-info skeleton-rect">
        <view class="wp-crown-shopping-product-package-list-card__main-content-footer-price-current">
          {{currencySymbol}} {{detail.price}}
        </view>
      </view>
      <image class="wp-crown-shopping-product-package-list-card__main-content-footer-icon skeleton-radius"
        src="../../images/common/add.svg"
        wx:if="{{detail.isQuantityEnough}}">
        <view class="wp-crown-shopping-product-package-list-card__main-content-footer-icon-tap"
          catchtap="onAddToShoppingCartTap"
          data-detail="{{detail}}"></view>
      </image>
      <view class="wp-crown-shopping-product-package-list-card__main-content-footer-icon-wrapper" wx:if="{{!detail.isQuantityEnough}}" catchtap="onPreventTap">
        <image class="wp-crown-shopping-product-package-list-card__main-content-footer-icon skeleton-radius" src="../../images/common/minus-disabled.svg" />
      </view>
    </view>
  </view>
</view>
