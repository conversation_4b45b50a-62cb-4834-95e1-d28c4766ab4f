@import 'config/theme';

@mixin wp-crown-shopping-product-package-list-card-theme($theme) {
  @include wp-crown-shopping-product-package-list-card-basic-theme($theme);
  @include wp-crown-shopping-product-package-list-card-image-theme($theme);
  @include wp-crown-shopping-product-package-list-card-main-content-theme($theme);
}

@mixin wp-crown-shopping-product-package-list-card-basic-theme($theme) {
  .wp-crown-shopping-product-package-list-card {
    &__wrapper {
      display: flex;
      flex-direction: column;

      width: u(260);
      height: u(356);
      border-radius: u(8);
      box-shadow: 0 2px 2px 0 #ebebeb;
    }
  }
}

@mixin wp-crown-shopping-product-package-list-card-image-theme($theme) {
  .wp-crown-shopping-product-package-list-card {
    &__image {
      &-container {
        display: flex;
        align-items: center;
        justify-content: center;

        position: relative;

        background-color: #fbfbfd;

        image {
          width: u(168);
          height: u(184);
          border-radius: u(8);
        }

        &-sold-out-img {
          position: absolute;
          top: 0;
          left: 50%;

          transform: translateX(-50%);
        }
      }
    }
  }
}

@mixin wp-crown-shopping-product-package-list-card-main-content-theme($theme) {
  .wp-crown-shopping-product-package-list-card {
    &__main-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;

      padding: u(16);

      &-title {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;

        line-height: u(28);

        font-size: u(24);
        color: #3a3f4a;
      }

      &-discount-info {
        &-container {
          display: flex;
        }

        &-item {
          margin-right: u(4);
          padding: u(2) u(4);
          border: u(2) solid #23b9de;
          border-radius: u(4);

          font-size: u(16);
          color: #23b9de;
        }
      }

       &-footer {
        &-container {
          display: flex;
          justify-content: space-between;
        }

        &-price {
          &-info {
            display: flex;
            align-items: flex-end;
          }

          &-current {
            color: #ff587d;
            font-size: u(28);
            margin-right: u(12);
          }

          &-original {
            transform: translateY(u(-4));
            color: #c4c9d2;
            font-size: u(20);
            text-decoration: line-through;
          }
        }

        &-icon {
          height: u(48);
          width: u(48);

          position: relative;
          overflow: visible;

          &-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;

          }

          &-tap {
            position: absolute;
            top: u(-20);
            left: u(-20);
            bottom: u(-20);
            right: u(-20);

            z-index: 1;
          }
        }
      }
    }
  }
}

@include wp-crown-shopping-product-package-list-card-theme($app-theme);
