Component({
  options: {
  },
  properties: {
    // 这里定义了innerText属性，属性值可以在组件使用时指定
    brand: {
      type: String,
      value: '',
    },
    expiration: {
      type: String,
      value: '',
    },
    category: {
      type: String,
      value: '',
    },
    region: {
      type: String,
      value: '',
    }
  },
  data: {
    items: [{
      name: '品牌',
      param: 'brand',
      text: '',
    }, {
    //   name: '保质期',
    //   param: 'expiration',
    //   text: '',
    // }, {
      name: '商品分类',
      param: 'category',
      text: '',
    }, {
      name: '原产地',
      param: 'region',
      text: '',
    }],
  },
  ready() {
    const items = this.data.items.map(item => ({
      name: item.name,
      param: item.param,
      text: this.data[item.param],
    }));

    this.setData({
      items,
    });
  },
  methods: {
    close() {
      this.triggerEvent('close');
    },
  }
});

