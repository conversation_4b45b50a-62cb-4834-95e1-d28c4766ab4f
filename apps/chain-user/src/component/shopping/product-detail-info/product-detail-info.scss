@import 'config/theme';
@import '~@petkit/style/icon/iconfont';

@mixin mp-shopping-product-detail-info($theme) {

  @keyframes easeHeightIn {
    0% {
      opacity: 0;
      height: 0;
    }
    100% { opacity: 100%; }
  }

  .mp-shopping-product-detail-info {
    &__wrapper {
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      overflow: hidden;
      background: rgba(0, 0, 0, .5);
      display: flex;
      align-items: flex-end;
      flex-direction: column;
    }

    &__backdrop {
      width: 100%;
      flex-grow: 1;
    }

    &__container {
      flex-grow: 0;
      width: 100%;
      height: u(720);
      background: #fff;
      border-radius: u(20) u(20) 0 0;
      padding: u(32) u(24) u(48);

      &--animation {
        animation: easeHeightIn .3s linear;
      }
    }

    &__header {
      position: relative;

      &-title {
        font-size: u(32);
        color: #575d6a;
        margin-bottom: u(30);
        text-align: center;
      }

      &-close {
        position: absolute;
        top: u(9);
        right: 0;
        width: u(32);
        height: u(32);
        font-size: u(36);
        color: #b0b0b0;
        display: flex;
        align-items: center;
        justify-content: center;

        &.PETKIT {
          font-size: u(36);
        }
      }
    }

    &__body {
      display: flex;
      flex-direction: column;
      // margin-bottom: u(120);
      margin-bottom: u(170);

      &-item {
        width: 100%;
        border-bottom: u(2) solid #f1f1f1;
        padding: u(24);
        display: flex;
        align-items: center;

        &_name {
          width: u(160);
          font-size: u(28);
          color: #9397a2;
        }

        &_text {
          flex-grow: 1;
          font-size: u(28);
          color: #575d6a;
        }
      }
    }

    &__footer {
      width: 100%;

      &-button {
        width: 100%;
        color: #fff;
        background: linear-gradient(180deg, rgba(55, 221, 243, 1) 0%, rgba(44, 176, 212, 1) 100%);
        border-radius: u(48);
        font-size: u(32);
      }
    }
  }
}

@include iu-icon-iconfont-theme($app-theme);
@include mp-shopping-product-detail-info($app-theme);
