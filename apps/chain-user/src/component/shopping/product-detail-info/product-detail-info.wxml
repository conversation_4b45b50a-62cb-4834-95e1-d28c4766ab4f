<view class="mp-shopping-product-detail-info__wrapper">
  <view class="mp-shopping-product-detail-info__backdrop" bind:tap="close"></view>
  <view class="mp-shopping-product-detail-info__container mp-shopping-product-detail-info__container--animation">
    <view class="mp-shopping-product-detail-info__header">
      <view class="mp-shopping-product-detail-info__header-title">产品参数</view>
      <view class="mp-shopping-product-detail-info__header-close PETKIT icon-close1" bind:tap="close"></view>
    </view>
    <view class="mp-shopping-product-detail-info__body">
      <view
        wx:for="{{items}}"
        class="mp-shopping-product-detail-info__body-item">
        <view class="mp-shopping-product-detail-info__body-item_name">{{item.name}}</view>
        <view class="mp-shopping-product-detail-info__body-item_text">{{item.text}}</view>
      </view>
    </view>
    <view class="mp-shopping-product-detail-info__footer">
      <button class="mp-shopping-product-detail-info__footer-button" bind:tap="close">完成</button>
    </view>
  </view>
</view>
