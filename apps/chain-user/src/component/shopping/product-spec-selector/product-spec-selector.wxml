<view class="mp-shopping-product-spec-selector__wrapper skeleton">
  <view catchtouchmove="true" class="mp-shopping-product-spec-selector__backdrop" bind:tap="close"></view>
  <view catchtouchmove="true" class="mp-shopping-product-spec-selector__container mp-shopping-product-spec-selector__container--animation {{specAttrList.length <= 1 ? 'mp-shopping-product-spec-selector__container--single' : 'mp-shopping-product-spec-selector__container--multiple'}}">
    <block wx:if="{{!detail.price}}"></block>
    <block wx:if="{{detail.price}}">
      <view class="mp-shopping-product-spec-selector__header skeleton-rect">
        <view class="mp-shopping-product-spec-selector__header-left">
          <image class="mp-shopping-product-spec-selector__header-image" src="{{detail._specImageList[0]}}" />
        </view>
        <view class="mp-shopping-product-spec-selector__header-right">
          <view class="mp-shopping-product-spec-selector__header-name">{{detail.name}}</view>
          <view class="mp-shopping-product-spec-selector__header-quantity" wx:if="{{detail.availableQuantity}}">库存{{detail.availableQuantity}}件</view>
          <view class="mp-shopping-product-spec-selector__header-price" wx:if="{{detail.price}}">{{currencySymbol}} {{detail.price}}
            <view wx:if="{{detail.activityPriceFlag}}" class="mp-shopping-product-spec-selector__header-activity-price">
              <view class="left">会员价</view>
              <view class="right">{{currencySymbol}}{{detail.activityPrice}}</view>
            </view>
          </view>
        </view>
        <view class="PETKIT icon-close1 mp-shopping-product-spec-selector__header-close" bind:tap="close"></view>
      </view>
      <view class="mp-shopping-product-spec-selector__body skeleton-rect {{specAttrList.length <= 1 ? 'mp-shopping-product-spec-selector__body--single' : 'mp-shopping-product-spec-selector__body--multiple'}}">
        <view
          class="mp-shopping-product-spec-selector__body-spec"
          wx:for="{{specAttrList}}"
          wx:key="index">
          <view class="mp-shopping-product-spec-selector__body-spec_name">{{item.name}}</view>
          <view class="mp-shopping-product-spec-selector__body-spec_list">
            <view
              bind:tap="selectSpecAttr"
              class="mp-shopping-product-spec-selector__body-spec_list-item {{attr.selected ? 'mp-shopping-product-spec-selector__body-spec_list-item--active' : ''}} {{attr.disabled ? 'mp-shopping-product-spec-selector__body-spec_list-item--disabled' : ''}}"
              data-attrId="{{attr.id}}"
              data-specId="{{item.id}}"
              data-disabled="{{attr.disabled}}"
              wx:for="{{item.attrList}}"
              wx:for-item="attr">{{attr.name}}</view>
          </view>
        </view>
        <view class="mp-shopping-product-spec-selector__body-number" wx:if="{{detail.price}}">
          <view class="mp-shopping-product-spec-selector__body-number_title">购买数量</view>
          <view class="mp-shopping-product-spec-selector__body-number_operator">
            <view
              data-type="minus"
              bind:tap="calculateNumber"
              class="PETKIT icon-minus mp-shopping-product-border-box mp-shopping-product-spec-selector__body-number_operator-minus {{detail.activityType !== 'DRAINAGE' && detail.number > 1 ? 'mp-shopping-product-spec-selector__body-number_operator-minus--active' : 0}}"></view>
            <view class="mp-shopping-product-spec-selector__body-number_operator-number">{{detail.number}}</view>
            <view
              data-type="plus"
              bind:tap="calculateNumber"
              class="PETKIT icon-plus mp-shopping-product-border-box mp-shopping-product-spec-selector__body-number_operator-plus {{detail.activityType !== 'DRAINAGE' && (detail.availableQuantity && detail.availableQuantity > detail.number) ? 'mp-shopping-product-spec-selector__body-number_operator-plus--active' : 0}}"></view>
          </view>
        </view>
      </view>
      <view class="mp-shopping-product-spec-selector__footer skeleton-rect">
        <button class="mp-shopping-product-spec-selector__footer-button {{!detail.number ? 'mp-shopping-product-spec-selector__footer-button--disabled' : ''}}" bind:tap="addCart" wx:if="{{!specSelectIsFromCart}}">{{mode === 'add' ? '加入购物车' : '立即购买'}}</button>
        <button class="mp-shopping-product-spec-selector__footer-button  {{!detail.number ? 'mp-shopping-product-spec-selector__footer-button--disabled' : ''}}" bind:tap="onUpdateCartTap" wx:else>确定</button>
      </view>
    </block>
  </view>
</view>
