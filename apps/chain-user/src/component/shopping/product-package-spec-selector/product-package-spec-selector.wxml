<view class="mp-shopping-product-spec-selector__wrapper skeleton">
  <view class="mp-shopping-product-spec-selector__backdrop" bind:tap="close"></view>
  <view class="mp-shopping-product-spec-selector__container mp-shopping-product-spec-selector__container--animation {{bottomBtnStyle === 'tabBar' ? 'useTabBar' : ''}} {{detail.specList.length <= 1 || !detail.specList ? 'mp-shopping-product-spec-selector__container--single' : 'mp-shopping-product-spec-selector__container--multiple'}}">
    <block wx:if="{{!detail.price}}"></block>
    <block wx:if="{{detail.price}}">
      <view class="mp-shopping-product-spec-selector__header skeleton-rect">
        <view class="mp-shopping-product-spec-selector__header-left">
          <image class="mp-shopping-product-spec-selector__header-image" src="{{detail._specImageList}}" />
        </view>
        <view class="mp-shopping-product-spec-selector__header-right">
          <view class="mp-shopping-product-spec-selector__header-name">{{detail.name}}</view>
          <view class="mp-shopping-product-spec-selector__header-quantity" wx:if="{{detail.availableQuantity}}">库存{{detail.availableQuantity}}件</view>
          <view class="mp-shopping-product-spec-selector__header-tags" wx:if="{{detail.tags && detail.tags.length}}">
            <view class="mp-shopping-product-spec-selector__header-tags-tag" wx:for="{{detail.tags}}" wx:key="index" wx:for-item="tag">
              {{tag}}
            </view>
          </view>
          <view class="mp-shopping-product-spec-selector__header-price" wx:if="{{detail.price}}">
            <view class="mp-shopping-product-spec-selector__header-price-origin">{{currencySymbol}}{{detail.price}}</view>
            <view class="mp-shopping-product-spec-selector__header-price-cost">{{detail.discountInfo}}</view>
          </view>
          <view class="mp-shopping-product-spec-selector__header-price"></view>
        </view>
        <view class="PETKIT icon-close1 mp-shopping-product-spec-selector__header-close" bind:tap="close"></view>
      </view>
      <view class="mp-shopping-product-spec-selector__body skeleton-rect {{detail.specList.length <= 1 ? 'mp-shopping-product-spec-selector__body--single' : 'mp-shopping-product-spec-selector__body--multiple'}}">
        <view
          class="mp-shopping-product-spec-selector__body-spec"
          wx:for="{{detail.specList}}"
          wx:key="index">
          <view class="mp-shopping-product-spec-selector__body-spec_name">{{item.name}}</view>
          <view class="mp-shopping-product-spec-selector__body-spec_list">
            <view
              bind:tap="selectSpecAttr"
              class="mp-shopping-product-spec-selector__body-spec_list-item {{attr.selected ? 'mp-shopping-product-spec-selector__body-spec_list-item--active' : ''}} {{attr.disabled ? 'mp-shopping-product-spec-selector__body-spec_list-item--disabled' : ''}}"
              data-attrId="{{attr.id}}"
              data-specId="{{item.id}}"
              data-disabled="{{attr.disabled}}"
              wx:for="{{item.attrs}}"
              wx:for-item="attr">{{attr.name}}</view>
          </view>
        </view>
        <view class="mp-shopping-product-spec-selector__body-number" wx:if="{{detail.price}}">
          <view class="mp-shopping-product-spec-selector__body-number_title">购买数量</view>
          <view class="mp-shopping-product-spec-selector__body-number_operator">
            <view
              data-type="minus"
              class="PETKIT icon-minus mp-shopping-product-border-box mp-shopping-product-spec-selector__body-number_operator-minus"></view>
            <view class="mp-shopping-product-spec-selector__body-number_operator-number">{{detail.number}}</view>
            <view
              data-type="plus"
              class="PETKIT icon-plus mp-shopping-product-border-box mp-shopping-product-spec-selector__body-number_operator-plus "></view>
          </view>
        </view>
      </view>
      <view class="mp-shopping-product-spec-selector__footer skeleton-rect">
        <button class="mp-shopping-product-spec-selector__footer-button {{detail.available === 0 ? 'mp-shopping-product-spec-selector__footer-button--disabled' : ''}}" bind:tap="onGoPackageDetailTap" wx:if="{{detail.optional === 1}}">选择套餐内商品</button>
        <button class="mp-shopping-product-spec-selector__footer-button  {{detail.available === 0 ? 'mp-shopping-product-spec-selector__footer-button--disabled' : ''}}" bind:tap="onAddCartTap" wx:else>加入购物车</button>
      </view>
    </block>
  </view>
</view>
