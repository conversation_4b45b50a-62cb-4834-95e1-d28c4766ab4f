import {
  updateProductPackageList
} from '@petkit/redux/src/flux/shopping/product-list.util';
Component({
  options: {
  },
  properties: {
    detail: {
      type: Object,
      value: {
        id: 0,
        name: '',
      }
    },
    bottomBtnStyle: {
      type: String,
      value: {
        type: 'tabBar'
      }
    },
      currencySymbol:{
        type:String,
          value:'￥'
      }
  },
  data: {
    animationData: null,

  },
  ready() {
  },
  methods: {
    show() {
      const animationData = wx.createAnimation({
        duration: 5000,
        timingFunction: 'ease',
        delay: 0,
      });
      this.setData({
        animationData,
      });
    },

    close() {
      this.triggerEvent('close');
    },

    selectSpecAttr({currentTarget: {dataset: {specid, attrid, disabled}}}) {
      if (!disabled) {
        this._updateSpecDetail(specid, attrid);
      } else {
        this.triggerEvent('message', {message: '库存不够了~'});
      }
    },

    _updateSpecDetail(specid, attrid) {
      const detail = this.data.detail;
      const {skuList, specList} = detail;
      // 勾选后新建一个新的选中的规格对象数组
      const selectedSpec = [];
      // update current select
      const _specList = specList.map(spec => {
        let attrs;
        if (spec.id === specid) {
          attrs = (spec.attrs || []).map((attr) => {
            if (attr.id === attrid) {
              selectedSpec.push({
                attrId: attr.id,
                attrName: attr.name,
                specId: spec.id,
                specName: spec.name,
              })
            }
            return {
              ...attr,
              selected: attr.id === attrid,
            }
          });
        } else {
          attrs = spec.attrs.map(attr => {
            if (attr.selected) {
              selectedSpec.push({
                attrId: attr.id,
                attrName: attr.name,
                specId: spec.id,
                specName: spec.name,
              })
            }
            return {
              ...attr
            }
          });
        }
        return {
          ...spec,
          attrs,
        }
      });

      // 通过specId, attrId, 找到currentSku
      const currentSku = skuList.find(sku => {
        // 新选的规格数组的长度
        const selectedSpecLen = selectedSpec.length;
        // skuList中匹配的规格数
        let findLen = 0;
        sku.specAttrs.forEach(skuSpecAttr => {
          findLen += selectedSpec.reduce((prev, curr) => (curr.specId === skuSpecAttr.specId && curr.attrId === skuSpecAttr.attrId) ? prev + 1 : prev, 0);
        });
        return findLen === selectedSpecLen;
      });

      const currentSelectedSpec = currentSku.specAttrs;
      const _changeSpec = updateProductPackageList(currentSelectedSpec, _specList, skuList);


      if (currentSku) {
        let _specImageList = '';
        if (currentSku.specImageList) {
          try {
            _specImageList = JSON.parse(currentSku.specImageList).result[0];
          } catch (err) {
            console.log(err);
          }
        }

        detail.name = currentSku.name;
        detail.price = currentSku.price;
        detail.tags = currentSku.tags;
        detail.available = 1;
        detail.optional = currentSku.optional;
        detail._specImageList = _specImageList;
        detail.number = 1;
        detail.currentSku = currentSku;

        detail.discountInfo = currentSku.discountInfo;

        detail.specList = _changeSpec;
        this.setData({
          detail,
        });
      }
    },

    addCart() {
      if (this.data.detail.availableQuantity >= this.data.detail.number) {
        const skuList = [];
        skuList.push({
          id: this.data.detail.id,
          number: this.data.detail.number,
        });

        this.triggerEvent('addcar', {skuList});
        this.close();
      }
    },

    onGoPackageDetailTap() {
      const detail = this.data.detail;
      if (detail.available === 1) {
        this.triggerEvent('godetail', {detail});
        this.close();
      } else {
        this.triggerEvent('message', {message: '库存不够了~'});
      }
    },

    onAddCartTap() {
      const detail = this.data.detail;
      if (detail.available === 1) {
        this.triggerEvent('addcar', {detail});
        this.close();
      } else {
        this.triggerEvent('message', {message: '库存不够了~'});
      }
    },
  }
});

