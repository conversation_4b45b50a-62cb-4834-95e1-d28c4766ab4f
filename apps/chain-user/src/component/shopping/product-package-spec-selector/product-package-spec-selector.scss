@import 'config/theme';
@import '~@petkit/style/icon/iconfont';
@import 'styles/variable/common';

@mixin mp-shopping-product-spec-selector($theme) {
  $single-spec-container-height: 840;
  $multiple-spec-container-height: 960;
  $header-height: 230;
  $footer-height: 130;
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $background: map-get($theme, background);

  @keyframes easeHeightIn {
    0% {
      opacity: 0;
      height: 0;
    }
    100% {
      opacity: 100;
    }
  }

  @keyframes topEaseHeightIn {
    0% {
      opacity: 10;
      height: u($single-spec-container-height);
    }
    100% {
      opacity: 100;
    }
  }

  .mp-shopping-product-spec-selector {
    &__wrapper {
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      overflow: hidden;
      background: rgba(0, 0, 0, .5);
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      z-index: 10;

      view {
        box-sizing: border-box;
      }
    }

    &__backdrop {
      width: 100%;
      flex-grow: 1;
    }

    &__container {
      flex-grow: 0;
      width: 100%;
      background: #fff;
      border-radius: u(20) u(20) 0 0;
      position: relative;

      &--animation {
        animation: easeHeightIn .3s linear;
      }

      &--single {
        height: u($single-spec-container-height);
      }

      &--multiple {
        animation: topEaseHeightIn .4s ease-out;
        height: u($multiple-spec-container-height);
      }
    }

    // 自定义tabbar 安全区域
    &__container.useTabBar {
      bottom: calc(#{u($customTabBarHeight)} - 10rpx + env(safe-area-inset-bottom));
    }

    &__header {
      padding: 0 u(156) 0 u(24);
      border-bottom: u(2) solid #f1f1f1;
      position: relative;
      height: $header-height;
      display: flex;
      align-items: center;
      overflow: hidden;

      &-left {
        width: u(168);
        height: u(184);
        overflow: hidden;
        flex-shrink: 0;
        margin-right: u(24);
      }

      &-image {
        width: 100%;
        height: 100%;
      }

      &-right {
        flex-grow: 1;
        height: 100%;
        padding: u(30);
      }

      &-name {
        font-size: u(26);
        color: #3a3f4a;
        height: u(57);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: u(12);
        line-height: 1.1em;
      }

      &-quantity {
        font-size: u(24);
        color: #9da7ba;
        margin-bottom: u(40);
        line-height: 1em;
      }

      &-tags {
        display: flex;
        margin-bottom: u(8);

        &-tag {
          margin-right: u(4);
          padding: u(2) u(4);
          border: u(2) solid mat-color($primary);
          border-radius: u(4);
          font-size: u(16);
          color: mat-color($primary);
          margin-top: u(8);

        }

      }

      &-price {
        color: mat-color($primary);
        font-size: u(28);
        line-height: 1em;

        &-origin {
          margin-right: u(8);

          color: #2034B5;
        }

        &-cost {
          font-size: u(20);
        }

      }

      &-close {
        position: absolute;
        top: u(32);
        right: u(24);
        width: u(32);
        height: u(32);
        font-size: u(36);
        color: #b0b0b0;
        display: flex;
        align-items: center;
        justify-content: center;

        &.PETKIT {
          font-size: u(36);
        }
      }
    }

    &__body {
      height: u(460);
      margin: u(20) 0;
      overflow-y: auto;
      overflow-x: hidden;

      &--single {
        height: u($single-spec-container-height - $header-height - $footer-height);
      }

      &--multiple {
        height: u($multiple-spec-container-height - $header-height - $footer-height);
      }

      &-spec {
        padding: 0 u(24);
        margin-bottom: u(24);
        overflow: hidden;

        &_name {
          font-size: u(26);
          color: #575d6a;
        }

        &_list {
          margin: 0;
          display: flex;
          flex-wrap: wrap;

          &-item {
            text-align: center;
            margin-right: u(16);
            margin-top: u(16);
            background: #f1f1f1;
            border-radius: u(8);
            padding: u(16) u(22);
            font-size: u(24);
            min-width: u(144);
            color: #575d6a;

            &--active {
              background-color: map-get($background, background);
              color: mat-color($primary);
            }

            &--disabled {
              color: #c4c9d2;
            }
          }
        }
      }

      &-number {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: u(2) solid #f1f1f1;
        padding: 0 u(24);

        &_title {
          font-size: u(26);
          color: #575d6a;
        }

        &_operator {
          display: flex;
          align-items: center;
          justify-content: center;

          .mp-shopping-product-border-box,
          &-minus,
          &-plus {
            box-sizing: content-box;
          }

          &-minus,
          &-plus {
            width: u(32);
            height: u(32);
            font-size: u(32);
            color: #f1f1f1;
            display: flex;
            align-items: center;
            justify-content: center;

            border: u(24) solid transparent;
            box-sizing: content-box;

            &.PETKIT {
              font-size: u(32);
            }

            &--active {
              color: mat-color($primary);
            }
          }

          &-number {
            text-align: center;
            color: #3B3B3B;
          }
        }
      }
    }

    &__footer {
      width: 100%;
      padding: 0 u(24) u(48);
      height: u($footer-height);
      position: relative;
      bottom: u(30);

      &-button {
        width: 100%;
        color: #fff;
        background-color: mat-color($primary);
        border-radius: u(48);
        font-size: u(32);
        border: none;
        outline: none;

        &--disabled {
          background: #e3e3e3;
        }
      }
    }
  }
}

@include iu-icon-iconfont-theme($lucky-theme);
@include mp-shopping-product-spec-selector($lucky-theme);
