/* eslint-disable no-empty-function */
import {connect} from "../../../../../libs/utils/weapp-redux";
import {bindActionCreators} from "redux";

Component({
  properties: {
    auto: {
      type: Boolean,
      value: false
    },
    detail: {
      type: Array,
      value: [],
    },
    showUse: {
      type: Boolean,
      value: false
    },
    backgroundColor: {
      type: String,
      value: ''
    },
    width: {
      type: String,
      value: '',
    },
    marginTop: {
      type: String,
      value: '',
    },
    // 由控制展开还是收缩使用说明，改为是否全局显示使用说明
    showUseDescription: {
      type: Boolean,
      value: false,
    },
    // 热区是否最大（洗护预约与商城时为true，热区最大）
    isTheHotZoneTheLargest: {
      type: Boolean,
      value: false,
    },
    currencySymbol:{
        type: String,
        value: '￥',
    }
  },
  data: {
    // 优惠券标签
    // couponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.rvhUHi6zeyKw02ac200be99e8eb21b04fa1a888b6821.svg',
    couponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.xNMThdoaMYcm67812fec148240ef41f4c68c92883464.png',
    // 积分券标签
    pointCouponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.rWk0qCeGKeaFdadb746834e513be03d4da090e93ab26.svg',
    // 代金券标签
    cashVoucherCouponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.8A4dyxDnZwxL3c248a25b4c6ccdf5c02bdcd58bc8e6e.svg',

    // 失效优惠券标签
    invalidCouponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.hg5Jll7PZD749b3e9f2a39e307bec69ab2799fca8fab.svg',
    // 失效积分券标签
    invalidPointCouponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.j74FgnxO7EyDb2a739834d8cebcc71a41470441cdf77.svg',
    // 失效代金券标签
    invalidCashVoucherCouponUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.0TbDK1jI78njbf78a74a78f6c353d0f0cf11db582a37.svg',

    // 优惠券印章
    exchangeUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.4klxx930ssS073df080f624c5c7e4359aa4a102ed71c.svg',
    // 代金券印章
    cashVoucherExchangeUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.42W1pLXNKYE1a41181e9f48fdccc04ce3c7c10e8aaf8.svg',
    // 失效券印章
    invalidExchangeUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.BMkSQfmu5z7hfc2560468da9a03d5ac79b7574689367.svg',
    // 积分券印章
    pointExchangeUrl: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.fQssXMqTPXRJ6e2bc863e960fa8ae88657a4b024b4ef.svg',
    // arrowUrl: arrowUrl,
    nodes: [{}]
  },
  ready() {
  },
  methods: {
    onUseTap({
      currentTarget: {
        dataset: {
          item,
          type,
        }
      }
    }) {
      if (!this.data.isTheHotZoneTheLargest) {
        if (type === 'main') {
          // 入口为main 并且热区非最大(券包)
          this.triggerEvent('watchMore', {
            id: item.userCouponId,
          });
        }
        return;
      }
      this.triggerEvent('useTap', {
        data: item,
      });
    },
    // 点击‘使用说明’
    onUseDescription({
      currentTarget: {
        dataset: {
          id
        }
      }
    }) {
      // 更新收缩或者展开使用说明的方式
      const detail = this.data.detail.map(item => {
        return {
          ...item,
          isShowUseDescription: id === item.userCouponId ? !item.isShowUseDescription : item.isShowUseDescription,
        }
      });
      this.setData({
        detail
      });
    },
    // 点击‘查看全部’
    onWatchMore({
      currentTarget: {
        dataset: {
          item: {
            userCouponId,
          }
        }
      }
    }) {
      this.triggerEvent('watchMore', {
        id: userCouponId,
      });
    },
    // 点击立即使用
    onNowUse() {
      // 目前此立即使用按钮只会存在我的券包中，后期如有需要可以通过triggerEvent触发到业务中去使用
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },
});


