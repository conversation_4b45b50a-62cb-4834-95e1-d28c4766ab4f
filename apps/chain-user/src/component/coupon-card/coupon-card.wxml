<view class="{{auto?'auto':''}} {{item._isExpired ? 'mp-my-coupon--disabled' : (item.onlyBooking === 1 ? 'mp-my-coupon--booking' : (item.consumptionStrategy !== 'POINT_LIMIT_ADD' ? 'mp-my-coupon--normal' : 'mp-my-coupon--point'))}}" style="{{marginTop ? 'margin-top: ' + marginTop : ''}}" wx:for="{{detail}}" wx:key="index">
  <view class="mp-my-coupon__wrapper-hidden">
    <view class="mp-my-coupon__wrapper" style="{{width ? 'width: ' + width : ''}}">
      <view class="mp-my-coupon__header" wx:if="{{!item._isExpired}}"  bindtap="onUseTap" data-item="{{item}}">
        <!--优惠券标签-->
        <image class="mp-my-coupon__header-images" wx:if="{{item.consumptionStrategy === 'DISCOUNT' || item.consumptionStrategy === 'LIMIT'}}" src="{{couponUrl}}"/>
        <!--代金券标签-->
        <image class="mp-my-coupon__header-images" wx:if="{{item.consumptionStrategy === 'CASH_VOUCHER'}}" src="{{cashVoucherCouponUrl}}"/>
        <!--积分券标签-->
        <image class="mp-my-coupon__header-images" wx:if="{{item.consumptionStrategy === 'POINT_LIMIT_ADD'}}" src="{{pointCouponUrl}}"/>
      </view>
      <view class="mp-my-coupon__header" wx:if="{{item._isExpired}}"  bindtap="onUseTap" data-item="{{item}}">
        <image class="mp-my-coupon__header-images"  wx:if="{{item.consumptionStrategy === 'DISCOUNT' || item.consumptionStrategy === 'LIMIT'}}"  src="{{invalidCouponUrl}}"/>
        <image class="mp-my-coupon__header-images" wx:if="{{item.consumptionStrategy === 'CASH_VOUCHER'}}" src="{{invalidCashVoucherCouponUrl}}"/>
        <image class="mp-my-coupon__header-images"  wx:if="{{item.consumptionStrategy === 'POINT_LIMIT_ADD'}}"  src="{{invalidPointCouponUrl}}"/>
      </view>
      <view class="mp-my-coupon__main" data-item="{{item}}">
        <view class="mp-my-coupon__main-show"  bindtap="onUseTap" data-item="{{item}}" data-type="main">
          <view class="mp-my-coupon__main-type">
            <view class="mp-my-coupon__header-amount" wx:if="{{item.consumptionStrategy === 'LIMIT' || item.consumptionStrategy === 'CASH_VOUCHER'}}">
              <!-- 满减 -->
              <text class="mp-my-coupon__header-symbol">{{currencySymbol}}</text>
              <text class="mp-my-coupon__header-text">{{item.limitReduce}}</text>
            </view>
            <view class="mp-my-coupon__header-amount" wx:if="{{item.consumptionStrategy === 'DISCOUNT'}}">
              <!-- 折扣 -->
              <text class="mp-my-coupon__header-text">{{item.discountRate/10}}</text>
              <text class="mp-my-coupon__header-symbol">折</text>
            </view>
            <view class="mp-my-coupon__header-amount" wx:if="{{item.consumptionStrategy === 'POINT_LIMIT_ADD'}}">
              <!-- 折扣 -->
              <text class="mp-my-coupon__header-text">{{item.limitReduce}}</text>
              <text class="mp-my-coupon__header-symbol">分</text>
            </view>
          </view>
          <view class="mp-my-coupon__main-subtitle">
            <text class="mp-my-coupon__header-type--orange" wx:if="{{item._type === 'store'}}">门店- </text>
            <text class="mp-my-coupon__header-type--orange" wx:if="{{item._type === 'mall'}}">商城- </text>
            <text class="mp-my-coupon__header-type--yellow" wx:if="{{item._type === 'normal'}}">通用- </text>
            <text class="mp-my-coupon__main-title-text">{{item.voucherName}}</text>
          </view>
        </view>
        <!-- 印章 -->
        <block wx:if="{{item.marketActivityType === 'EXCHANGE'}}">
          <view class="mp-my-coupon__main-seal" wx:if="{{(item.consumptionStrategy === 'DISCOUNT' || item.consumptionStrategy === 'LIMIT') && !item._isExpired}}">
            <image src="{{exchangeUrl}}"></image>
          </view>
          <view class="mp-my-coupon__main-seal" wx:if="{{item.consumptionStrategy === 'CASH_VOUCHER' && !item._isExpired}}">
            <image src="{{cashVoucherExchangeUrl}}"></image>
          </view>
          <view class="mp-my-coupon__main-seal" wx:if="{{item.consumptionStrategy === 'POINT_LIMIT_ADD' && !item._isExpired}}">
            <image src="{{pointExchangeUrl}}"></image>
          </view>
          <view class="mp-my-coupon__main-seal" wx:if="{{item._isExpired}}">
            <image src="{{invalidExchangeUrl}}"></image>
          </view>
        </block>

        <view class="mp-my-coupon__main-time {{!showUse ? 'align-center' : ''}}">
          <view class="mp-my-coupon__main-subtitle-content"  bindtap="onUseTap" data-item="{{item}}">{{item._effectiveDate}} - {{item._expiredDate}}</view>
          <view class="mp-my-coupon__main-subtitle-show" data-id="{{item.userCouponId}}" bindtap="onUseDescription" wx:if="{{showUseDescription}}">
            <view class="mp-my-coupon__main-subtitle-explain">使用说明</view>
            <view class="{{!showUseDescription ? 'mp-my-coupon__main-subtitle-explain-triangle' : 'mp-my-coupon__main-subtitle-explain-triangle--rotate'}}"></view>
          </view>
        </view>
      </view>
      <view class="mp-my-coupon__circle-top" style="background-color: {{backgroundColor}}">
      </view>
      <view class="mp-my-coupon__circle-bottom" style="background-color: {{backgroundColor}}">
      </view>
    </view>
    <form-check class="mp-my-coupon__wrapper-check" checked="{{item.selected || item.isSelected}}"  bindtap="onUseTap" data-item="{{item}}" wx:if="{{isTheHotZoneTheLargest}}"></form-check>
    <!--立即使用-->
    <view class="mp-my-coupon__wrapper-use" bindtap="onNowUse" wx:if="{{!item._isExpired && showUse}}">
      立即使用
    </view>
  </view>
  <view class="mp-my-coupon__extra-content {{item.instructions ? 'mp-my-coupon__extra-content__more' : ''}}" wx:if="{{item.isShowUseDescription}}">
    <view class="mp-my-coupon__extra-detail" wx:if="{{item.instructions + item.limitDesc}}">
      <rich-text nodes="{{(item.instructions || '') + item.limitDesc}}"></rich-text>
    </view>
    <view class="mp-my-coupon__extra-detail-show" wx:else>
      优惠券暂无说明~
    </view>
    <!--失效券不显示查看更多-->
    <view class="mp-my-coupon__extra-more" wx:if="{{!item._isExpired}}">
      <text class="mp-my-coupon__extra-more-detail" data-item="{{item}}" bindtap="onWatchMore">查看全部</text>
      <text class="PETKIT icon-right-arrow mp-my-coupon__extra-more-arrow"></text>
    </view>
    </view>
</view>
