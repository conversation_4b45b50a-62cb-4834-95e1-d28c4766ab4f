/**
 * 提供功能：
 * 1. tabbar 拒绝定位的时候展示该页面，隐藏原先的页面：
 *    - 还没有授权，弹出授权框，拒绝展示。
 *    - 已经拒绝授权，展示该页面，且弹出授权框，拒绝之后在该页面本次未打开其他页面不展示。重新打开该页面弹出框提醒授权。
 * 2. 点击重试打开弹出框，授权定位之后，存储 warehouseId，关闭页面，打开需要展示的页面。
 * 设计一个全局的对象，存储是否已经获取过授权，授权状态，以及是否有当前存储的门店 id。
 * 组件设计：
 * 输入：是否需要展示的情况，
 * 输出：定位获取到存储好了之后，刷新当前页面，隐藏组件展示。
 */
Component({
  methods: {
    onOpenLocation() {
      wx.showModal({
        title: "请求授权当前位置",
        content: "需要获取您的地理位置，请确认授权",
        success: (res) => {
          if (res.cancel) {
            this.triggerEvent('locationEvent', {status: 'cancel'})
          } else if (res.confirm) {
            wx.openSetting({
              success: (res) => {
                if (res.authSetting["scope.userLocation"] == true) {
                  this.triggerEvent('locationEvent', { status: 'accepted'});
                } else {
                  this.triggerEvent('locationEvent', {status: 'refused'});
                  wx.showToast({
                    title: "授权失败",
                    icon: "none",
                    duration: 1000,
                  });
                }
              },
              failure: (res) => {
                this.triggerEvent('locationEvent', {status: 'failure'});
                wx.showToast({
                  title: "授权失败，请关闭微信后重新进入",
                  icon: "none",
                  duration: 1000,
                });
              }
            });
          }
        },
      });
    }
  },
});
