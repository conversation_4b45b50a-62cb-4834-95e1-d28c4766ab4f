@import "config/theme";
@mixin wp-common-location-tip-theme($theme) {
  $primary: map-get($theme, primary);
  .wp-common-location-tip {
    &__container {
      display: flex;
      align-items: center;
      flex-direction: column;
      box-sizing: border-box;
      height: 100%;
      background-color: #ffffff;
      justify-content: center;
    }
    &__image {
      width: u(176);
      height: u(176);
    }
    &__text {
      font-size: u(28);
      color: #444444;
      margin-top: u(24);
      margin-bottom: u(8);
    }
    &__check {
      font-size: u(28);
      color: #444444;
      margin-bottom: u(16);
    }

    &__self {
      width: u(176);
      height: u(64);
      background-color: mat-color($primary);
      color: #ffffff;
      text-align: center;
      line-height: u(64);
      font-size: u(24);
      border-radius: u(8);
    }
  }
}

@include wp-common-location-tip-theme($lucky-theme);
