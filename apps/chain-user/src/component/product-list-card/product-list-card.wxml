<!--
  组合套餐 categoryId === -1
  单品    categoryId !== -1
 -->
<view class="wp-crown-shopping-product-list-card__wrapper" wx:if="{{!detail.add_type}}">
  <view class="wp-crown-shopping-product-list-card__image-container  skeleton-rect">
    <image src="{{detail.image}}?x-oss-process=image/resize,w_168/quality,Q_80/format,jpg" />
    <image class="wp-crown-shopping-product-list-card__image-container-sold-out-img"
      wx:if="{{!detail.isQuantityEnough}}"
      src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/edb6485990c340d7a13f13f1430e1d5e?nsukey=hA%2Fw%2F13oKumKoYjVkUPkETTVVmRyLjgkKv7ST7J5nUYCKDHVSqbNQo8fuPX7SQrupOF1UTW7r0rEiwXxOz3loIIxl0Hw6GPcRi0VAaf2ITeC%2FgYWgv6mrtCvqxCU%2FKp%2Fi7R5IliUS%2Bi7XoOFdY2hlQ0bZsamwtbc7HGhU0mb9Hrbu99CeKmlt8Du6rz2AcRB6a2cgGceJkfFij3ehxyscQ%3D%3D" />
  </view>
  <view class="wp-crown-shopping-product-list-card__main-content">
    <view class="wp-crown-shopping-product-list-card__main-content-title skeleton-rect">
      <!-- wx:if="{{detail._crossBorder}}" -->
      <image
        wx:if="{{detail.type !== 'package' && detail._crossBorder}}"
        class="wp-crown-shopping-product-list-card__main-content-title-oversea"
        src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsxGW3fdbr3gEQ8Fu26dud2I.Vf6MbWxnnBcg50b12d6c32d8a8322b012f977d86ad9f.png"
      >
      </image>
      {{detail.name}}
    </view>
    <view class="wp-crown-shopping-product-list-card__main-content-subtitle">
      {{detail.subTitle || ''}}
    </view>
    <view class="wp-crown-shopping-product-list-card__main-content-discount-info-container">
    <!-- wx:if="{{detail.vipPrice !== null && detail.vipPrice !== detail.price && detail.vipDiscountTag}}" -->
      <view class="wp-crown-shopping-product-list-card__main-content-discount-info-container-vip" wx:if="{{detail.type !== 'package' && detail.vipPrice !== null && detail.vipPrice !== detail.price && detail.vipDiscountTag}}">
        <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.T3mruORkeoYr1bcd625079465661f5480b617dd7d0f5.svg" alt=""/>
        <view>{{detail.vipDiscountTag}}折</view>
      </view>
      <block wx:for="{{detail.tags}}" wx:key="item">
      <!-- <block wx:for="{{detail.tags}}" wx:key="item"> -->
        <view class="wp-crown-shopping-product-list-card__main-content-discount-info-item">
          {{item}}
        </view>
      </block>
    </view>
    <view class="wp-crown-shopping-product-list-card__main-content-footer-container">
      <view class="wp-crown-shopping-product-list-card__main-content-footer-price-info skeleton-rect">
        <view class="wp-crown-shopping-product-list-card__main-content-footer-price-current">
          {{currencySymbol}}{{detail.price}}
        </view>
        <view class="wp-crown-shopping-product-list-card__main-content-footer-price-original">
          <view class="wp-crown-shopping-product-list-card__main-content-footer-price-original-price">
            <view
              class="wp-crown-shopping-product-list-card__main-content-footer-price-sku"
              wx:if="{{detail.type !== 'package' && detail.vipPrice === null && detail.marketPrice !== detail.price && detail.marketPrice !== null}}"
            >{{currencySymbol}}{{detail.marketPrice || 0}}</view>
            <view class="wp-crown-shopping-product-list-card__main-content-footer-price-package" wx:if="{{detail.type === 'package'}}">
              {{detail.discountInfo}}
            </view>
          </view>
          <view class="wp-crown-shopping-product-list-card__main-content-footer-price-original-button">
            <block wx:if="{{detail.isQuantityEnough && detail.type !== 'package'}}">
              <image
                class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius"
                wx:if="{{!registerName && (pageState === 1 || pageState === 3) }}"
                src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.kVKGgJ5tYEbnc4eae12aff26e89096d64cf576e15261.svg"
                catchtap="_onPreventTap">
              </image>
              <image class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius"
                src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.kVKGgJ5tYEbnc4eae12aff26e89096d64cf576e15261.svg"
                wx:else>
                <view class="wp-crown-shopping-product-list-card__main-content-footer-icon-tap"
                  catchtap="onAddToShoppingCartTap"
                  data-detail="{{detail}}"
                ></view>
              </image>
            </block>
            <view
              class="wp-crown-shopping-product-list-card__main-content-footer-icon-wrapper"
              wx:if="{{!detail.isQuantityEnough && detail.type !== 'package'}}"
              catchtap="onPreventTap">
              <image class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/images/f78b1a4090df42a480391dc64be66972"></image>
            </view>
            <block wx:if="{{detail.isQuantityEnough && detail.type === 'package'}}">
              <image
                class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius"
                wx:if="{{!registerName && (pageState === 1 || pageState === 3) }}"
                src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs4KIjfB3OGt_WQOIn85Sd2I.CRenGoj4kZGxa768146149fc0f3d3a6293439daff5a7.png"
                catchtap="_onPreventTap">
              </image>
              <image class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius"
                src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs4KIjfB3OGt_WQOIn85Sd2I.CRenGoj4kZGxa768146149fc0f3d3a6293439daff5a7.png"
                wx:else>
                <view class="wp-crown-shopping-product-list-card__main-content-footer-icon-tap"
                  catchtap="onAddToShoppingCartTap"
                  data-detail="{{detail}}"
                ></view>
              </image>
            </block>
            <view
              class="wp-crown-shopping-product-list-card__main-content-footer-icon-wrapper"
              wx:if="{{!detail.isQuantityEnough && detail.type === 'package'}}"
              catchtap="onPreventTap">
              <image class="wp-crown-shopping-product-list-card__main-content-footer-icon skeleton-radius" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/images/d3aac88c53fd40f78993df2c6c1a7c96"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
