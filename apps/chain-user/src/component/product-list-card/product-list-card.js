Component({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
    categoryId: {
      type: Number,
      value: '',
    },
      currencySymbol:{
          type:String,
          value:'￥'
      }
  },

  methods: {
    // 自定义方法
    onAddToShoppingCartTap(ev) {
      const detail = ev.currentTarget.dataset.detail
      this.triggerEvent('addTap', {
        data: detail,
        touches: ev.touches,
      });
    },
    onPreventTap() {
      this.triggerEvent('hint');
    }
  }
});
