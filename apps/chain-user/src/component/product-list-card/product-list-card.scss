@import 'config/theme';

@mixin wp-crown-shopping-product-list-card-theme($theme) {
  @include wp-crown-shopping-product-list-card-basic-theme($theme);
  @include wp-crown-shopping-product-list-card-image-theme($theme);
  @include wp-crown-shopping-product-list-card-main-content-theme($theme);
}

@mixin wp-crown-shopping-product-list-card-basic-theme($theme) {
  .wp-crown-shopping-product-list-card {
    &__wrapper {
      display: flex;
      flex-direction: column;

      width: 100%;
      // height: u(356);
      border-radius: u(8);
      box-shadow: 0 2px 2px 0 #ebebeb;
    }
  }
}

@mixin wp-crown-shopping-product-list-card-image-theme($theme) {
  .wp-crown-shopping-product-list-card {
    &__image {
      &-container {
        display: flex;
        align-items: center;
        justify-content: center;

        position: relative;

        background:rgba(251,251,253,1);
        border-radius:u(8) u(8) u(0) u(0);

        image {
          width: u(168);
          height: u(184);
          border-radius: u(8);
        }

        &-sold-out-img {
          position: absolute;
          top: 0;
          left: 50%;

          transform: translateX(-50%);
        }
      }
    }
  }
}

@mixin wp-crown-shopping-product-list-card-main-content-theme($theme) {
  .wp-crown-shopping-product-list-card {
    &__main-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;

      padding: u(8) u(16) u(0) u(16);

      &-title {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;

        line-height: u(28);

        font-size: u(24);
        color: #3a3f4a;

        &-oversea {
          line-height: u(32);
          height: u(24);
          width: u(72);
          margin-right: u(8);
          position: relative;
          top: u(2);
        }
      }

      &-subtitle {
        margin-top: u(8);
        height: u(40);
        font-size: u(24);
        color: #B0B0B0;
        line-height: u(40);

        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      &-discount-info {

        &-container {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          margin-top: u(8);

          &-vip {

            // height: u(24);
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            margin-right: u(4);
  
            & > image {
              display:block;
              margin:0px;
              width: u(64);
              height: u(24);
            }
  
            &> view {
              background:linear-gradient(316deg,rgba(245,222,194,1) 0%,rgba(245,213,173,1) 100%,rgba(239,214,183,1) 100%);
              border-radius: u(0) u(4) u(4) u(0);
              width: u(48);
              height: u(24);
              font-size:8px;
              font-weight:500;
              color:rgba(71,55,44,1);
              display: flex;
              flex-direction: row;
              justify-content: center;
              align-items: center;
            }
            
          }
        }

        &-item {
          height: u(20);
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-right: u(4);
          margin-bottom: u(8);
          padding: u(0) u(4);
          border-radius: u(18);
          opacity:0.9;
          border:u(2) solid rgba(23,39,145,1);

          font-size: u(16);
          font-weight:500;
          color:rgba(23,39,145,1);
        }
      }

       &-footer {
        &-container {
          display: flex;
          justify-content: space-between;
        }

        &-price {
          &-info {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-top: u(8);
          }

          &-current {
            font-size: u(24);
            margin-right: u(12);
            font-family:PingFangSC-Regular,PingFang SC;
            font-weight:400;
            color:rgba(23,39,145,1);
            line-height: u(44);
          }

          &-package {
            font-size: u(20);
            font-family:PingFangSC-Regular,PingFang SC;
            font-weight:400;
            color:rgba(255,86,123,1);
            line-height: u(20);
          }

          &-sku {
            text-decoration: line-through;
          }

          &-original {
            width: 100%;
            transform: translateY(u(-4));
            font-size: u(20);
            font-size: u(20);
            font-family:PingFangSC-Regular,PingFang SC;
            font-weight:400;
            color:rgba(176,176,176,1);
            line-height: u(40);
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            &-price {

            }

            &-button {
              width: u(44);
              height: u(44);
              border-radius: 50%;
            }
          }
        }

        &-icon {
          height: u(44);
          width: u(44);

          position: relative;
          overflow: visible;

          &-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;

            width: u(44);
            height: u(44);
          }

          &-tap {
            position: absolute;
            top: u(-20);
            left: u(-20);
            bottom: u(-20);
            right: u(-20);

            z-index: 1;
          }
        }
      }
    }
  }
}

@include wp-crown-shopping-product-list-card-theme($app-theme);
