Component({
  properties: {
    // 当前数据对象
    item: {
      type: Object,
      value: {}
    },
    // 是否显示单选按钮
    showCheck: {
      type: Boolean,
      value: false
    },
    // 是否显示默认border
    border: {
      type: Boolean,
      value: false
    }
  },
  data: {
    // 组件内部的数据
  },
  ready() {
  },
  methods: {
    onDeductionTap() {
      this.triggerEvent('onItemTap', {
        data: this.data.item,
      });
    },
    // check radio 被点击
    onCheckTap() {
      this.triggerEvent('onCheckTap', {
        data: this.data.item,
      });
    }
  }
});

