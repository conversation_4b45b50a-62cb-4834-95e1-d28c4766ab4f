@import 'config/theme';

@mixin mp-deduction-card($primary, $textMainColor, $textSecondaryColor, $isDisabled, $bordered) {
  .mp-deduction-card {
    &__wrapper {
      width: 100%;
      margin: 0 0 u(24);
      background-color: #fff;
      border-radius: u(8);
      box-sizing: border-box;
    }
    @if $bordered {
      &__wrapper {
        border: u(2) solid #EBEBEB;
        margin: 0 0 u(16);
      }
    }


    &__header {
      &-title {
        font-size: u(36);
        color: $textMainColor;
        text-align: center;
        margin: 0 auto;
        padding-top: u(26);
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &-desc {
        color: $textMainColor;
        font-size: u(24);
        text-align: center;
        padding: u(12) 0 u(8) 0;
        font-weight: 500;
        font-family: PingFangSC-Medium, PingFangSC;
      }

      &-order-button {
        background-color: $primary;
        color: #fff;
        width: u(360);
        height: u(96);
        line-height: u(96);
        margin-bottom: u(26);

        &::after {
          border: none;
        }
      }

      &-time {
        width: 100%;
        color: $textSecondaryColor;
        font-size: u(20);
        padding-bottom: u(24);
        font-weight: 400;
        text-align: center;
        font-family: PingFangSC-Regular, PingFangSC;
      }

      &-icon {
        color: #fff;
        padding: 0 u(4);
        border-radius: u(4);
        margin-left: u(12);
        font-size: u(20);
        display: inline-block;
        width: u(56);
        height: u(34);
        line-height: u(34);
        vertical-align: middle;

        &--warn {
          @if $isDisabled {
            background-color: #b0b0b0;
          } @else {
            background-color: #fb9e0f;
          }
        }

        &--primary {
          @if $isDisabled {
            background-color: #b0b0b0;
          } @else {
            background-color: #38c5cf;
          }
        }
      }
    }

    &__main {
      color: $textMainColor;
      position: relative;

      &-tips {
        font-size: u(20);
        padding-bottom: u(20);
      }

      &-content-item {
        height: u(70);
        width: 100%;
        line-height: u(70);
        font-size: u(28);
        display: flex;
        justify-content: space-between;

        &-center {
          width: 100%;
          text-align: center;
          padding-bottom: u(10);
          font-weight: 400;
        }
      }
    }

    &__divider {
      width: 100%;
      height: 1px;
      border-top: 1px dotted #ebebeb;
      margin-top: u(14);

      &-container {
        width: 100%;
        position: relative;
      }

      &-circle {

        &-left {
          width: u(28);
          height: u(28);
          background-color: #f6f6f6;
          border-radius: 50%;
          position: absolute;
          top: -14rpx;
          left: -14rpx;
        }

        &-right {
          width: u(28);
          height: u(28);
          background-color: #f6f6f6;
          border-radius: 50%;
          position: absolute;
          top: -14rpx;
          right: -14rpx;
        }
        @if $bordered {
          &-left {
            border: u(2) solid #EBEBEB;
            background-color: #fff;
          }
          &-right {
            border: u(2) solid #EBEBEB;
            background-color: #fff;
          }
        }
      }
    }

    &__watermark {
      position: absolute;
      height: u(96);
      width: u(96);
      right: u(40);
      top: u(16);
      transform: rotate(10deg);

      &__background {
        background: transparent;
        height: u(96);
        width: u(96);
        left: 0;
        top: 0;
      }

      image {
        height: 100%;
        width: 100%;
      }

      &__radio {
        position: absolute;
        width: u(28);
        height: u(28);
        left: u(32);
        top: u(32);
        border-radius: 50%;
        border: u(2) solid #B0B0B0;
        transform: rotate(-10deg);
        background-color: #fff;

        image {
          display: none;
        }
      }

      &__radio.active {
        border: none;

        image {
          display: block;
        }
      }
    }
  }
}

@mixin mp-deduction-card-theme($primary) {
  .mp-deduction-card {
    position: relative;

    &--disabled {
      @include mp-deduction-card(#e3e3e3, #b0b0b0, #b0b0b0, true, false);
    }

    &--normal {
      @include mp-deduction-card(#2034B5, #2f2f2f, #9397a2, false, false);
    }

    &--disabled.border {
      @include mp-deduction-card(#e3e3e3, #b0b0b0, #b0b0b0, true, true);
    }

    &--normal.border {
      @include mp-deduction-card(#2034B5, #2f2f2f, #9397a2, false, true);
    }
  }
}

@include mp-deduction-card-theme($app-theme)
