<view class="mp-deduction-card {{border?'border':''}} {{item._isExpired ? 'mp-deduction-card--disabled' :  'mp-deduction-card--normal' }}"
      bindtap="onDeductionTap">
  <!-- 商品预购券 -->
  <view class="mp-deduction-card__wrapper">
    <view class="mp-deduction-card__header">
      <view class="mp-deduction-card__header-title">
        {{item.voucherName || item.skuName || item.strategyName}}
        <text wx:if="{{item.userCouponType ==='SERVICE' || item.type ==='SERVICE'}}" class="mp-deduction-card__header-icon mp-deduction-card__header-icon--warn">服务</text>
        <text wx:if="{{item.userCouponType ==='PRODUCT' || item.type ==='PRODUCT'}}" class="mp-deduction-card__header-icon mp-deduction-card__header-icon--primary">商品</text>
      </view>
      <view class="mp-deduction-card__header-desc">{{item.title?item.title:''}}</view>
      <view class="mp-deduction-card__header-time">
        {{item._createDate||item._effectiveDate}}-{{item.deductionExpirationDate || item._expirationDate}} 到期
      </view>
      <view class="mp-deduction-card__header-time">
        {{item.limitDesc}}
      </view>
    </view>
    <view class="mp-deduction-card__divider-container">
      <view class="mp-deduction-card__divider"></view>
      <view class="mp-deduction-card__divider-circle-left"></view>
      <view class="mp-deduction-card__divider-circle-right"></view>
    </view>
    <view class="mp-deduction-card__main">
      <!-- 商品内容 -->
      <view class="mp-deduction-card__main-content-item">
        <view class="mp-deduction-card__main-content-item-center">
          剩余次数： 1 / 1
        </view>
      </view>
    </view>
  </view>

  <!--兑换水印-->
  <view class="mp-deduction-card__watermark">
    <image wx:if="{{item.marketActivityType==='EXCHANGE'}}"
           src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.fDG3biJWEP1sdcf714824d5db72feade1b508b71da1e.svg"/>

    <view class="mp-deduction-card__watermark__background"
          wx:if="{{showCheck}}"
          catchtap="onCheckTap">
      <view class="mp-deduction-card__watermark__radio {{item.active?'active':''}}">
        <image src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.j7QqEyQhcJrm5eaa40360a4687ccb7fb831cdb7b54f4.png"/>
      </view>
    </view>
  </view>
</view>
