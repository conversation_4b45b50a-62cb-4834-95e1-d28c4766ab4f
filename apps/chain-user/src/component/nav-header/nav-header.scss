@import 'config/theme';

@mixin ui-nav-header-theme($theme) {
  $primary: map-get($theme, primary);

  .nav-header-container {
    position: fixed;
    width: 100%;
    z-index: 9999;
    top: -100%;
    left: 0;
    right: 0;
    background-color: #fff;
    transition: top .2s ease-in-out;
    box-shadow: 0 u(4) u(8) 0 rgba(0, 0, 0, 0.1);

    &--name {
      font-size: u(36);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #3b3b3b;
      text-align: center;
    }

    &--icon {
      width: u(222);
      height: u(48);
      margin-left: u(40);
      margin-top: u(11);
    }

    &.show {
      top: 0;
    }
  }
}

@include ui-nav-header-theme($lucky-theme);

