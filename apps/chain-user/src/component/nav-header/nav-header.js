import {store} from '@petkit/redux';

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    titleBarHeight: {
      type: Number,
      value: 44
    },
    headerName: {
      type: String,
      value: '小佩宠物'
    },
    page: {
      type: String,
      value: ''
    }
  },
  data: {
    systemInfo: {}, // 设备信息
  },
  ready() {
    // 系统信息
    const systemInfo = store.getState().global.env.systemInfo;
    this.setData({
      systemInfo
    });
  },
});
