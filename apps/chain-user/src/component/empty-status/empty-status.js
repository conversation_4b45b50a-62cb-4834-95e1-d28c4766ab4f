Component({
  properties: {
    type: {
      type: String,
      value: 'search'
    }
  },
  data: {
    options: {
      car: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.70q93WgHeLsW986323d309ad7f25d54f5a3fae8549d2.png',
        title: '',
        subTitle: '购物车还是空的',
        btnText: '去逛逛'
      },
      search: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.veSD5A6vbZesd2741b8b88a807bc7c15afc2e79bc9c1.png',
        title: '',
        subTitle: '查询无结果',
        btnText: ''
      },
      coupon: {
        ghost: false,
        title: '',
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.veSD5A6vbZesd2741b8b88a807bc7c15afc2e79bc9c1.png',
        subTitle: '暂无可领的优惠券',
        btnText: ''
      },
      subCard: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '暂无记录',
        subTitle: '您还没有次卡消费记录',
        btnText: ''
      },
      myOrder: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '',
        subTitle: '您还没有相关的订单',
        btnText: '去逛逛'
      },
      couponList: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '',
        subTitle: '暂无优惠券',
        btnText: '去领券中心逛逛'
      },
      pointCouponList: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '',
        subTitle: '暂无积分券',
        btnText: '去领券中心逛逛'
      },
      deductionCouponList: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '',
        subTitle: '暂无抵扣券',
        btnText: '去领券中心逛逛'
      },
      invalidCouponList: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '',
        subTitle: '暂无失效券'
      },
      foster: {
        ghost: false,
        // 空页面图片
        url: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.6qgQW9cM4zlD9416ad546b6a50b4e7cbdb29aeeebb55.png',
        title: '暂无寄养订单',
        subTitle: '您近期没有寄养过宠物哟～',
        btnText: ''
      },
    },
    option: {}
  },
  ready() {
    this.setOption(this.data.type);
  },
  methods: {
    setOption(type) {
      const option = this.data.options[type];
      this.setData({
        option
      });
    },
    onBtnTap() {
      this.triggerEvent('onSelect', {
        data: this.data,
      });
    },
  }
});

