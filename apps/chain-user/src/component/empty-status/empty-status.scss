@import 'config/theme';

@mixin empty-status($theme) {
  $primary: map-get($theme, primary);

  .empty-status__container {
    margin: u(252) auto 0;
    position: relative;
    width: u(400);
    display: flex;
    flex-direction: column;
    align-items: center;

    &--image {
      width: 100%;
      height: u(300);

      &__icon {
        display: block;
        height: 100%;
        width: 100%;
      }
    }

    &--title {
      font-size: u(28);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #444444;
      text-align: center;
      margin-top: u(4);
    }

    &--subtitle {
      font-size: u(28);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8D8D8D;
      text-align: center;

      &.active {
        margin-top: u(44);
      }
    }

    &--button {
      padding: u(12) u(34);
      border-radius: u(40);
      text-align: center;
      font-size: u(28);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      margin-top: u(98);
      box-sizing: border-box;

      &.ghost-btn {
        background-color: #fff;
        border: u(2) solid mat-color($primary);
        color: mat-color($primary);
      }

      &.normal-btn {
        background: linear-gradient(90deg, #FE7B70 0%, #EB4538 100%);
        color: #fff;
        min-width: u(248);
      }
    }
  }
}

@include empty-status($lucky-theme);

