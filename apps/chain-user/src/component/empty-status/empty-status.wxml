<view class="empty-status__container">
  <view class="empty-status__container--image">
    <image src="{{option.url}}" class="empty-status__container--image__icon"></image>
  </view>
  <view wx:if="{{option.title}}" class="empty-status__container--title">{{option.title}}</view>
  <view class="empty-status__container--subtitle {{!option.title ? 'active' : ''}}">{{option.subTitle}}</view>
  <view wx:if="{{option.btnText}}"
        bindtap="onBtnTap"
        class="empty-status__container--button {{option.ghost ? 'ghost-btn' : 'normal-btn'}}">
    {{option.btnText}}
  </view>
</view>
