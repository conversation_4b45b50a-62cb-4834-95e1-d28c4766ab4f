@import 'config/theme';

@mixin wp-crown-shopping-product-list-select-theme($theme) {
  @include wp-crown-shopping-product-list-select-basic-theme($theme);
}

@mixin wp-crown-shopping-product-list-select-basic-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $background: map-get($theme, background);
  .wp-crown-shopping-product-list-select {
    &__wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &__scroll {
      max-height: u(276);
    }
    &__selectors {
      display: flex;
      flex-wrap: wrap;

      &-item {
        display: flex;
        align-items: center;

        min-width: u(250);
        height: u(50);
        margin: u(10) u(12) u(10) u(24);

        font-size: u(24);
        color: #3a3f4a;

        &--selected {
          justify-content: center;

          position: relative;

          border-radius: u(8);

          color: mat-color($accent);
          background-color: mat-color($background, background);
        }

        &--inactive {
          color: #e3e3e3;
        }
      }

      &-icon {
        position: absolute;
        bottom: 0;
        right: 0;

        display: flex;
        justify-content: flex-end;
        align-items: flex-end;

        width: u(28);
        height: u(28);
        border-radius: u(8);
        padding-right: u(4);
        clip-path: polygon(0 100%, 100% 0, 100% 100%);

        font-size: u(16);

        color: #ffffff;
        background-color: mat-color($accent);

        &--inactive {
          display: none;
        }
      }
    }

    &__hint {
      padding: u(30);
      color: #9da7ba;
    }
  }
}

@include wp-crown-shopping-product-list-select-theme($lucky-theme);
