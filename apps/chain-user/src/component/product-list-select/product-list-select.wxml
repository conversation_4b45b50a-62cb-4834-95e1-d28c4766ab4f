<scroll-view scroll-y="true"
             class="wp-crown-shopping-product-list-select__scroll">
  <view class="{{list.length === 0 ? 'wp-crown-shopping-product-list-select__wrapper' : ''}}">
    <view class="wp-crown-shopping-product-list-select__selectors">
      <block wx:for="{{list}}" wx:key="item">
        <view class="wp-crown-shopping-product-list-select__selectors-item {{!item.available || !item.selectable ? 'wp-crown-shopping-product-list-select__selectors-item--inactive' : ''}} {{item.selected ? 'wp-crown-shopping-product-list-select__selectors-item--selected' : ''}}"
              data-item="{{item}}"
              bindtap="onSelectTap">
          {{item.name}}
          <view class="{{item.selected ? 'wp-crown-shopping-product-list-select__selectors-icon' : 'wp-crown-shopping-product-list-select__selectors-icon--inactive'}}">×</view>
        </view>
      </block>
      <view class="wp-crown-shopping-product-list-select__hint" wx:if="{{list.length === 0}}">暂无数据</view>
    </view>
  </view>
</scroll-view>
