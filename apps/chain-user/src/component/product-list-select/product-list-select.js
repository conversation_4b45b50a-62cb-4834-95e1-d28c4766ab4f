Component({
  properties: {
    list: {
      type: Array,
      value: [],
    }
  },
  data: {
    // 组件内部的数据
  },
  ready() {
  },

  methods: {
    // 自定义方法

    onCloseTap() {
      this.triggerEvent('close');
    },

    onSelectTap(ev) {
      const item = ev.currentTarget.dataset.item;

      if (item.available && item.selectable) {
        this.triggerEvent('select', item);

        if (!item.selected) {
          this.onCloseTap();
        }
      }
    }
  }
});
