<view class="component-progress">
  <view class="component-progress-message">
    <view  class="component-progress-message-popup component-progress-message-popup-{{progressData.moveStage.style}}" style="left: {{progressData.moveStage.number}}%">
      <view class="component-progress-message-popup-block component-progress-message-popup-block-{{progressData.moveStage.messageStyle}}">
        {{ progressData.alradyInvitionNumber > 0 && progressData.alradyInvitionNumber < progressData.oldUserInvitedPeopleNumber ? '已邀请' + progressData.alradyInvitionNumber + '人' : '活动奖励' }}
      </view>
      <view class="component-progress-message-popup-arrow component-progress-message-popup-arrow-{{progressData.moveStage.messageStyle}}"></view>
    </view>
    <!-- <view  class="component-progress-message-text component-progress-message-text-{{moveStage.style}}" style="left: {{moveStage.number}}%">{{ invitionNumber > 0 && invitionNumber < 10 ? '已经邀请' + invitionNumber + '人' : '活动奖励' }}</view> -->
  </view>
  <view class="component-progress-content">
    <view class="component-progress-content-stage">
      <view class=" component-progress-content-stage-{{progressData.moveStage.style}} component-progress-content-stage-style" style="left: {{progressData.moveStage.number}}%">
        <view></view>
        <!-- <view class="component-progress-content-stage-message">已经邀请10人</view> -->
      </view>
      <view  class="component-progress-content-stage-{{progressData.moveStage.lastStyle}} component-progress-content-stage-style" style="left: 100%">
        <view></view>
      </view>
    </view>
    <view class="component-progress-content-radius">
      <view class="component-progress-content-radius-stripe" style="transform: translateX(-{{progressData.translateX}}%)"></view>
      <view class="component-progress-content-radius-line"></view>
    </view>
  </view>
  <view class="component-progress-count">
    <!-- <block wx:for="{{peopleCount}}" wx:key="index"> -->
      <view class="component-progress-count-style component-progress-count-{{item.style}}" style="left:{{0 * 10}}%">{{0}}人</view>
      <view class="component-progress-count-style component-progress-count-{{item.style}}" style="left:{{10 * 10}}%">{{progressData.oldUserInvitedPeopleNumber}}人</view>
    <!-- </block> -->
  </view>
  <view class="PETKIT icon-right-arrow"></view>
</view>
  