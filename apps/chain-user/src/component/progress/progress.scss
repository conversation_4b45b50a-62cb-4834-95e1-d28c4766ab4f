@import 'config/theme';


@mixin component-progress($theme) {
  // $padding: u(56);
  $padding: u(120); // 增加30直接改这个值来改变宽度
  $progress_radius: u(12);
  $translateX: -100%;
  $progress_height: u(24);
  $leave_color: rgba(230,241,255,1);
  $hover_color: linear-gradient(135deg,rgba(255,187,92,1) 0%,rgba(251,158,15,1) 100%);
  .component-progress {
    background: hsla(0, 0%, 100%, 1);
    height: u(160);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    &-content{
      width: calc(100% - #{$padding});
      height: u(40);
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;

      &-stage {
        width: 100%;
        height: 100%;
        position: absolute;
        top: u(0);
        display: flex;
        flex-direction: row;

        // &-message {
        //   position: absolute;
        //   width: u(60);
        // }

        &-active {
          transition:  1s linear;
          background: $hover_color;
          z-index: 12;
          & > view:nth-child(1) {
            width: u(16);
            height: u(16);
            background: $hover_color;
            border-radius: 50%;
            border: u(4) solid #fff;
          }
        }

        &-leave {
          transition:  1s linear;
          background: $hover_color;
          z-index: 12;
          & > view:nth-child(1) {
            width: u(16);
            height: u(16);
            background: $hover_color;
            border-radius: 50%;
            border: u(4) solid #fff;
          }
        }
        // 进度条圆圈两端样式
        &-last {
          background: $leave_color;
          z-index: 11;
          & > view:nth-child(1) {
            width: u(16);
            height: u(16);
            background: $leave_color;
            border-radius: 50%;
            border: u(4) solid #fff;
          }
        }

        &-last-none {
          display: none
        }

        // 进度条圆圈两端样式

        &-none {
          z-index: 11;
          background: $leave_color;
          & > view:nth-child(1) {
            width: u(16);
            height: u(16);
            background: $leave_color;
            border-radius: 50%;
            border: u(4) solid #fff;
          }
        }

        &-style {
          position: absolute;
          width: u(40);
          height: u(40);
          border-radius: 50%;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          transform: translateX(u(-20))
        }
      }
      
      &-radius {
        width: 100%;
        height:  $progress_height;
        border-radius: $progress_radius;
        overflow: hidden;
        position: relative;
        &-stripe {
          position: absolute;
          width: 100%;
          height:  $progress_height;
          background: repeating-linear-gradient(45deg, 
                  #fb3, #fb3 u(4),
                  hsla(0, 94%, 68%, 1) 0, hsla(0, 94%, 68%, 1) u(8));
    
          border-radius: $progress_radius;
          z-index: 10;
          transform: translateX($translateX);
          transition: 1s linear;
    
        } 
    
        &-line {
          position: absolute;
          width: 100%;
          height:  $progress_height;
          background:rgba(230,241,255,1);
          border-radius: $progress_radius;
        }
      }
    }

    &-message {
      width: calc(100% - #{$padding});
      height: u(64);
      margin-bottom: u(8);
      display: flex;
      flex-direction: row;
      position: relative;

      &-popup {
        position: absolute;
        // width: u(136);
        width: u(150);
        height: 100%;
        transform: translateX(u(-50));
        transition: 1s linear;

        &-block {
          // width: 100%;
          // padding: u(0) u(8);
          padding-right: u(4);
          padding-left: u(4);
          height: u(54);
          border-radius: u(10);
          // transform: translateX(u(-22));
          transform: translateX(u(-10));
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          position: absolute;
          z-index: 1;
          font-size: u(24);
          font-family:PingFangSC-Medium,PingFangSC;
          font-weight:500;

          &-leave {
            background:rgba(255,215,51,1);
            color:rgba(47,47,47,1);
          }
  
          &-active {
            background:rgba(255,215,51,1);
            color:rgba(47,47,47,1);
          }
  
          &-none {
            // background:#fff;
            display: none;
          }

          &-gray {
            background:rgba(216,235,255,1);
            color:rgba(255,255,255,1);
          }
        }

        &-arrow {
          height: u(30);
          width: u(30);
          position: absolute;
          bottom: u(8);
          left: 50%;
          // transform: translateX(u(-14)) rotate(45deg);
          // margin-top: u(-28);
          // margin-top: u(-28);
          transform: translateX(u(-39)) rotate(45deg);

          &-leave {
            // background:rgba(230,241,255,1);
            background:rgba(255,215,51,1);
          }
  
          &-active {
            background:rgba(255,215,51,1);
          }
  
          &-none {
            background:#fff;
          }

          &-gray {
            background:rgba(216,235,255,1);
            color:rgba(255,255,255,1);
          }
        }
      }

      &-text {
        position: absolute;
        width: u(136);
        height: u(54);
        transform: translateX(u(-72));
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        // line-height: u(24);
        transition: 1s linear;

        font-size: u(24);
        font-family:PingFangSC-Medium,PingFangSC;
        // font-weight:500;

        &-leave {
          color:rgba(255,255,255,1);
        }

        &-active {
          color:rgba(47,47,47,1);
        }

        &-none {
          // color:rgba(255,255,255,1);
          display: none;
        }
      }
    }
    
    &-count {
      width: calc(100% - #{$padding});
      position: relative;

      
      &-style {
        width: u(60);
        position: absolute;
        transform: translateX(u(-38));
        font-size: u(24);
        font-family:PingFangSC-Regular,PingFangSC;
        font-weight:400;
        color:rgba(124,166,210,1);
      }

      & > view:nth-child(1) {
        transform: translateX(u(0));
      }
    }
  }
}

@include component-progress($lucky-theme);