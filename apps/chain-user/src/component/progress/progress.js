Component({
  properties: {
    progressData: {
      type: Object
    }
    // oldUserInvitedPeopleNumber: { // 属性名
    //   type: Number,
    //   value: 10090
    // },
    // alradyInvitionNumber: {
    //   type: Number,
    //   value: 0
    // },
    // progressStatus: {
    //   type: String,
    //   value: 0
    // }
  },
  data: {
    oldUserInvitedPeopleNumber: 0,
    progressStatus: '',
    alradyInvitionNumber: 0,
    // 组件内部的数据
    translateX: 100 , // 100 - (已经邀请人数 / 邀请的总人数) * 100
    moveStage: {
      number: 0, // 0 (已经邀请人数 / 邀请的总人数) * 100
      style: 'leave', // leave 
      lastStyle: 'last',
      messageStyle: 'leave'
    }
  },
  // ready() {
  //   let that = this;
  //   let properties = that.properties;
  //   that.setData({
  //     oldUserInvitedPeopleNumber: properties.oldUserInvitedPeopleNumber,
  //     progressStatus: properties.progressStatus,
  //     alradyInvitionNumber: properties.alradyInvitionNumber
  //   }, () => {
  //     console.log('ready', this.data)
  //     console.log(this.data.alradyInvitionNumber, this.data.oldUserInvitedPeopleNumber)
  //     this.setData({
  //       translateX: 100 - (this.data.alradyInvitionNumber / this.data.oldUserInvitedPeopleNumber) * 100 ,
  //     })
  //     if (this.data.alradyInvitionNumber === 0) {
  //     console.log('this.data.alradyInvitionNumber === 0')
  //     this.setData({
  //       moveStage: {
  //         style: 'none', // leave 0 none 1-9 active 10 leave
  //         messageStyle: 'none',
  //         lastStyle: 'last-none',
  //         number: (this.data.alradyInvitionNumber / this.data.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //       }
  //     }) 
  //     }

  //   if (0 < this.data.alradyInvitionNumber && this.data.alradyInvitionNumber < this.data.oldUserInvitedPeopleNumber) {
  //     console.log('0 < this.data.alradyInvitionNumber && this.data.alradyInvitionNumber < this.data.oldUserInvitedPeopleNumber')
  //     this.setData({
  //       moveStage: {
  //         style: 'active', // leave 0 none 1-9 active 10 leave
  //         messageStyle: 'active',
  //         lastStyle: 'last',
  //         number: (this.data.alradyInvitionNumber / this.data.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //       }
  //     }) 
  //   }

  //   if (this.data.alradyInvitionNumber === this.data.oldUserInvitedPeopleNumber) {
  //     console.log('alradyInvitionNumber', this.data.alradyInvitionNumber)
  //     console.log('oldUserInvitedPeopleNumber', this.data.oldUserInvitedPeopleNumber);
  //     console.log('this.data.alradyInvitionNumber === this.data.oldUserInvitedPeopleNumber', this.data.alradyInvitionNumber === this.data.oldUserInvitedPeopleNumber);
  //     this.setData({
  //       moveStage: {
  //         style: 'leave', // leave 0 none 1-9 active 10 leave
  //         messageStyle: 'leave',
  //         lastStyle: 'last',
  //         number: (this.data.alradyInvitionNumber / this.data.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //       }
  //     }) 
  //   }

  //   // 判断是否已经领取奖励
  //   // 重新发起 代表已经领取了奖励
  //   if (this.data.progressStatus === 'RECEIVED_AWARD') {
  //     console.log("this.data.progressStatus === 'RECEIVED_AWARD'");
  //     this.setData({
  //       moveStage: {
  //         style: 'leave', // leave 0 none 1-9 active 10 leave
  //         messageStyle: 'gray',
  //         lastStyle: 'last',
  //         number: (this.data.alradyInvitionNumber / this.data.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //       }
  //     }) 
  //   }
  //   })
    
  //   // this.setData({
  //   //   translateX: 100 - (this.properties.alradyInvitionNumber / this.properties.oldUserInvitedPeopleNumber) * 100 ,
  //   // })
  // },
  // attached() {
  //   // console.log('attached')
  //   // console.log('11', this.data.oldUserInvitedPeopleNumber)
  //   // console.log('oldUserInvitedPeopleNumber', this.data);
  //   // console.log('oldUserInvitedPeopleNumber', this.data.oldUserInvitedPeopleNumber);

    

  //   // if (this.properties.alradyInvitionNumber === 0) {
  //   //   console.log('this.properties.alradyInvitionNumber === 0')
  //   //   this.setData({
  //   //     moveStage: {
  //   //       style: 'none', // leave 0 none 1-9 active 10 leave
  //   //       messageStyle: 'none',
  //   //       lastStyle: 'last-none',
  //   //       number: (this.properties.alradyInvitionNumber / this.properties.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //   //     }
  //   //   }) 
  //   // }

  //   // if (0 < this.properties.alradyInvitionNumber && this.properties.alradyInvitionNumber < this.properties.oldUserInvitedPeopleNumber) {
  //   //   console.log('0 < this.properties.alradyInvitionNumber && this.properties.alradyInvitionNumber < this.properties.oldUserInvitedPeopleNumber')
  //   //   this.setData({
  //   //     moveStage: {
  //   //       style: 'active', // leave 0 none 1-9 active 10 leave
  //   //       messageStyle: 'active',
  //   //       lastStyle: 'last',
  //   //       number: (this.properties.alradyInvitionNumber / this.properties.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //   //     }
  //   //   }) 
  //   // }

  //   // if (this.properties.alradyInvitionNumber === this.properties.oldUserInvitedPeopleNumber) {
  //   //   console.log('alradyInvitionNumber', this.properties.alradyInvitionNumber)
  //   //   console.log('oldUserInvitedPeopleNumber', this.properties.oldUserInvitedPeopleNumber);
  //   //   console.log('this.properties.alradyInvitionNumber === this.properties.oldUserInvitedPeopleNumber', this.properties.alradyInvitionNumber === this.properties.oldUserInvitedPeopleNumber);
  //   //   this.setData({
  //   //     moveStage: {
  //   //       style: 'leave', // leave 0 none 1-9 active 10 leave
  //   //       messageStyle: 'leave',
  //   //       lastStyle: 'last',
  //   //       number: (this.properties.alradyInvitionNumber / this.properties.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //   //     }
  //   //   }) 
  //   // }

  //   // // 判断是否已经领取奖励
  //   // // 重新发起 代表已经领取了奖励
  //   // if (this.properties.progressStatus === 'RECEIVED_AWARD') {
  //   //   console.log("this.properties.progressStatus === 'RECEIVED_AWARD'");
  //   //   this.setData({
  //   //     moveStage: {
  //   //       style: 'leave', // leave 0 none 1-9 active 10 leave
  //   //       messageStyle: 'gray',
  //   //       lastStyle: 'last',
  //   //       number: (this.properties.alradyInvitionNumber / this.properties.oldUserInvitedPeopleNumber) * 100, // (已经邀请人数 / 邀请的总人数) * 100
  //   //     }
  //   //   }) 
  //   // }

  //   // console.log('moveStage', this.data.moveStage)
  //   // console.log(this.properties)

  // },

  methods: {

  }
});