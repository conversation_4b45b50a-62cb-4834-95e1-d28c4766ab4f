@import "config/theme";

@mixin wp-common-follow-account-theme($theme) {
  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    20% {
      opacity: .2;
    }
    40% {
      opacity: .4;
    }
    60% {
      opacity: .6;
    }
    80% {
      opacity: .8;
    }
    100% {
      opacity: 1;
    }
  }

  $primary: map-get($theme, primary);
  .wp-common-follow-account__container {
    display: none;
    position: absolute;
    z-index: 9999;
    height: 100%;
    width: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: #BEBEBE;
    animation: fade-in .2s ease-in-out;

    &__wrapper {
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -45%, 0);
      position: absolute;
      width: u(668);
      height: u(1170);

      &__image {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
      }

      &__close {
        position: absolute;
        z-index: 2;
        width: u(100);
        height: u(48);
        right: u(60);
        top: u(26);
      }

      &__account {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: u(492);
        height: u(92);
        z-index: 2;
        bottom: u(56);
      }

      &.has-navbar {
        top: 48%;
        height: u(1070);
      }
    }
  }

  .wp-common-follow-account__container.show {
    display: block;
  }

}

@include wp-common-follow-account-theme($lucky-theme);
