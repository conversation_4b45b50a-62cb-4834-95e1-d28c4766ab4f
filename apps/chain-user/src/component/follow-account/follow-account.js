Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    hasNavBar: {
      type: Boolean,
      value: false
    }
  },
  data: {
    // coverImage: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.iWxEsZXUvThja17a99cece6e75625c401df30852f83a.png',
    coverImage: 'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs_Lcll3ysGxCj8erlAz5Tmw.vCrvtKjxJbFQf5a32b9db7cfd41915a4d88dac2a12bd.png',
    isIPhoneX: false
  },
  ready() {
    this.getIphonex();
  },
  methods: {

    closePanel() {
      this.triggerEvent('close');
    },

    followAccount() {
      this.triggerEvent('follow');
    },

    getIphonex() {
      const isIPhoneX = getApp().globalData.isIphone;
      this.setData({
        isIPhoneX
      })
    }
  },
});
