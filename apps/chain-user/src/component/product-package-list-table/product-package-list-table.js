Component({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
    pageState: {
      type: Number,
      value: ''
    },
      currencySymbol:{
          type:String,
          value:'￥'
      }
  },

  methods: {
    // 自定义方法
    onAddToShoppingCartTap({
      currentTarget: {
        dataset: {
          detail
        }
      },
      touches
    }) {
      this.triggerEvent('addTap', {
        data: detail,
        touches: touches,
      });
    },

    onPreventTap() {
      this.triggerEvent('hint');
    },

    _onPreventTap() {
      return false;
    },

    onGetPhoneNumberTap(ev) {
      this.triggerEvent('registry', {data: ev});
    }
  }
});
