<view class="wp-crown-shopping-product-package-list-table__wrapper {{detail.type && detail.type === 'search' ? 'wp-crown-shopping-product-package-list-table__wrapper-search' : ''}}">
  <view class="wp-crown-shopping-product-package-list-table__image-container skeleton-rect">
    <image src="{{detail.image}}?x-oss-process=image/resize,w_168/quality,Q_80/format,jpg" />
    <image class="wp-crown-shopping-product-package-list-table__image-container-sold-out-img"
      wx:if="{{!detail.isQuantityEnough}}"
      src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/edb6485990c340d7a13f13f1430e1d5e?nsukey=hA%2Fw%2F13oKumKoYjVkUPkETTVVmRyLjgkKv7ST7J5nUYCKDHVSqbNQo8fuPX7SQrupOF1UTW7r0rEiwXxOz3loIIxl0Hw6GPcRi0VAaf2ITeC%2FgYWgv6mrtCvqxCU%2FKp%2Fi7R5IliUS%2Bi7XoOFdY2hlQ0bZsamwtbc7HGhU0mb9Hrbu99CeKmlt8Du6rz2AcRB6a2cgGceJkfFij3ehxyscQ%3D%3D"></image>
  </view>
  <view class="wp-crown-shopping-product-package-list-table__main-content">
    <view class="wp-crown-shopping-product-package-list-table__main-content-title skeleton-rect">
      {{detail.name}}
    </view>
    <view class="wp-crown-shopping-product-package-list-table__main-content-discount-info-container">
      <view class="wp-crown-shopping-product-package-list-table__main-content-discount-info-item" wx:if="{{detail.tags && detail.tags.length}}" wx:for="{{detail.tags}}" wx:key="index">{{item}}</view>
    </view>
    <view class="wp-crown-shopping-product-package-list-table__main-content-footer-container">
      <view class="wp-crown-shopping-product-package-list-table__main-content-footer-price-info skeleton-rect">
        <view class="wp-crown-shopping-product-package-list-table__main-content-footer-price-current">{{currencySymbol}}{{detail.price}}</view>
        <view class="wp-crown-shopping-product-package-list-table__main-content-footer-price-discount">{{detail.discountInfo}}</view>
      </view>
      <block wx:if="{{detail.isQuantityEnough}}">
        <image
          class="wp-crown-shopping-product-package-list-table__main-content-footer-icon skeleton-radius"
          wx:if="{{pageState === 1 || pageState === 3 }}"
          src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs4KIjfB3OGt_WQOIn85Sd2I.CRenGoj4kZGxa768146149fc0f3d3a6293439daff5a7.png"
          catchtap="_onPreventTap">
        </image>
        <image
          wx:else
          class="wp-crown-shopping-product-package-list-table__main-content-footer-icon skeleton-radius"
          src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs4KIjfB3OGt_WQOIn85Sd2I.CRenGoj4kZGxa768146149fc0f3d3a6293439daff5a7.png"
          >
          <view class="wp-crown-shopping-product-package-list-table__main-content-footer-icon-tap"
            catchtap="onAddToShoppingCartTap"
            data-detail="{{detail}}"
          ></view>
        </image>
      </block>
      <view
        class="wp-crown-shopping-product-package-list-table__main-content-footer-icon-wrapper"
        wx:if="{{!detail.isQuantityEnough}}"
        catchtap="onPreventTap">
        <image class="wp-crown-shopping-product-package-list-table__main-content-footer-icon skeleton-radius" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/images/d3aac88c53fd40f78993df2c6c1a7c96"></image>
      </view>
    </view>
  </view>
</view>
