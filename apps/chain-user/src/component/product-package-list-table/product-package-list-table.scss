@import 'config/theme';

@mixin wp-crown-shopping-product-package-list-table-theme($theme) {
  @include wp-crown-shopping-product-package-list-table-basic-theme($theme);
  @include wp-crown-shopping-product-package-list-table-image-theme($theme);
  @include wp-crown-shopping-product-package-list-table-main-content-theme($theme);
}

@mixin wp-crown-shopping-product-package-list-table-basic-theme($theme) {
  .wp-crown-shopping-product-package-list-table {
    &__wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      padding: 0 u(28) u(32);

      &-search {
        padding: 0 u(32) u(26);
      }
    }
  }
}

@mixin wp-crown-shopping-product-package-list-table-image-theme($theme) {
  .wp-crown-shopping-product-package-list-table {
    &__image {
      &-container {
        display: flex;
        align-items: center;
        justify-content: center;

        position: relative;

        margin-right: u(24);

        background-color: #fcfcfc;

        image {
          width: u(168);
          height: u(184);
          border-radius: u(8);
        }

        &-sold-out-img {
          position: absolute;
          top: 0;
          left: 50%;

          transform: translateX(-50%);
        }
      }
    }
  }
}

@mixin wp-crown-shopping-product-package-list-table-main-content-theme($theme) {
  $primary: map-get($theme, primary);
  $foreground: map-get($theme, foreground);
  .wp-crown-shopping-product-package-list-table {
    &__main-content {
      display: flex;
      flex-direction: column;
      justify-content: center;

      flex-grow: 1;

      &-title {
        overflow: hidden;
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 2;
        display: flex;
        box-orient: vertical;
        line-clamp: 2;

        line-height: u(32);

        font-size: u(26);
        color: #3a3f4a;
      }

      &-paid-number-info {
        font-size: u(18);
        color: #9da7ba;
      }

      &-discount-info {
        &-container {
          display: flex;
        }

        &-item {
          margin-right: u(4);
          padding: u(2) u(4);
          border: u(1) solid mat-color($primary);
          border-radius: u(5);

          font-size: u(16);
          color: mat-color($primary);

          margin-top: u(8);
        }
      }

      &-footer {
        &-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          margin-top: u(16);
        }

        &-price {
          &-info {
            // display: flex;
            // align-items: flex-end;
            // align-items: center;
          }

          &-current {
            color: mat-color($primary);
            font-size: u(28);
            margin-right: u(12);
          }

          &-discount {
            color: #ff587d;
            font-size: u(20);
          }

          &-original {
            transform: translateY(u(-4));
            color: #c4c9d2;
            font-size: u(20);
            text-decoration: line-through;
          }
        }

        &-icon {
          height: u(44);
          width: u(44);
          border-radius: 50%;

          position: relative;
          overflow: visible;

          &--button {
            display: block;
            border: none;
            outline: none;
            background: transparent;
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;

            &::after {
              border: none;
            }
          }

          &-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;

            width: u(44);
            height: u(44);
          }

          &-tap {
            position: absolute;
            top: u(-20);
            left: u(-20);
            bottom: u(-20);
            right: u(-20);

            z-index: 1;
          }
        }
      }
    }
  }
}

@include wp-crown-shopping-product-package-list-table-theme($lucky-theme);
