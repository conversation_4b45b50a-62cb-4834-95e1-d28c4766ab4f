/**
 * 业务组件，实现引导动画
 * 输入：需要引导的页面
 * 输出：变更引导动画的状态，是否已经播放完
 * guideHasShown: {
 *   booking[Boolean],
 *   home[Boolean],
 *   orderCreate[Boolean],
 * }
 */
import {
  store,
  getStorage
} from "@petkit/redux";

Component({
  behaviors: [],
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    // home, booking, orderCreate
    page: {
      type: String,
      value: "",
    },
  },

  data: {
    indicatorColor: "rgba(255, 255, 255, .7)",
    indicatorActiveColor: "#23b9de",
    currTutorialImgIndex: 0,
    tutorialImgUrls: [],
    standardTutorialImgUrls: {
      booking: {
        iphone7: [
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.YpemzvVnd0Bn3c99bc6bb062e5c342ede82d7e0d14e2.png",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.vBGRXUm3xou7234aad553834fd5188b35ce2327e22e5.png",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.iBDDAAVdBbxDeddd6e8907a3bf95f3bf29140bed4974.png",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.wPIALUaRggfi0e362cc74ccbb5e0dbd749b966845b9e.png",
        ],
        iphoneX: [
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.oT0Qw9VOc5hy5a0ebc91a81eee060cb8a7191ea7d755.jpg",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.8iKbC5C41JKjb0b4817a17e2687c8b64660cfb07408b.png",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.WtdwjuJCQtO38271e094c0ec785ba4322be5bbc2bcae.png",
          "https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.TAucfARRlvme145bbd9d9958b6eac192991e7c2af2f5.png",
        ],
      },
      home: {
        iphone7: [
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.xw8OLiKnTSqa38ae5d574ec13ad6abd01b2616d89f9d.png',
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.iJBaRr83nh91d19f2c2ba927a2a12d874b8cdb4ea9b7.png',
        ],
        iphoneX: [
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.yBjKYBmfPTUB2224cfcd5753fa96cf8a6171933c5a88.jpg',
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.Q8xpYwcjNOL68b1f8fa705782b3ac7ad4ed88c241ed5.jpg',
        ],
      },
      orderCreate: {
        iphone7: [
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.yU5U6rHjiDzF0b6e489bf110e152820b95a9338ac7f2.png',
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.caoSDTa0XbxAc554039e75e7376162fd862496aa863a.png'
        ],
        iphoneX: [
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.vXN3CCW7hJfX11f04ae31332875cd2b21092d446ae0c.png',
          'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs5mbk2Q5NvTn7aMRgbMRo-s.TBv1C6ul718E725ab6a4b9cc22658c458bd9f0e56849.png'
        ],
      },
    },
    isIphone: false

  }, // 私有数据，可用于模板渲染

  lifetimes: {
    // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
    attached: function () {
      this._initTutorial();
    },
  },
  attached: function () {
    this._initTutorial();
  },

  methods: {
    _initTutorial() {
      const isIphone = getApp().globalData.isIphone;
      const pages = this.data.standardTutorialImgUrls[this.data.page];
      let tutorialImgUrls = [];
      const iphoneXHeight = 812;
      // const iphone7 = 0.622;
      // const iphoneX = 0.518;
      // const standardScale = (iphone7 + iphoneX) / 2;
      const {
        windowWidth,
        windowHeight,
      } = store.getState().global.env.systemInfo;
      // const currentScale = windowWidth / windowHeight;
      // if (currentScale > standardScale) {
      //   tutorialImgUrls = this.data.standardTutorialImgUrls[this.data.page]["iphone7"];
      // } else {
      //   tutorialImgUrls = this.data.standardTutorialImgUrls[this.data.page]["iphoneX"];
      // }
      tutorialImgUrls = (isIphone || windowHeight >= iphoneXHeight) ? pages["iphoneX"] : pages["iphone7"];

      this.setData({
        tutorialImgUrls
      });
    },

    _closeTutorial() {
      let guideHasShown = getStorage().getItem('guideHasShown') || {};
      guideHasShown[this.data.page] = true;
      getStorage().setItem("guideHasShown", guideHasShown);
      this.triggerEvent('close', true);
    },

    // 教程
    onTutorialImageTap() {
      const {currTutorialImgIndex, tutorialImgUrls} = this.data;

      if (currTutorialImgIndex === tutorialImgUrls.length - 1) {
        this._closeTutorial();
      } else {
        this.setData({
          currTutorialImgIndex: currTutorialImgIndex + 1,
        });
      }
    },

    onTutorialSwiperChange({detail: {current}}) {
      this.setData({
        currTutorialImgIndex: current,
      });
    },

    onSkipTutorialTap() {
      this._closeTutorial();
    },

  },
});
