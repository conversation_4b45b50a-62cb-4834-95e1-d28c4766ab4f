<!-- <view class="mp-common-animation-guide__tutorial-container"> -->
  <view class="mp-common-animation-guide__tutorial">
    <view class="mp-common-animation-guide__tutorial-skip" wx:if="{{page === 'booking'}}" bind:tap="onSkipTutorialTap">跳过</view>
    <swiper class="mp-common-animation-guide__tutorial-swiper"
      indicator-dots="{{true}}"
      autoplay="{{false}}"
      circular="{{false}}"
      indicator-color="{{indicatorColor}}"
      indicator-active-color="{{indicatorActiveColor}}"
      current="{{currTutorialImgIndex}}"
      bind:change="onTutorialSwiperChange"
    >
      <block wx:for="{{tutorialImgUrls}}" wx:key="item">
        <swiper-item class="mp-common-animation-guide__tutorial-swiper-item">
          <image class="mp-common-animation-guide__tutorial-swiper-img"
                 src="{{item}}"
                 mode="scaleToFill"
                 bind:tap="onTutorialImageTap" />
        </swiper-item>
      </block>
    </swiper>
  </view>
<!-- </view> -->
