@import 'config/theme';
@mixin mp-common-animation-guide-theme() {
  .mp-common-animation-guide {
    &__tutorial {
      position: absolute;
      z-index: 100;

      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

      &-container {
        width: 100%;
        height: 100%;
        position: relative;
      }

      &-skip {
        position: absolute;
        z-index: 1;

        right: u(24);
        top: u(24);

        color: #fff;

        font-size: u(36);
        line-height: 1em;
      }

      &-swiper {
        width: 100%;
        height: 100%;
        background-color: #f2f4f8;

        &-item {
          width: 100%;
          height: 100%;
        }

        &-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

@include mp-common-animation-guide-theme();
