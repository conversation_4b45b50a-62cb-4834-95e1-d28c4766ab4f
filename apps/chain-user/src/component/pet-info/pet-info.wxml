<view class="wp-comp-upload__container {{ type === 'avatar' ? 'wp-comp-upload__container--avatar' : '' }}">
  <view wx:for="{{items}}" class="wp-comp-upload__item-container {{ type === 'avatar' ? 'wp-comp-upload__item-container--avatar' : '' }}">
    <block wx:if="{{type === 'video'}}">
      <video class="wp-comp-upload__item-video" src="{{item.tempFilePath || item.url}}"></video>
    </block>
    <block wx:elif="{{type === 'avatar'}}">
      <image class="wp-comp-upload__item-avatar" src="{{item.tempFilePath || item.url}}" bindtap="onCreateTap"></image>
    </block>
    <block wx:else>
      <image class="wp-comp-upload__item-video" src="{{item.tempFilePath || item.url}}"></image>
    </block>
  </view>
  <view wx:if="{{editable && type !== 'avatar'}}" class="wp-comp-upload__item-create" bindtap="onCreateTap">
    <text class="PETKIT icon-add"></text>
    {{hintText}}
  </view>
</view>

