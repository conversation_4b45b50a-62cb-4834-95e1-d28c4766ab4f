@import 'config/theme';
@import '~@petkit/style/icon/iconfont';

@mixin wp-comp-upload-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .wp-comp-upload {
    &__container {
      display: block;
      overflow: auto;

      &--avatar {
        width: u(152);
        height: u(152);
      }
    }

    &__item {
      &-container {
        $width: u(280);
        display: inline-block;
        float: left;
        width: $width;
        height: $width;
        border: 1px solid map-get($foreground, divider);
        margin: u(8);

        &--avatar {
          width: u(152);
          height: u(152);
          border: none;
          margin: 0;
        }
      }

      &-video {
        width: 100%;
        height: 100%;
      }

      &-avatar {
        width: u(152);
        height: u(152);
      }

      &-create {
        @extend .wp-comp-upload__item-container;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icon-add {
          font-size: u(56);
        }
      }
    }
  }
}

@include wp-comp-upload-theme($app-theme);
@include iu-icon-iconfont-theme($app-theme);

