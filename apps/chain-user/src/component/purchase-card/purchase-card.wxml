<view class="mp-purchase-card__container {{item._isExpired ? 'mp-purchase-card--disabled' :  'mp-purchase-card--normal' }}"
      wx:for="{{list}}" wx:for-item="item" wx:key="index">
  <!--券包显示-->
  <block wx:if="{{item.couponType!=='DEDUCTION'}}">
    <!-- 服务预购券 -->
    <view class="mp-purchase-card__wrapper" wx:if="{{item.type === 'SERVICE'}}" bindtap="onTitleTap">
      <view class="mp-purchase-card__header" bindtap="onTitleTap">
        <view class="mp-purchase-card__header-title" bindtap="onTitleTap">
          {{item.strategyName}}
          <text class="mp-purchase-card__header-icon mp-purchase-card__header-icon--warn" wx:if="{{item.type === 'SERVICE'}}">{{item._type}}</text>
          <text class="mp-purchase-card__header-icon mp-purchase-card__header-icon--primary" wx:if="{{item.type === 'PRODUCT'}}">{{item._type}}</text>
        </view>
        <view class="mp-purchase-card__header-link">
          <button class="mp-purchase-card__header-order-button" data-item="{{item}}" bindtap="onOrderTap">
            立即预约
          </button>
        </view>
        <view class="mp-purchase-card__header-time">
          有效期
          <text>{{item._effectiveDate}}</text>
          至
          <text>{{item._expirationDate}}</text>
        </view>
        <view class="mp-purchase-card__header-time mp-purchase-card__header-time-text">
          使用时间限制：{{item.restrictionDesc}}
        </view>
      </view>
      <view class="mp-purchase-card__divider-container">
        <view class="mp-purchase-card__divider"></view>
        <view class="mp-purchase-card__divider-circle-left"></view>
        <view class="mp-purchase-card__divider-circle-right"></view>
      </view>
      <view class="mp-purchase-card__main">
        <view class="mp-purchase-card__main-tips">*可使用门店：{{item._storeName}}</view>
        <!-- 服务内容 -->
        <view class="mp-purchase-card__main-content">
          <view class="mp-purchase-card__main-content-item" wx:for="{{item.strategies}}" wx:for-index="index2" wx:for-item="service" wx:key="index2">
            <view class="mp-purchase-card__main-content-item-left">{{service.serviceName}}</view>
            <view class="mp-purchase-card__main-content-item-right">
              剩余：<text>{{service._limitNumber}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 商品预购券 -->
    <view class="mp-purchase-card__wrapper" wx:if="{{item.type === 'PRODUCT'}}">
      <view class="mp-purchase-card__header">
        <view class="mp-purchase-card__header-title">
          {{item.skuName}}
          <text class="mp-purchase-card__header-icon mp-purchase-card__header-icon--warn" wx:if="{{item.type === 'SERVICE'}}">{{item._type}}</text>
          <text class="mp-purchase-card__header-icon mp-purchase-card__header-icon--primary" wx:if="{{item.type === 'PRODUCT'}}">{{item._type}}</text>
        </view>
        <view class="mp-purchase-card__header-time">
          有效期
          <text>{{item._effectiveDate}}</text>
          至
          <text>{{item._expirationDate}}</text>
        </view>
      </view>
      <view class="mp-purchase-card__divider-container">
        <view class="mp-purchase-card__divider"></view>
        <view class="mp-purchase-card__divider-circle-left"></view>
        <view class="mp-purchase-card__divider-circle-right"></view>
      </view>
      <view class="mp-purchase-card__main">
        <!-- 商品内容 -->
        <view class="mp-purchase-card__main-content">
          <view class="mp-purchase-card__main-content-item-center">
            剩余次数： {{item._remainedNumber}}/{{item._initNumber}}
          </view>
        </view>
      </view>
    </view>
  </block>
</view>
