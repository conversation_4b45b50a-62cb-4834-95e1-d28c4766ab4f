@import 'config/theme';
@mixin wp-crown-service-booking-package-list-theme($theme) {
  $primary: map-get($theme, primary);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  .wp-crown-service-booking-package-list {
    overflow: auto;
    overflow-x: hidden;
    &__container {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        width: u(702);
        height: u(176);
        margin: u(24);
        border-radius: u(12);

      &-main {
        display: flex;
        justify-content: center;
        align-items: center;
        width: u(702);

        &-add {
          margin-left: u(24);
          &-checked {
            width: 100%;
            height: 100%;
            color: mat-color($primary);
            font-size: u(40);
            display: flex;
            align-items: center;
          }
          &-unchecked {
            width: 100%;
            height: 100%;
            color: rgba(176,176,176,1);
            font-size: u(40);
            display: flex;
            align-items: center;
          }
        }

        &-image {
          width: u(112);
          height: u(112);
          box-sizing: border-box;
          padding: u(16);
          border-radius: u(10);
          border: u(2) solid rgba(227, 227, 227, .99);
          margin: 0 u(24);

          &-icon {
            width: u(80);
            height: u(80);
          }
        }

        &-content {
          width: u(526);
          margin-left: u(10);
          display: flex;
          align-items: center;

          &-title {
            font-size: u(28);
            font-weight: 400;
            color: #2F2F2F;
            margin-right: u(160);
            width: 100%;
          }

          &-add {
            width: u(112);
            height: u(48);
            background-color: mat-color($primary);
            font-size: u(24);
            color: #ffffff;
            border-radius: u(24);
            text-align: center;
            line-height: u(48);
            position: absolute;
            margin-top: u(-24);
            right: u(48);

            &-detail {
              &.already {
                background-color: #b0b0b0;
                border-radius: u(24);
              }
            }

          }

          &-price {
            &-store {
              font-size: u(20);
              line-height: 1em;
              color: #9397a2;
              text-align: left;
              text-decoration: line-through;
              margin-top: u(4);
            }

            &-vip {
              font-size: u(24);
              line-height: 1em;
              color: map-get($foreground, hint-text);
              margin-right: u(10);
            }
          }

          &-description {
            font-size: u(22);
            color: #b0b0b0;
            margin-right: u(24);
            width: u(430);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &-arrow {
            margin-right: u(24);

            & > text {
              font-size: u(18);
              font-weight: bold;
              color: #9397a2;
            }
          }
        }
      }

      &__show {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &__content {
        width: u(430);
      }
    }
  }
}
@include wp-crown-service-booking-package-list-theme($lucky-theme);
