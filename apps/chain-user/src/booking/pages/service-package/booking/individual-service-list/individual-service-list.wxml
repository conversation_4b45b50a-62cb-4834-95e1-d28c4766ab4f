<view class="wp-crown-service-booking-package-list">
  <view class="wp-crown-service-booking-package-list__container" wx:if="{{singleSkus || (singleSkus && singleSkus.length != 0)}}" wx:for="{{singleSkus}}" wx:key="single" wx:for-item="single">
    <view class="wp-crown-service-booking-package-list__container-main">
      <view class="wp-crown-service-booking-package-list__container__show" bind:tap="onSingleSkuSelected" data-id="{{single.id}}">
        <view class="wp-crown-service-booking-package-list__container-main-add">
          <text class="PETKIT icon-xuanzhong wp-crown-service-booking-package-list__container-main-add-checked" wx:if="{{single.isSelected}}"></text>
          <text class="PETKIT icon-circle wp-crown-service-booking-package-list__container-main-add-unchecked" wx:if="{{!single.isSelected}}"></text>
        </view>
        <view class="wp-crown-service-booking-package-list__container-main-image">
          <image class="wp-crown-service-booking-package-list__container-main-image-icon" src="{{single.imgUrl}}?x-oss-process=image/resize,w_120/quality,Q_80/format,jpg" />
        </view>
      </view>
      <view class="wp-crown-service-booking-package-list__container-main-content" data-id="{{single.id}}" bindtap="onIndividualImageShow">
        <view class="wp-crown-service-booking-package-list__container__content">
          <view class="wp-crown-service-booking-package-list__container-main-content-title">
            {{single.spuName}}
          </view>
          <view class="wp-crown-service-booking-package-list__container-main-content-price">
            <text class="wp-crown-service-booking-package-list__container-main-content-price-vip">{{currencySymbol}}{{single.vipPrice || single.storePrice}}</text>
            <text class="wp-crown-service-booking-package-list__container-main-content-price-store" wx:if="{{single.vipPrice}}">{{currencySymbol}}{{single.storePrice}}</text>
          </view>
          <view class="wp-crown-service-booking-package-list__container-main-content-description" wx:if="{{single.remark}}">
            {{single.remark}}
          </view>
        </view>
        <view class="wp-crown-service-booking-package-list__container-main-content-arrow">
          <text class="PETKIT icon-right-arrow"></text>
        </view>
      </view>
    </view>
  </view>
  <view class="wp-crown-service-booking-package-list__container" wx:if="{{!singleSkus || (singleSkus && singleSkus.length === 0)}}">
    抱歉，暂无可选服务套餐！
  </view>
</view>
