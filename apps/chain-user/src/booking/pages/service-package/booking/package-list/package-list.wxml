<view class="wp-crown-service-booking-package-list">
  <view class="wp-crown-service-booking-package-list__container"
        wx:if="{{selectedPackageSku}}"
        wx:for="{{packageSkus}}"
        wx:key="package"
        wx:for-item="package"
       >
    <view class="wp-crown-service-booking-package-list__container-main">
      <view class="wp-crown-service-booking-package-list__container__show" data-id="{{package.id}}" bind:tap="onPackageSkuSelected">
        <view class="wp-crown-service-booking-package-list__container-main-add">
          <view class="wp-crown-service-booking-package-list__container-main-add-checked" wx:if="{{package.isSelected}}">
            <text class="PETKIT icon-xuanzhong"></text>
          </view>
          <view class="wp-crown-service-booking-package-list__container-main-add-unchecked" wx:if="{{!package.isSelected}}">
            <text class="PETKIT icon-circle"></text>
          </view>
        </view>
        <view class="wp-crown-service-booking-package-list__container-main-image">
          <view class="wp-crown-service-booking-package-list__container-main-image-content">
            <image class="wp-crown-service-booking-package-list__container-main-image-icon" src="{{package.imgUrl}}?x-oss-process=image/resize,w_120/quality,Q_80/format,jpg" />
          </view>
        </view>
      </view>
      <view class="wp-crown-service-booking-package-list__container-main-content" data-id="{{package.id}}" bindtap="onPackageImageShow">
        <view class="wp-crown-service-booking-package-list__container__content">
          <view class="wp-crown-service-booking-package-list__container-main-content-title">{{package.spuName}}</view>
          <view class="wp-crown-service-booking-package-list__container-main-content-price">
            <text class="wp-crown-service-booking-package-list__container-main-content-price-vip">{{currencySymbol}}{{package.vipPrice || package.storePrice}}</text>
            <text class="wp-crown-service-booking-package-list__container-main-content-price-store" wx:if="{{package.vipPrice}}">{{currencySymbol}}{{package.storePrice}}</text>
          </view>
          <view class="wp-crown-service-booking-package-list__container-main-content-description" wx:if="{{package.remark}}">
            {{package.remark}}
          </view>
        </view>
        <view class="wp-crown-service-booking-package-list__container-main-content-arrow">
          <text class="PETKIT icon-right-arrow"></text>
        </view>
      </view>
    </view>
  </view>
  <view class="wp-crown-service-booking-package-list__container" wx:if="{{!selectedPackageSku}}">
    抱歉，暂无可选服务套餐！
  </view>
</view>

