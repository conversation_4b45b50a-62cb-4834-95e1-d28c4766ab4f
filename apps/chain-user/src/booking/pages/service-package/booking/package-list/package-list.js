import {
  acs,
} from '@petkit/redux';
import {
  bindActionCreators,
} from 'redux';
import {
  connect,
} from 'utils/weapp-redux';

const pageConfig = {
  data: {
  },

  onLoad() {},

  onPackageSkuSelected({
    currentTarget: {
      dataset: {
        id,
      }
    }
  }) {
    this.onPackageSkuSelectedAc({
      packageSkuId: id,
    });
  },

  onSingleSkuSelected({
    currentTarget: {
      dataset: {
        id,
      }
    }
  }) {
    this.onSingleSkuSelectedAc({
      singleSkuId: id,
    });
  },

  onPackageImageShow(ev) {
    const id = ev.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/booking/pages/service-package/booking/package-image-show/package-image-show?id=${id}`
    });
  },


};

const mapStateToData = state => {

  const pets = state.servicePackage.booking.pets;

  const packageSkus = state.servicePackage.booking.packageSkus;
  const selectedPackageSku = packageSkus.find(sku => sku.isSelected) || null;
  const singleSkus = state.servicePackage.booking.singleSkus;
  const currencySymbol = state.wp.store.warehouse.warehouseById.currencySymbol || '￥';


  return {
    pets,
    packageSkus,
    selectedPackageSku,
    singleSkus,currencySymbol
  };
};

const mapDispatchToPage = dispatch => bindActionCreators({
  onPetSelectedAC: acs.servicePackage.booking.onPetSelected,
  onPetActivedAc: acs.servicePackage.booking.onPetActived,
  onPackageSkuSelectedAc: acs.servicePackage.booking.onPackageSkuSelected,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));

