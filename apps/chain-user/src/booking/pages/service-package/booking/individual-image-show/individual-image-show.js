import {acs} from '@petkit/redux';
import {cloneDeep, map, chunk} from 'lodash-es';
import {bindActionCreators} from 'redux';
import {connect} from 'utils/weapp-redux';

const pageConfig = {
  data: {
    finishLoadFlag: false,
    errorLoadFlag: false
  },
  onLoad(e) {
    const id = e.id;
    let detailIndividualImage = '';
    let selectedSingleSkus = null;
    if (this.data.singleSkus) {
      this.data.singleSkus.map(sku => {
        if (sku.id === Number(id)) {
          if (sku.detailImages) {
            const image = JSON.parse(sku.detailImages);
            detailIndividualImage = image.result[0];
          }
          selectedSingleSkus = sku;
        }
      });
      this.setData({
        detailIndividualImage: detailIndividualImage,
        selectedSingleSkus: selectedSingleSkus
      });
    }
    // iPhone系列适配
    let isIphone = getApp().globalData.isIphone;
    this.setData({
      isIphone: isIphone
    });
  },

  finishLoad(e) {
    this.setData({
      finishLoadFlag: true
    });
  },

  errorLoad(e) {
    this.setData({
      finishLoadFlag: true,
      errorLoadFlag: true
    });
  },

  onSingleSkuSelected({
    currentTarget: {
      dataset: { id }
    }
  }) {
    this.onSingleSkuSelectedAc({
      singleSkuId: id
    });

    let selectedSingleSkus = null;
    if (this.data.singleSkus) {
      this.data.singleSkus.map(sku => {
        if (sku.id === Number(id)) {
          selectedSingleSkus = sku;
          if (sku.isSelected) {
            wx.navigateBack();
          }
        }
      });
      this.setData({
        selectedSingleSkus: selectedSingleSkus
      });
    }
  },

  onSettlement() {
    wx.navigateBack({
      delta: 2
    });
  }
};

const mapStateToData = state => {
  const pets = state.servicePackage.booking.pets;

  const packageSkus = state.servicePackage.booking.packageSkus;
  const singleSkus = state.servicePackage.booking.singleSkus;

  const currencySymbol= state.wp.store.warehouse.warehouseById.currencySymbol || '￥';

  return {
    pets,
    packageSkus,
    singleSkus,
    currencySymbol
  };
};

const mapDispatchToPage = dispatch =>
  bindActionCreators(
    {
      onPetSelectedAC: acs.servicePackage.booking.onPetSelected,
      onPetActivedAc: acs.servicePackage.booking.onPetActived,
      onPackageSkuSelectedAc: acs.servicePackage.booking.onPackageSkuSelected,
      onSingleSkuSelectedAc: acs.servicePackage.booking.onSingleSkuSelected
    },
    dispatch
  );

Page(
  connect(
    mapStateToData,
    mapDispatchToPage
  )(pageConfig)
);
