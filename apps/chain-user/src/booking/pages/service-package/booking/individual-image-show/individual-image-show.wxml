<view style="padding-bottom: 90rpx">
  <view wx:if='{{!finishLoadFlag}}' style='width: 100%; height: 100%;'></view>
  <image class='{{finishLoadFlag ? "" : "before-load"}}' src='{{detailIndividualImage}}' bindload='finishLoad' mode="widthFix" lazy-load="true" style="width: 100%" />
  <view class="wp-crown-service-booking-individual-image-show__button" wx:if="{{selectedSingleSkus}}">
    <view class="wp-crown-service-booking-individual-image-show__button-price {{isIphone ? 'wp-crown-service-booking-individual-image-show__button-price--isIphone' : ''}}">
      <view class="wp-crown-service-booking-individual-image-show__button-price-vipPrice">
        {{currencySymbol}}{{selectedSingleSkus.vipPrice || selectedSingleSkus.storePrice}}
      </view>
      <view class="wp-crown-service-booking-individual-image-show__button-price-storePrice" wx:if="{{selectedSingleSkus.vipPrice}}">
        {{currencySymbol}}{{selectedSingleSkus.storePrice}}
      </view>
    </view>
    <view class="wp-crown-service-booking-individual-image-show__button-choice" wx:if="{{!selectedSingleSkus.isSelected}}" bind:tap="onSingleSkuSelected" data-id="{{selectedSingleSkus.id}}">
      添加
    </view>
    <view class="wp-crown-service-booking-individual-image-show__button-choice" wx:if="{{selectedSingleSkus.isSelected}}" bind:tap="onSettlement" data-id="{{selectedSingleSkus.id}}">
      去结算
    </view>
  </view>
  <view wx:if='{{errorLoadFlag}}' style='width: 100%; height: 100%; text-align: center;'>
    网络异常, 加载失败
  </view>
</view>
