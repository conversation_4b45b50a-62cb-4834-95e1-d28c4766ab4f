@import "config/theme";
@mixin wp-crown-service-booking-package-image-show-theme($theme) {
  $primary: map-get($theme, primary);
  $foreground: map-get($theme, foreground);
  .before-load {
    width: 0;
    height: 0;
    opacity: 0;
  }
  .wp-crown-service-booking-package-image-container{
    padding-bottom: u(104);
    &.isIphone{
      padding-bottom: u(160);
    }
  }
  .wp-crown-service-booking-package-image-show {
    &__button {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      box-shadow: 0 -2rpx 20rpx #eee;
      z-index: 2;

      &-price {
        &--isIphone {
          padding-bottom: u(18);
        }
        &-vipPrice {
          font-size: u(40);
          color: map-get($foreground, hint-text);
        }
        &-storePrice {
          font-size: u(20);
          color: #9397a2;
          text-decoration: line-through;
          margin-top: u(-15);
          float: right;
          margin-bottom: u(8);
        }
      }

      &-choice {
        display: flex;
        justify-content: center;
        align-items: center;
        width: u(280);
        height: u(104);
        color: #fff;
        font-size: u(36);
        background-color: mat-color($primary);
        border-radius: 0;
        margin-right: 0;
        margin-left: u(24);
      }
    }
  }
}
@include wp-crown-service-booking-package-image-show-theme($lucky-theme);
