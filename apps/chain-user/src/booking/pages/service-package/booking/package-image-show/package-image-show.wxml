<view class="wp-crown-service-booking-package-image-container {{isIphone?'isIphone':''}}">
  <view wx:if='{{!finishLoadFlag}}' style='width: 100%; height: 100%;'></view>
  <image class='wp-crown-service-booking-package-image-container__image {{finishLoadFlag && !errorLoadFlag ? "" : "before-load"}}' src='{{detailPackageImage}}' bindload='finishLoad' binderror='errorLoad'
         mode="widthFix"
         style="width: 100%" />
  <view class="wp-crown-service-booking-package-image-show__button safe-area-inset-bottom" wx:if="{{selectedPackageSku}}">
    <view class="wp-crown-service-booking-package-image-show__button-price {{isIphone ? 'wp-crown-service-booking-package-image-show__button-price--isIphone' : ''}}">
      <view class="wp-crown-service-booking-package-image-show__button-price-vipPrice">
        {{currencySymbol}}{{selectedPackageSku.vipPrice || selectedPackageSku.storePrice}}
      </view>
      <view class="wp-crown-service-booking-package-image-show__button-price-storePrice" wx:if="{{selectedPackageSku.vipPrice}}">
        {{currencySymbol}}{{selectedPackageSku.storePrice}}
      </view>
    </view>
    <view class="wp-crown-service-booking-package-image-show__button-choice" wx:if="{{selectedPackageSku.isSelected}}" bind:tap="onSettlement">
      去结算
    </view>
    <view class="wp-crown-service-booking-package-image-show__button-choice" wx:if="{{!selectedPackageSku.isSelected}}" bind:tap="onPackageSelected" data-id="{{selectedPackageSku.id}}">
      选择
    </view>
  </view>
  <view wx:if='{{errorLoadFlag}}' style='width: 100%; height: 100%; text-align: center;'>
    网络异常, 加载失败
  </view>
</view>
