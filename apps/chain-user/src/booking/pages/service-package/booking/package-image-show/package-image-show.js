import { acs } from "@petkit/redux";
import { bindActionCreators } from "redux";
import { connect } from "utils/weapp-redux";

const pageConfig = {
  data: {
    finishLoadFlag: false,
    errorLoadFlag: false,
    isIphone: false
  },

  onLoad(e) {
    const id = e.id;
    let detailPackageImage = "";
    let selectedPackageSku = null;
    if (this.data.packageSkus) {
      this.data.packageSkus.map(sku => {
        if (sku.id === Number(id)) {
          if (sku.detailImages) {
            const image = JSON.parse(sku.detailImages);
            detailPackageImage = image.result[0];
          }
          selectedPackageSku = sku;
        }
      });
      this.setData({
        detailPackageImage: detailPackageImage,
        selectedPackageSku: selectedPackageSku
      });
    }
    // iPhone系列适配
    let isIphone = getApp().globalData.isIphone;
    this.setData({
      isIphone: isIphone
    });
  },

  finishLoad() {
    this.setData({
      finishLoadFlag: true
    });
  },

  errorLoad() {
    this.setData({
      finishLoadFlag: true,
      errorLoadFlag: true
    });
  },

  onPackageSelected({
    currentTarget: {
      dataset: { id }
    }
  }) {
    this.onPackageSkuSelectedAc({
      packageSkuId: id
    });

    let selectedPackageSku = null;
    if (this.data.packageSkus) {
      this.data.packageSkus.map(sku => {
        if (sku.id === Number(id)) {
          selectedPackageSku = sku;
          if (sku.isSelected) {
            wx.navigateBack();
          }
        }
      });
      this.setData({
        selectedPackageSku: selectedPackageSku
      });
    }
  },

  onSettlement() {
    wx.navigateBack({
      delta: 2
    });
  }
};

const mapStateToData = state => {
  const pets = state.servicePackage.booking.pets;

  const packageSkus = state.servicePackage.booking.packageSkus;
  const singleSkus = state.servicePackage.booking.singleSkus;
  const currencySymbol = state.wp.store.warehouse.warehouseById.currencySymbol || '￥';

  return {
    pets,
    packageSkus,
    singleSkus,
    currencySymbol
  };
};

const mapDispatchToPage = dispatch =>
  bindActionCreators(
    {
      onPetSelectedAC: acs.servicePackage.booking.onPetSelected,
      onPetActivedAc: acs.servicePackage.booking.onPetActived,
      onPackageSkuSelectedAc: acs.servicePackage.booking.onPackageSkuSelected,
      showToast: acs.global.toast.show,
      hideToast: acs.global.toast.hide
    },
    dispatch
  );

Page(
  connect(
    mapStateToData,
    mapDispatchToPage
  )(pageConfig)
);
