@import 'config/theme';
@import 'styles/util';
@import 'styles/icon';

@mixin mp-crown-order-shuttle-theme($theme) {
  @include mp-crown-order-shuttle-container-theme($theme);
  @include mp-crown-order-shuttle-header-theme($theme);
  @include mp-crown-order-shuttle-body-theme($theme);
  @include mp-crown-order-shuttle-buttons-theme($theme);
}

@mixin mp-crown-order-shuttle-container-theme($theme) {
  .mp-crown-order-shuttle {
    &__container {
      height: 100%;

      display: flex;
      flex-direction: column;
    }
  }
}

@mixin mp-crown-order-shuttle-header-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $background: map-get($theme, background);
  .mp-crown-order-shuttle {
    &__header {
      flex-shrink: 0;

      height: u(64);
      background-color: map-get($background, background);

      text-align: center;
      color: mat-color($accent);
      font-size: u(28);
      line-height: u(64);
    }
  }
}

@mixin mp-crown-order-shuttle-body-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $background: map-get($theme, background);
  .mp-crown-order-shuttle {
    &__body {
      flex-grow: 1;
      overflow: auto;

      padding: u(24) u(24) 0;
    }

    &__card {
      margin-bottom: u(24);
      padding: 0 u(24);

      border-radius: u(8);

      background-color: #fff;

      overflow: hidden;
    }

    &__service {
      font-size: u(28);
      line-height: u(28);
      color: #575d6a;

      margin-top: u(24);
    }

    &__item {
      margin: u(24) 0;
      font-size: u(28);
      line-height: u(28);

      display: flex;
      align-items: center;

      &-desc {
        margin-left: u(12);
        flex-grow: 1;
      }

      &-title {
        font-weight: 500;

        &-send {
          margin-bottom: u(12);
        }
      }

      &-text {
        font-size: u(24);
        color: #9397a2;
        margin-bottom: u(24);
      }

      &-time {
        margin-right: u(12);

        &--no-data {
          @extend .mp-crown-order-shuttle__item-time;
          color: mat-color($primary);
        }
      }

      &-community {
        margin-left: u(8);
      }
    }

    &__community {
      &-title {
        font-size: u(28);
        line-height: u(28);

        margin-top: u(24);
        margin-bottom: u(16);
      }

      &-store {
        color: mat-color($primary);
        margin: 0 u(8);
      }
    }

    &__address {
      font-size: u(28);
      line-height: u(28);
      color: #575d6a;

      margin-top: u(24);

      &-textarea {
        box-sizing: border-box;
        padding: u(24);

        border: u(2) solid #f1f1f1;
        border-radius: 4px;

        font-size: u(24);

        height: u(140);
        width: 100%;

        margin-top: u(16);
        margin-bottom: u(24);

        &:focus {
          border: u(2) solid mat-color($primary);
        }
      }

      &-text {
        margin-top: u(16);
        margin-bottom: u(24);
        color: #2f2f2f;
        font-size: u(28);
        line-height: u(28);
      }
    }
  }
}

@mixin mp-crown-order-shuttle-buttons-theme($theme) {
  $primary: map-get($lucky-theme, primary);
  .mp-crown-order-shuttle {
    &__buttons {
      flex-shrink: 0;

      height: u(96);
      width: 100%;

      text-align: center;
      line-height: u(96);
      color: #fff;
      font-size: u(36);
      font-weight: 500;

      background-color: mat-color($primary);
      border-radius: 0;

      &[class][disabled] {
        background-color: #e3e3e3;
        color: #fff;
      }
    }
  }
}
@mixin mp-crown-order-shuttle-icon() {
  .mp-crown-order-shuttle-icon {
    &-right-arrow {
      font-size: 18rpx;
      font-weight: bold;
      color: #b0b0b0;
    }
  }

}


@include mp-crown-order-shuttle-icon();
@include mp-crown-order-shuttle-theme($lucky-theme);
@include mp-crown-util($lucky-theme);
@include mp-crown-icon($lucky-theme);


