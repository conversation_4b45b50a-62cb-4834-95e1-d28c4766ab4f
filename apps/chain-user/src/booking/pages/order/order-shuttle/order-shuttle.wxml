<form bind:submit="onSubmit">
  <view class="mp-crown-order-shuttle__container">
    <view class="mp-crown-order-shuttle__header">
      营业期间较为忙碌，接送宠物可能不及时，尽请见谅
    </view>
    <view class="mp-crown-order-shuttle__body">
      <view class="mp-crown-order-shuttle__card">
        <view class="mp-crown-order-shuttle__service">
          开始服务: {{serviceStart}}  结束服务: 预计{{serviceEnd}}
        </view>
        <view class="mp-crown-order-shuttle__item mp-crown-order-shuttle__item-title">
          <form-check bind:tap="onReceiveTap" checked="{{receiveTime !== null}}"></form-check>
          <view class="mp-crown-order-shuttle__item-desc">上门接宠服务</view>
          <picker
            mode="time"
            disabled="{{!isCreate}}"
            value="{{receiveTime}}"
            start="{{receiveStart}}"
            end="{{receiveEnd}}"
            bind:change="onReceiveTimeChange"
          >
            <text class="mp-crown-order-shuttle__item-time" wx:if="{{receiveTime}}">{{receiveTime}}</text>
            <text class="mp-crown-order-shuttle__item-time--no-data" wx:if="{{receiveTime === ''}}">请选择时间</text>
          </picker>
          <text class="PETKIT icon-right-arrow mp-crown-order-shuttle-icon-right-arrow" wx:if="{{receiveTime !== null}}"></text>
          <!-- <image src="/images/home/<USER>" class="mp-crown-icon__drop-right" wx:if="{{receiveTime !== null}}"></image> -->
        </view>
        <view class="mp-crown-divider"></view>
        <view class="mp-crown-order-shuttle__item mp-crown-order-shuttle__item-title mp-crown-order-shuttle__item-title-send">
          <form-check bind:tap="onSendTap" checked="{{sendTime !== null}}"></form-check>
          <view class="mp-crown-order-shuttle__item-desc">服务后送宠到家</view>
          <picker
            mode="time"
            disabled="{{!isCreate}}"
            value="{{sendTime}}"
            start="{{sendStart}}"
            end="{{sendEnd}}"
            bind:change="onSendTimeChange"
          >
            <text class="mp-crown-order-shuttle__item-time" wx:if="{{sendTime}}">{{sendTime}}</text>
            <text class="mp-crown-order-shuttle__item-time--no-data" wx:if="{{sendTime === ''}}">请选择时间</text>
          </picker>
          <text class="PETKIT icon-right-arrow mp-crown-order-shuttle-icon-right-arrow" wx:if="{{sendTime !== null}}"></text>
          <!-- <image src="/images/home/<USER>" class="mp-crown-icon__drop-right" wx:if="{{sendTime !== null}}"></image> -->
        </view>
        <view class="mp-crown-order-shuttle__item-text">洗护接宠与送宠到家，默认同一地址</view>
      </view>
      <view class="mp-crown-order-shuttle__card" wx:if="{{isCreate}}">
        <view class="mp-crown-order-shuttle__community-container">
          <view class="mp-crown-order-shuttle__community-title">
            请选择<text class="mp-crown-order-shuttle__community-store">{{storeName}}</text>附近小区
          </view>
          <view class="mp-crown-order-shuttle__item-text">当前仅支持以下小区接送服务, 其他小区敬请期待</view>
        </view>
        <block wx:for="{{community}}" wx:key="index">
          <view class="mp-crown-divider"></view>
          <view class="mp-crown-order-shuttle__item" bind:tap="onCommunityTap" data-item="{{item}}">
            <form-check checked="{{communityName === item.name}}"></form-check>
            <view class="mp-crown-order-shuttle__item-community">{{item.name}}</view>
          </view>
        </block>
      </view>
      <view class="mp-crown-order-shuttle__card" wx:if="{{isCreate}}">
        <view class="mp-crown-order-shuttle__address">详细地址 (具体到几号栋/楼层/房号)</view>
        <textarea
          class="mp-crown-order-shuttle__address-textarea"
          placeholder="如: 12号栋3楼401"
          value="{{address}}"
          bind:blur="onAddressBlur"
          name="address"
        >
        </textarea>
      </view>
      <view class="mp-crown-order-shuttle__card" wx:if="{{!isCreate}}">
        <view class="mp-crown-order-shuttle__address">接送地址</view>
        <view class="mp-crown-order-shuttle__address-text">{{communityName + address}}</view>
      </view>
    </view>
    <button
      class="mp-crown-order-shuttle__buttons"
      wx:if="{{isCreate}}"
      disabled="{{(receiveTime === '' || sendTime === '') || !communityName || !address}}"
      form-type="submit"
    >确认</button>
  </view>
</form>

