import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';
import moment from 'moment';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    isCreate: true,
  },

  onReceiveTap() {
    // 不勾选时，receiveTime为null
    // 勾选时，receiveTime为''
    if (this.data.isCreate) {
      if (this.data.receiveTime !== null) {
        this.setData({
          receiveTime: null,
        });
      } else {
        this.setData({
          receiveTime: '',
        });

      }
    }
  },

  onReceiveTimeChange({
    detail: {
      value,
    }
  }) {
    this.setData({
      receiveTime: value || '',
    });
  },

  onSendTap() {
    // 不勾选时，receiveTime为null
    // 勾选时，receiveTime为''
    if (this.data.isCreate) {
      if (this.data.sendTime !== null) {
        this.setData({
          sendTime: null,
        });
      } else {
        this.setData({
          sendTime: '',
        });
      }
    }
  },

  onSendTimeChange({
    detail: {
      value,
    }
  }) {
    this.setData({
      sendTime: value || '',
    });
  },

  onCommunityTap(e) {
    const dataset = e.currentTarget.dataset;
    const {
      item,
    } = dataset;
    if (item) {
      this.setData({
        communityName: item.name,
      });
    }
  },

  onAddressBlur({
    detail: {
      value,
    }
  }) {
    this.setData({
      address: value,
    });
  },

  onSubmit({
    detail: {
      value: {
        address,
      }
    }
  }) {
    const transfer = {
      dayTime: this.data.dayTime,
      receiveTime: this.data.receiveTime,
      sendTime: this.data.sendTime,
      communityName: this.data.communityName,
      address: address,
    };

    this.saveServiceBookingTransfer(transfer);
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const id = Number(options.id);
    if (!isNaN(id) && id !== 0) {
      this.setData({
        isCreate: false,
      });

      wx.setNavigationBarTitle({
        title: '接送详情',
      });
    }

    const selectedDay = this.data.selectedPets[0].days[0].day;
    const selectedDays = selectedDay.split('.');
    const selectedAccuracyTimes = this.data.selectedPets[0].days[0].year + '-' + selectedDays[0] + '-' + selectedDays[1] + ' ' + this.data.selectedPets[0].times[0].time;
    const selectedAccuracyTimesStart = moment(selectedAccuracyTimes).subtract(20, 'm').format('HH:mm');
    this.setData ({
      receiveTime: selectedAccuracyTimesStart
    });
  },
};

const mapStateToData = state => {
  const businessStartDate = state.wp.store.warehouse.warehouseById.businessStartDate;
  const dayTime = state.service.booking.serviceBooking.transfer.dayTime || '';
  const selectedPets = state.servicePackage.booking.selectedPets;
  const receiveStart = businessStartDate ? moment(`${dayTime} ${businessStartDate}`).subtract(20, 'm').format('HH:mm') : '';
  const serviceStart = (selectedPets[0] && selectedPets[0].times[0].time) || state.service.booking.serviceBooking.transfer.serviceStart;
return {
    community: state.wp.store.warehouse.warehouseById.shuttleServiceCommunityList || [],
    storeName: state.wp.store.warehouse.warehouseById.warehouseName || '',
    receiveTime: state.service.booking.serviceBooking.transfer.receiveTime || null,
    sendTime: state.service.booking.serviceBooking.transfer.sendTime || null,
    communityName: state.service.booking.serviceBooking.transfer.communityName,
    address: state.service.booking.serviceBooking.transfer.address,
    dayTime,
    receiveStart,
    selectedPets,
    receiveEnd: serviceStart || '', // 接送宠物时间默认晚于预约时间
    sendStart: state.service.booking.serviceBooking.transfer.sendStart || '',
    sendEnd: '23:59',
    serviceStart,
    serviceEnd: (selectedPets[0] && selectedPets[0].times[0].endTime) || state.service.booking.serviceBooking.transfer.serviceEnd,
  };
};

const mapDispatchToPage = dispatch => bindActionCreators({
  saveServiceBookingTransfer: acs.service.booking.saveServiceBookingTransfer,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
