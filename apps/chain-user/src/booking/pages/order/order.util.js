export function extractPetsParam(pets) {
  return pets.map(pet => {
    const petId = pet.pet.id;
    const {
      day,
      year,
    } = (pet.days[0] || {});
    const time = (pet.times[0] || {}).time;

    return {
      petId,
      servicePackageSkuId: pet.packageSkus.length ? pet.packageSkus[0].id : 0,
      serviceSingleSkuIds: pet.singleSkus.length ? pet.singleSkus.map(item => item.id) : [],
      day: `${year}.${day}`,
      time,
    };
  });
}
