import {
  connect,
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
  CONSTANT
} from '@petkit/redux';

const pageConfig = {
  /**
   * 页面的初始数据
   */
  data: {
    selectCoupon: '-1', // 默认选择的优惠券

    type: '', // 类型online 线上立减 coupon 优惠
    showUse: true, // 去使用
    detail: [],
    showUseDescription: true,
    isTheHotZoneTheLargest: true,
  },

  onNoSelectTap({
    currentTarget: {
      dataset: {
        id,
        type,
      }
    }
  }) {
    this.onUpdateCoupon(id, type);
  },
  onSendTap({detail: {data: {userCouponId,_isExpired}}}) {
    this.onUpdateCoupon(userCouponId, _isExpired);
  },

  onUpdateCoupon(id, type) {
    if (id !== '-1' && type === true) { // 失效 直接返回
      return;
    }
    this.setData({
      selectCoupon: id,
    });

    let data = {};
    if (id === '-1') {
      data.isUseCoupon = false; // 自定义字段 不使用
      data.selectedCoupon = null;
      data.type = this.data.type;
      this.data.detail.map(coupon => {
        coupon.isSelected = false;
      });
    } else {
      let selectedCoupon = this.data.detail.find(item => id === item.userCouponId);
      this.data.detail.map(item => item.isSelected = item === selectedCoupon);

      data.isUseCoupon = true;
      data.selectedCoupon = selectedCoupon;
      data.type = this.data.type;
    }
    wx.navigateBack();
    this.selectBookingCouponList(data);

    if (this.data.type === 'online') {
      this.resetCouponList({
        type: 'coupon',
      });
    }
  },

  onShow() {
    let pages = getCurrentPages();
    let currentPage = pages[pages.length - 1];
    const type = currentPage.options.type === 'online' ? 'online' : 'coupon';
    this.setData({
      type,
    });
    // 初始化未优惠券
    let selectCoupon = '-1';
    const detail = this.data.type === 'coupon' ? this.data.bookingCouponList.coupons || [] : this.data.bookingCouponList.serviceCoupons || [];
    detail.forEach(coupon => {
      if (coupon.isSelected) {
        selectCoupon = coupon.userCouponId;
        // hanlder里面计算金额存在改为失效的情况，此处强行改为有效
        coupon._isExpired = false;
      }
    });
    this.setData({
      detail,
      selectCoupon,
    });
    wx.setNavigationBarTitle({
      title: this.data.type === 'coupon' ? '使用优惠券' : '使用线上优惠',
    });
  },
  onWatchMore({detail: {id}}) {
    // 跳转详情
    // 失效的不跳转详情 v2等产品处理
    const couponIsAvaiable = this.data.detail.find(item => item.userCouponId === id && !item._isExpired);
    if (!couponIsAvaiable) return;

    this.selectedCoupon({
      id,
      couponList: this.data.detail,
      type: CONSTANT.CAMPAIGN.COUPON_PAGE_TYPES.SERVICE_COUPON,
    })
    wx.navigateTo({
      url: '/coupon/pages/coupon-detail/coupon-detail'
    });
  }
};

const mapStateToData = state => ({
  bookingCouponList: state.service.bookingCouponList,
    currencySymbol: state.wp.store.warehouse.warehouseById.currencySymbol || '￥',
});

const mapDispatchToPage = dispatch => bindActionCreators({
  selectBookingCouponList: acs.service.bookingCouponList.selectBookingCouponList,
  resetCouponList: acs.service.bookingCouponList.resetCouponList,
  selectedCoupon: acs.campaign.moreCoupon.selectedCoupon,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
