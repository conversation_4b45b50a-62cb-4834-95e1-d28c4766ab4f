// 此样式文件用于 shopping/order-create/order-create.scss 文件
@mixin mp-coupon-card-other-theme ($theme){
  .mp-coupon-card__title {
    font-size: u(28);
    color: #2F2F2F;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: u(48);
    line-height: u(48);
    padding: u(12) 0;
    background-color: #fff;
    margin: u(30);
    border-radius: u(8);
    padding-right: u(20);
    border: u(2) solid rgba(241, 241, 241, 1);
    padding-left: u(24);
    &__font {
      flex: 1;
    }
  }
  .mp-coupon-card__main-title-form-check {
    display:flex;
    flex-direction:row;
    align-items:center;
  }
}
