import {acs} from "@petkit/redux";
import {memorize} from "utils";

import {bindActionCreators} from "redux";
import {connect} from "utils/weapp-redux";
import {extractPetsParam} from "./order.util";

const pageConfig = {
    /**
     * 页面的初始数据
     */
    data: {
        id: 0,
        isShuttle: true,
        remark: "",
        showDialog: false, // 选择支付方式 弹框切换的内容 默认false
        selectPaymentType: "VIP", // 选择支付类型 默认用户支付'WX' VIP为用户余额支付
        isOnlinePay: true, // 是否线上支付  到店支付
        isUseVipDiscount: true,
        // 是否显示积分说明，默认不显示
        isShowVipPointsExplain: false,
        activeDeduction: "",
        integralShow: false,
        getRules: [
            "1、每完成一笔消费，按照支付金额获取相应积分。每消费1元，获取1积分，门店、商城消费均可获得积分。",
        ],
        useRules: [
            "1、积分数量>=100，可使用积分抵扣支付金额。（仅限线上商城使用）",
            "2、每100积分可抵扣1元现金。",
            "3、每笔订单有积分抵扣上限。",
            "4、若出现退单情况，使用积分将退还，该订单赠送的积分将扣除。",
            "5、积分使用可与优惠活动同享。",
        ],
        // 是否显示会员专享折扣，默认不显示
        isVipDiscountPopup: false,
        use: true,
        notuse: false,
        isShowPayLoading: false,
        starPosRequestTimes: 0,
        starPosRequestTimer: 0,
        isContactDialog: false,
        isUseStorePoints: false,
        storePointsRadio:'0',
        isShowStorePointsPopup: false,
    },
    onUnload() {
        memorize.clear("order");
        memorize.clear("booking-coupon-list");
        // 清兑换券
        this.updateActiveBookingDeduction({});
    },
    onReady() {
        let timer;

        const initPaymentType = () => {
            const vipPrice = this.data.couponlist.vipPrice;
            const bookingPrice = this.data.bookingPrice;
            const productPackageId = this.data.productPackageId;

            if (vipPrice !== undefined) {
                clearInterval(timer);
                // this.setData({
                //     selectPaymentType: "VIP",
                // });
                if (!this.data.id || this.data.id.length === 0) {
                    const pets = extractPetsParam(this.data.pets);
                    const storeId = this.data.storeInfo.id;
                    const paymentSystem = "BALANCE";
                    let isUseVipDiscount = this.data.isUseVipDiscount;

                    this.requestBookingCouponList(
                        storeId,
                        pets,
                        paymentSystem,
                        bookingPrice,
                        productPackageId,
                        isUseVipDiscount
                    );
                }
            }
        };

        timer = setInterval(initPaymentType, 200);

        if (this.data.id === 0) {
            wx.setNavigationBarTitle({
                title: "确认订单",
            });
        }

    },

    descriptionShow() {
        this.setData({
            integralShow: !this.data.integralShow,
        });
    },

    closeIntegralPopup() {
        this.setData({
            integralShow: !this.data.integralShow,
        });
    },
    // 取消订单
    cancelOrder() {
        const bookingId = this.data.id;
        const that = this;
        wx.showModal({
            title: "",
            content: "是否确定取消该预约的订单？",
            cancelColor: "#2034B5",
            confirmColor: "#2034B5",
            success(res) {
                if (res.confirm) {
                    that.requestCancelBookingOrder(
                        {bookingId: bookingId},
                        {
                            success: () => {
                                wx.navigateBack();
                            },
                        }
                    );
                }
            },
        });
    },

    // 取消已预约的订单
    cancelBookedOrder() {
        const bookingId = this.data.id;
        const that = this;

        wx.showModal({
            title: "",
            content: "是否确定取消该预约的订单？",
            cancelColor: "#2034B5",
            confirmColor: "#2034B5",
            success(res) {
                if (res.confirm) {
                    that.requestCancelBookedOrder(
                        {bookingId},
                        {
                            success: () => {
                                wx.navigateBack();
                            },
                        }
                    );
                }
            },
        });
    },

    // 立即支付
    immediatePay() {
        const that = this;
        const bookingId = this.data.id;
        this.requestRepayData(
            {bookingId},
            {
                success: () => {
                    if (this.data.repayObject) {
                        that._payAgain(this.data.repayObject);
                    }
                },
            }
        );
    },

    _payAgain(repayObject) {
        const packageString = repayObject.packageString;
        const params = JSON.parse(packageString);
        const timeStamp = params.timeStamp; //时间戳，自1970年以来的秒数
        const nonceStr = params.nonceStr; //随机串
        const packageInfo = params.package;
        const signType = params.signType; //微信签名方式:
        const paySign = params.paySign;

        const logNo = params.logNo;
        const storeId = (this.data.storeInfo && this.data.storeInfo.id) || 0;
        const orderId = repayObject.orderId;

        wx.requestPayment({
            timeStamp,
            nonceStr,
            package: packageInfo,
            signType,
            paySign,
            success: () => {
                if (logNo) {
                    this.onGetPosOrderResult({orderId, logNo, storeId});
                } else {
                    wx.navigateBack();
                }
            },
            fail: (error) => {
                wx.reportEvent("payment_error",{
                    "wxdata_perf_error_code": 0,
                    "wxdata_perf_error_msg": typeof error === "object" ? JSON.stringify(error) : error,
                    "wxdata_perf_extra_info1": "apps/chain-user/src/booking/pages/order/order.js _payAgain",
                    "wxdata_perf_extra_info2": JSON.stringify(params),
                    "wxdata_perf_extra_info3": ""
                })
                this.showToast({
                    message: "支付失败~",
                });
                this.resetServiceBooking();
                this.resetServiceBookingTransfer();
            },
        });
    },

    onLoad(options) {
        let pages = getCurrentPages();
        let currentPage = pages[pages.length - 1];
        let option = currentPage.options;
        let id = option.id ? option.id : 0;
        let bookingPrice = options.bookingPrice;
        let productPackageId = options.productPackageId;
        let isUseVipDiscount = this.data.isUseVipDiscount;
        if (id && id.length) {
            this.requestBookingListDetail(id, {
                success: () => {
                    this.getOrderConcatInfo(id);
                },
            });
            wx.setNavigationBarTitle({
                title: "订单详情",
            });
        } else {
            this.getOrderConcatInfo(id);
            wx.setNavigationBarTitle({
                title: this.data.storeInfo.name + "预约",
            });
            // 获取可使用优惠列表
            const pets = extractPetsParam(this.data.pets);
            const storeId = this.data.storeInfo.id;
            const paymentSystem =
                this.data.selectPaymentType === "WX" ? "WECHAT" : "BALANCE";
            if (paymentSystem === "WECHAT" && this.data.selectPaymentType === "WX") {
                isUseVipDiscount = false;
            }

            /**
             *  存在有余额但是不是会员的情况，这种情况下如果用户有满足使用条件的优惠券的话是无法使用的， 该判断解决了！！
             * **/
            if (!this.data.isUserVip && paymentSystem === "BALANCE") {
                isUseVipDiscount = false;
                this.setData({
                    use: false,
                    notuse: true,
                });
            }

            this.requestBookingCouponList(
                storeId,
                pets,
                paymentSystem,
                bookingPrice,
                productPackageId,
                isUseVipDiscount,
            );
        }
        this.requestAdvancePaymentConfig({storeId: this.data.storeInfo.id || 0}, {
            success: () => {
                const bookingPaymentTypes = this.data.bookingPaymentTypes

                if (bookingPaymentTypes.WECHAT === false) {
                    // 此处需要将支付方式默认指向到店支付
                    this.setData({
                        isOnlinePay: false,
                    });
                }
                console.log({bookingPaymentTypes})
            }
        })
        // iPhone系列适配
        let isIphone = getApp().globalData.isIphone;

        this.setData({
            isIphone: isIphone,
            id: id,
            bookingPrice: bookingPrice,
            productPackageId: productPackageId,
        });
    },

    onRemarkInput({detail: {value}}) {
        this.setData({
            remark: value,
        });
    },

    onConfirmTap() {
        // TODO: http request

        // 防止两次点击间隔太快
        const nowTime = new Date();
        if (nowTime - this.data.tapTime < 2000) {
            return;
        }

        let points = 0;
        let isUseVipDiscount = this.data.isUseVipDiscount || this.data.use;
        let isUseStorePoints = this.data.isUseStorePoints;
        if (this.data.isOnlinePay) {
            points =
                this.data.selectPaymentType === "WX"
                    ? this.data.totalPoints
                    : this.data.vipPoints;
        } else {
            points = this.data.offlinePoints;
        }
        let saveTotalPrice;

        if (this.data.isOnlinePay) {
            // 未使用任何优惠 微信支付
            if (
                !this.data.couponlist.isUseOnlineCoupon &&
                !this.data.couponlist.isUseCoupon &&
                !this.data.couponlist.isUsePrepurchase &&
                this.data.selectPaymentType === "WX"
            ) {
                if (this.data.isUseStorePoints) {
                    saveTotalPrice = this.data.totalPrice;
                } else {
                    saveTotalPrice = this.data.couponlist.totalPrice;
                }
            } else if (
                this.data.couponlist.isUseOnlineCoupon ||
                this.data.couponlist.isUseCoupon ||
                this.data.couponlist.isUsePrepurchase ||
                this.data.selectPaymentType !== "WX"
            ) {
                if( isUseStorePoints && isUseVipDiscount){
                    saveTotalPrice = this.data.totalPriceWithVip;
                }else if(isUseStorePoints && !isUseVipDiscount) {
                    saveTotalPrice = this.data.pointPrice;
                }else {
                    saveTotalPrice = isUseVipDiscount
                    ? this.data.couponlist.vipPrice
                    : this.data.couponlist.totalPrice;
                }

            }
        } else {
            if (this.data.bookingPaymentTypes.STORE_NO_DEPOSIT) {
                saveTotalPrice = 0;
            } else {
                saveTotalPrice = 5;
            }

        }

        // 处理情况：用户先选择时间，再选择单项服务，这时时间time被清空，但数据还未返回，就进入了确认订单页面进行下单，这时并没有拿到time，导致页面报错
        const isSelectedTime =
            this.data.selectedPets && this.data.selectedPets.length > 0
                ? this.data.selectedPets[0].times &&
                this.data.selectedPets[0].times.length > 0
                    ? this.data.selectedPets[0].times[0].time
                    : ""
                : "";
        if (!isSelectedTime) {
            this.showToast({
                message: "服务时长延迟，请再次确认",
                duration: 1500,
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 2000);
            return;
        }
        // @TODO 优化支付逻辑判断
        // if (
        //     this.data.paymentSystem === "WECHAT" &&
        //     this.data.selectPaymentType === "WX"
        // ) {
        //     isUseVipDiscount = false;
        // }
        const submitData = {
            finalPaymentPrice: Number(saveTotalPrice),
            productPackageId: this.data.isOnlinePay
                ? this.data.productPackageId
                    ? this.data.productPackageId
                    : 0
                : 0,
            remark: this.data.remark,
            bookingSourceEnum: this.data.isOnlinePay ? "WECHAT" : this.data.bookingPaymentTypes.STORE_NO_DEPOSIT ? 'STORE_NO_DEPOSIT' : "STORE",
            points: +this.data.storePointsRadio,
            isUseVipDiscount:this.data.isUseVipDiscount,
        }
        if (this.data.activeBookingBalanceCard.cardType === 'BENEFIT_CARD'){
            submitData.benefitCardId = this.data.activeBookingBalanceCard.cardId;
            submitData.isUseVipDiscount = true;
            submitData.paymentSystem = 'WECHAT'
        }else if(this.data.activeBookingBalanceCard.cardType === 'CHARGE'){
            submitData.userBalanceId = this.data.activeBookingBalanceCard.cardId;
            submitData.isUseVipDiscount = true;
            submitData.paymentSystem = 'BALANCE';
        }else {
            submitData.paymentSystem = 'WECHAT';
            submitData.userBalanceId = 0
        }

        this.requestConfirmAc(
            {
                // benefitCardId: this.data.activeBookingBalanceCard.cardType === 'BENEFIT_CARD' ? this.data.activeBookingBalanceCard.cardId : undefined,
                // finalPaymentPrice: Number(saveTotalPrice),
                // productPackageId: this.data.isOnlinePay
                //     ? this.data.productPackageId
                //         ? this.data.productPackageId
                //         : 0
                //     : 0,
                // remark: this.data.remark,
                // bookingSourceEnum: this.data.isOnlinePay ? "WECHAT" : this.data.bookingPaymentTypes.STORE_NO_DEPOSIT ? 'STORE_NO_DEPOSIT' : "STORE",
                // paymentSystem: this.data.activeBookingBalanceCard.cardType === 'BENEFIT_CARD' ? "WECHAT" : this.data.paymentSystem,
                /**
                 * points 是原本会员积分的使用字段
                 */
                // points: this.data.isUseVipPoints ? points : 0,
                // points: +this.data.storePointsRadio,
                // isUseVipDiscount: isUseVipDiscount,
                // userBalanceId:
                //     this.data.paymentSystem === "BALANCE"
                //         ? this.data.activeBookingBalanceCard.cardId
                //         : 0,
                ...submitData
            },
            {
                success: () => {
                    const bookingOrderResult = this.data.bookingOrderResult;
                    if (bookingOrderResult) {
                        switch (bookingOrderResult.code) {
                            case 0:
                                const earnPoints = this.data.earnPoints;
                                const medicalPoints = this.data.medicalPoints;
                                const param = {
                                    earnPoints,
                                    amount: saveTotalPrice,
                                    medicalPoints,
                                };
                                // 当用户选择微信支付是，只判断selectPaymentType是否为VIP时，似乎会出现结果为true的情况
                                // 此处加上BALANCE用以严格判断其用户是否真的使用用户余额来进行支付的情况
                                if (
                                    this.data.selectPaymentType === "VIP" &&
                                    this.data.paymentSystem === "BALANCE"
                                ) {
                                    // 会员余额支付
                                    setTimeout(() => {
                                        wx.reLaunch({
                                            url:
                                                "/pages/order-status/order-status?param=" +
                                                JSON.stringify(param),
                                        });
                                    }, 200);
                                    this.resetServiceBooking();
                                    this.resetServiceBookingTransfer();
                                } else {
                                    // 修复微信支付 0 元 || 添加条件当不使用预约金 并且到店支付的时候  订单预约金为0
                                    if (this.data.wxPay === "" || (this.data.bookingPaymentTypes.STORE_NO_DEPOSIT && !this.data.isOnlinePay)) {
                                        // 这个地方取totalPrice  有问题
                                        param.amount = "0.00"; // 微信支付0元,积分肯定大于等于500， 支付金额为0 ,手动设置， 跳转后的金额0.00
                                        setTimeout(() => {
                                            wx.reLaunch({
                                                url:
                                                    "/pages/order-status/order-status?param=" +
                                                    JSON.stringify(param),
                                            });
                                        }, 200);
                                        this.resetServiceBooking();
                                        this.resetServiceBookingTransfer();
                                    } else if (this.data.wxPay && this.data.wxPay.timeStamp) {
                                        this._payBooking();
                                    } else {
                                        memorize.clear("service-package");
                                        this.requestDateRange();
                                    }
                                }
                                break;
                            case -2:
                                wx.showModal({
                                    title: "警告",
                                    content: bookingOrderResult.message || "",
                                    showCancel: false,
                                    success: (res) => {
                                        if (res.confirm) {
                                            wx.redirectTo({
                                                url: "/booking/pages/booking/list/list?type=0",
                                            });
                                        }
                                    },
                                });
                                break;
                            case -1:
                                this.showToast({
                                    message: bookingOrderResult.message || "",
                                    duration: 1500,
                                });
                                break;
                            default:
                                this.showToast({
                                    message: bookingOrderResult.message || "",
                                    duration: 1500,
                                });
                                break;
                        }
                    }
                },
            }
        );

        this.setData({
            tapTime: nowTime,
        });
    },

    onUncheckShuttleTap() {
        this.resetServiceBookingTransfer();
    },

    onShuttleTap() {
        wx.navigateTo({
            url: `/booking/pages/order/order-shuttle/order-shuttle?id=${this.data.id}`,
        });
    },
    onPrepurchaseTap() {
        wx.navigateTo({
            url: `/booking/pages/order/order-prepurchase/order-prepurchase?id=${this.data.id}&type=prepurchase`,
        });
    },

    onCouponTap() {
        wx.navigateTo({
            url: `/booking/pages/order/order-coupon/order-coupon?id=${this.data.id}&type=coupon`,
        });
    },

    onCouponOnLineTap() {
        wx.navigateTo({
            url: `/booking/pages/order/order-coupon/order-coupon?id=${this.data.id}&type=online`,
        });
    },

    getPayTotalPrice() {
        let totalPrice = this.data.couponlist.totalPrice;
        if (this.data.isUseStorePoints) {
            totalPrice = this.data.totalPrice;
        }

        if (!this.data.isOnlinePay) {
            totalPrice = this.data.bookingPaymentTypes.STORE_NO_DEPOSIT ? 0 : 5;
            if (this.data.isUseStorePoints) {
                totalPrice = this.data.offlinePrice;
            }
        }

        return totalPrice;
    },

    starPosPaySuccess() {
        // 订单页面支付成功 不是详情立即支付
        if (this.data.bookingOrderId && !this.data.id) {
            const totalPrice = this.getPayTotalPrice();
            const param = {
                earnPoints: this.data.earnPoints,
                amount: totalPrice,
            };
            const url =
                "/pages/order-status/order-status?param=" + JSON.stringify(param);
            wx.getSystemInfo({
                success: function (res) {
                    if (res.platform == "ios") {
                        wx.reLaunch({
                            url,
                        });
                    } else {
                        wx.redirectTo({
                            url,
                        });
                    }
                },
            });
        } else {
            wx.navigateBack();
        }
    },

    onGetPosOrderResult({orderId, logNo, storeId}) {
        if (this.data.starPosRequestTimes < 20) {
            this.requestPosOrderResult(
                {orderId, logNo, storeId},
                {
                    success: () => {
                        // 星pos支付：S-交易成功 F-交易失败 A-等待授权 Z-交易未知
                        const posOrderResult = this.data.posOrderResult;
                        // debugger;
                        if (posOrderResult && posOrderResult.message) {
                            switch (posOrderResult.message.result) {
                                case "S":
                                    this.getPosOrderStatus(false);
                                    this.starPosPaySuccess();

                                    this.resetServiceBooking();
                                    this.resetServiceBookingTransfer();
                                    break;
                                case "A":
                                    this.getPosOrderStatus(true);
                                    this.data.starPosRequestTimer = setTimeout(() => {
                                        this.data.starPosRequestTimes++;
                                        this.onGetPosOrderResult({orderId, logNo, storeId});
                                    }, 400);
                                    break;
                                case "F":
                                    this.getPosOrderStatus(false);
                                    this._requestPosOrderRefund(orderId, logNo, storeId);
                                    break;
                                default:
                                    this.getPosOrderStatus(false);
                                    wx.redirectTo({
                                        url: "/pages/order-status-pos/order-status-pos?type=service",
                                    });
                                    break;
                            }
                        }
                    },
                    fail: () => {
                        this.showToast({
                            message: "支付失败",
                            duration: 1500,
                        });
                    },
                }
            );
        } else {
            this.getPosOrderStatus(false);
            // 超过次数
            this._requestPosOrderRefund(orderId, logNo, storeId);
        }
    },

    // 星pos退单
    _requestPosOrderRefund(orderId, logNo, storeId) {
        this.resetServiceBooking();
        this.resetServiceBookingTransfer();

        this.starPosRequestTimer && clearTimeout(this.starPosRequestTimer);
        this.showToast({
            message: "支付失败~",
        });
        // 超过次数
        this.requestPosOrderRefund({orderId, logNo, storeId});
        setTimeout(() => {
            wx.redirectTo({
                url: "/pages/order-status-pos/order-status-pos?type=mall",
            });
        }, 200);
    },

    _payBooking() {
        const payPackage = this.data.wxPay;
        const timeStamp = payPackage.timeStamp; //时间戳，自1970年以来的秒数
        const nonceStr = payPackage.nonceStr; //随机串
        const signType = payPackage.signType; //微信签名方式:
        const paySign = payPackage.paySign;
        const packageInfo = payPackage.package
            ? payPackage.package
            : "prepay_id=" + payPackage.prepayId;
        const logNo = payPackage.logNo;
        const storeId = (this.data.storeInfo && this.data.storeInfo.id) || 0;
        const orderId = this.data.bookingOrderId;

        wx.requestPayment({
            timeStamp,
            nonceStr,
            signType,
            paySign,
            package: packageInfo,
            success: () => {
                const earnPoints = this.data.earnPoints;
                const medicalPoints = this.data.medicalPoints;
                let totalPrice = this.data.couponlist.totalPrice;
                if (this.data.isUseStorePoints) {
                    totalPrice = this.data.totalPrice;
                }
                if(this.data.isUseVipDiscount && this.data.isUseStorePoints){
                    totalPrice = this.data.pointPrice;
                }
                // 在不使用预约金时 预约订单金额为0 不会调起微信支付
                if (!this.data.isOnlinePay) {
                    totalPrice = 5;
                    if (this.data.isUseStorePoints) {
                        totalPrice = this.data.offlinePrice;
                    }
                }

                if (logNo) {
                    this.onGetPosOrderResult({orderId, logNo, storeId});
                } else {
                    const param = {
                        earnPoints,
                        amount: totalPrice,
                        medicalPoints,
                    };
                    const url =
                        "/pages/order-status/order-status?param=" + JSON.stringify(param);
                    wx.redirectTo({
                        url,
                    });
                }
                this.resetServiceBooking();
                this.resetServiceBookingTransfer();
            },
            fail: (error) => {
                wx.reportEvent("payment_error",{
                    "wxdata_perf_error_code": 0,
                    "wxdata_perf_error_msg": typeof error === "object" ? JSON.stringify(error) : error,
                    "wxdata_perf_extra_info1": "apps/chain-user/src/booking/pages/order/order.js _payBooking",
                    "wxdata_perf_extra_info2": JSON.stringify({...payPackage,orderId}),
                    "wxdata_perf_extra_info3": ""
                })
                this.showToast({
                    message: "支付失败~",
                });
                this.resetServiceBooking();
                this.resetServiceBookingTransfer();
                this.redirectOrderDetail();
            },
        });
    },

    // 当前页面重定向到预约详情页面
    redirectOrderDetail() {
        const bookingId = this.data.bookingOrderResult.data.booking.id;
        setTimeout(() => {
            // 详情页面
            const url = encodeURIComponent(
                `/booking/pages/order/order?id=${bookingId}`
            );
            // 列表页面
            const dashboardRedirectUrl = encodeURIComponent(
                "/booking/pages/booking/list/list?redirectType=navigate&page=" + url
            );
            // 我的页面
            wx.reLaunch({
                url:
                    "/pages/dashboard/dashboard?redirectType=navigate&page=" +
                    dashboardRedirectUrl,
            });
        }, 200);
    },

    toggleDialog() {
        this.setData({
            showDialog: !this.data.showDialog,
        });
    },

    togglePaymentType(e) {
        // 是否使用会员优惠
        let isUseVipDiscount = this.data.isUseVipDiscount;
        // 当前选中的支付方式
        let selectPaymentType = e.currentTarget.dataset.type;
        // 当前点击的充值卡
        const card = e.currentTarget.dataset.card || {};
        // 使用会员优惠页面展示控制
        let use = this.data.use;
        let notuse = this.data.notuse;
        // 重复点击
        if (selectPaymentType === "VIP" && !card.selected) {
            return;
        }
        // selectPaymentType === "VIP" && this.changeBookingBalanceCard(card);
        this.changeBookingBalanceCard(card);

        const bookingPrice = this.data.bookingPrice;
        const productPackageId = this.data.productPackageId;

        memorize.clear("order");
        const pets = extractPetsParam(this.data.pets);
        const storeId = this.data.storeInfo.id;
        let paymentSystem = selectPaymentType === "WX" || (card.cardType === 'BENEFIT_CARD') ? "WECHAT" : "BALANCE";

        // 优惠互斥切换
        isUseVipDiscount = selectPaymentType === "VIP";
        use = selectPaymentType === "VIP";
        notuse = selectPaymentType === "WX";

        this.setData({
            isUseVipDiscount,
            selectPaymentType,
            use,
            notuse,
            showDialog: !this.data.showDialog,
        });
        this.requestBookingCouponList(
            storeId,
            pets,
            paymentSystem,
            bookingPrice,
            productPackageId,
            isUseVipDiscount,
            {
                complete: () => {
                    this.selectBookingCouponList({});
                },
            }
        );
    },

    selectIsOnlinePay(e) {
        let type = e.currentTarget.dataset.type;
        this.setData({
            isOnlinePay: type === "online",
        });
        this.saveIsOnlinePay(type === "online"); // redux层新增保存
        if (type !== "online") {
            if (Number(this.data.productPackageId)) {
                this.showToast({
                    message: "限时优惠只限小程序线上全额支付~",
                    duration: 3000,
                });
            }
        }
        // 不管线上支付还是到店支付均支持余额、微信支付
        const userBalanceOfDirect = this.data.userBalanceOfDirect;
        const userBalanceOfCurrentStore = this.data.userBalanceOfCurrentStore;
        const vipPrice = this.data.couponlist.vipPrice;
        if (
            Number(
                userBalanceOfCurrentStore && userBalanceOfCurrentStore.userBalance
            ) >= vipPrice ||
            Number(userBalanceOfDirect && userBalanceOfDirect.userBalance) >= vipPrice
        ) {
            this.setData({
                selectPaymentType: "VIP",
            });
        }
    },

    onSelectUseVipPointsTap() {
        this.setData({
            isUseStorePoints: !this.data.isUseStorePoints,
        });
    },

    onShowDescriptionTap() {
        this.setData({
            isShowVipPointsExplain: true,
        });
    },

    onToRechargeTap() {
        // 去充值
        wx.navigateTo({
            url: "/recharge/pages/member-recharge/member-recharge",
        });
    },

    onShowVipDiscountPopup() {
        this.setData({
            isVipDiscountPopup: true,
        });
    },

    onShowStorePointsPopup() {
        this.setData({
            isShowStorePointsPopup: true,
        });
    },
    onHiddenStorePointsPopup() {
        this.setData({
            isShowStorePointsPopup: false,
        });
    },
    onChangeStorePointsRadio(event) {
        // console.log('onChangeStorePointsRadio',event)
        // this.setData({
        //     isUseStorePoints: event.detail === "1",
        //     storePointsRadio: event.detail,
        // });
    },
    onClickStorePointsRadio(event) {
        console.log('onClickStorePointsRadio',event.currentTarget.dataset)
        const {name} = event.currentTarget.dataset;
        this.setData({
            isUseStorePoints: name === "1",
            storePointsRadio: name,
        });
    },

    onClose() {
        this.setData({
            isVipDiscountPopup: false,
        });
    },

    useClick(evt) {
        const use = evt.currentTarget.dataset.use;
        if (
            (use === "use" && this.data.use === true) ||
            (use === "notuse" && this.data.notuse === true)
        ) {
            return;
        }
        this.setData({
            use: !this.data.use,
            notuse: !this.data.notuse,
            isUseVipDiscount: !this.data.use,
        });
    },

    closeVipDiscountPopup() {
        this.setData({
            isVipDiscountPopup: false,
        });
        this.confirmIsUseVipDiscount(this.data.use);
    },

    // 联系客服
    onShowContactDialog() {
        this.setData({
            isContactDialog: true,
        });
    },

    getOrderConcatInfo(id) {
        // 洗护预约
        let warehouseContactInfo = this.data.contactInfo;
        // 查看详情
        if (id || this.data.id) {
            let warehouseLists = this.data.availableWarehouse;
            const orderStoreId = this.data.bookingListDetail.storeId;

            let warehouseById = warehouseLists.length
                ? warehouseLists.find((item) => item.id === orderStoreId) || {}
                : {};

            warehouseContactInfo = {
                mobile: warehouseById.mobile || "",
                start: warehouseById.businessStartDate || "",
                end: warehouseById.businessEndDate || "",
                telephone: warehouseById.telephone || "",
            };
        }
        this.setData({
            warehouseContactInfo,
        });
    },
};

const mapStateToData = (state) => {
    const isUserVip =
        state.user.user.user &&
        state.user.user.user.level &&
        state.user.user.user.level.id
            ? state.user.user.user.level.id
            : state.user.user.user.isStoreVip
                ? state.user.user.user.isStoreVip
                : false;
    const pets = state.servicePackage.booking.selectedPets;
    const wxPay = state.servicePackage.booking.wxPay;
    const serviceCoupons = state.service.bookingCouponList.serviceCoupons || [];
    const onlineCouponCount = serviceCoupons.filter(
        (coupon) => !coupon._isExpired
    ).length;

    // 使用积分后的最终价格
    const couponlist = state.service.bookingCouponList;
    const dashboard = state.wp.user.dashboard;
    const earnPoints = state.servicePackage.booking.earnPoints;
    couponlist.totalPrice = couponlist.totalPrice ? couponlist.totalPrice : 0;
    couponlist.vipPrice = couponlist.vipPrice ? couponlist.vipPrice : 0;
    couponlist.pointDeductionPrice = couponlist.pointDeductionPrice
        ? couponlist.pointDeductionPrice
        : 0;

    const totalPrice = Number(
        couponlist.totalPrice - couponlist.pointDeductionPrice
    ).toFixed(2);
    // 使用积分 + 选择使用会员
    const totalPriceWithVip = Number(
        couponlist.totalPrice -
        couponlist.pointDeductionPrice -
        couponlist.vipDiscountPrice
    ).toFixed(2);
    // 余额支付 使用积分 不使用优惠券或者vip折扣
    const pointPrice = couponlist.totalPrice - couponlist.pointDeductionPrice
    const vipPrice = Number(
        couponlist.vipPrice - couponlist.pointDeductionPrice
    ).toFixed(2);
    const offlinePrice = 100 > dashboard.points ? "0.00" : Number((4).toFixed(2));
    const totalPoints = couponlist.pointDeduction || 0;
    const vipPoints = couponlist.pointDeduction | 0;
    const offlinePoints = 100 > dashboard.points ? 0 : 100;
    // 节省的价格 -- 会员 + 非会员
    let savePriceMemberWithPoints = 0,
        savePriceMember = 0,
        savePriceNomember = 0,
        savePriceOfWX = 0;
    // 会员 - 使用积分
    if (isUserVip) {
        savePriceMemberWithPoints = couponlist.vipPrice
            ? Number(
                couponlist.originPrice - couponlist.vipPrice + vipPoints / 100
            ).toFixed(2)
            : 0.0;
        // 会员 - 不使用积分
        savePriceMember = couponlist.vipPrice
            ? Number(couponlist.originPrice - couponlist.vipPrice).toFixed(2)
            : 0.0;
    }

    // 微信支付节省的钱
    savePriceOfWX =
        couponlist.originPrice - totalPrice
            ? Number(couponlist.originPrice - totalPrice).toFixed(2)
            : 0.0;

  let isUseVipDiscount = !!state.service.bookingCouponList.isUseVipDiscount || true;

  const selectPaymentType = state.service.bookingCouponList.selectPaymentType;
   let use = isUseVipDiscount;
  let notuse = !isUseVipDiscount;
  // if (state.service.bookingCouponList.selectPaymentType === 'VIP'){
  //     // isUseVipDiscount = true
  //     use = selectPaymentType === "VIP";
  //     notuse = selectPaymentType === "WX";
  // }

    return {
        registerName: state.user.user.registerName || "",
        userMobile: state.user.user.user.mobile || "",
        pets,
        wxPay,
        // 优惠券相关
        onlineCouponCount,
        dashboard: state.wp.user.dashboard, // 用户余额
        couponlist: state.service.bookingCouponList,
        storeInfo: state.wp.store.warehouse.storeInfo,
        bookingListDetail: state.service.bookingList.bookingListDetail,
        state: state.service.booking.serviceBooking,
        payPackage: state.service.booking.payPackage,
        bookingId: state.service.booking.bookingId,
        repayObject: state.service.booking.repayObject,
        bookingPaymentTypes: state.service.booking.bookingPaymentTypes,

        hasShuttleService:
            state.wp.store.warehouse.warehouseById.hasShuttleService || 0,
        usePrepurchaseNum: state.service.bookingCouponList.usePrepurchaseNum || 0,
        selectedCoupon:
            (state.service.bookingCouponList &&
                state.service.bookingCouponList.selectedCoupon) ||
            null,
        activeBookingDeduction: state.servicePackage.booking.activeBookingDeduction,
        // 获得的积分
        earnPoints,

        // 使用积分后的最终价格
        totalPrice,
        vipPrice,
        offlinePrice,
        pointPrice,

        // 使用积分 + 选择使用会员
        totalPriceWithVip,

        // 可使用的积分
        totalPoints,
        vipPoints,
        offlinePoints,

        // 已节省价格
        savePriceMember,
        savePriceNomember,
        savePriceMemberWithPoints,
        savePriceOfWX,

        isUserVip,

        selectedPets: state.servicePackage.booking.selectedPets,

        userInfo: state.wp.user.dashboard.userInfo,
        user: state.user.user.user,
        userBalanceOfDirect: state.charge.userBalanceOfDirect,
        userBalanceOfCurrentStore: state.charge.userBalanceOfCurrentStore,
        isUseVipDiscount,
        use,
        notuse,
        disabledCouponsNum: state.service.bookingCouponList.disabledCouponsNum,
        useCouponsNum: state.service.bookingCouponList.useCouponsNum || 0,
        posOrderResult: state.service.booking.posOrderResult,
        bookingOrderId: state.servicePackage.booking.bookingOrderId,
        medicalPoints: state.servicePackage.booking.medicalPoints,
        bookingOrderResult: state.servicePackage.booking.bookingOrderResult,
        bookingBalanceCardList:
            state.service.bookingCouponList.bookingBalanceCardList || [],
        activeBookingBalanceCard:
            state.service.bookingCouponList.activeBookingBalanceCard || {},
        paymentSystem: state.service.bookingCouponList.paymentSystem || "BALANCE",
        selectPaymentType:
            state.service.bookingCouponList.selectPaymentType || "VIP",
        originStoreList: state.wp.store.warehouse.origin,
        contactInfo: state.wp.store.warehouse.warehouseContactInfo,
        availableWarehouse: state.wp.store.warehouse.availableWarehouse,
        pointDeductionPrice: state.service.bookingCouponList.pointDeductionPrice,
        deductionPoint: state.service.bookingCouponList.deductionPoint,
        currencySymbol: state.wp.store.warehouse.warehouseById.currencySymbol || '￥',
    };
};

const mapDispatchToPage = (dispatch) =>
    bindActionCreators(
        {
            requestBookingCouponList:
            acs.service.bookingCouponList.requestBookingCouponListAll,
            selectBookingCouponList:
            acs.service.bookingCouponList.selectBookingCouponList,
            requestDateRange: acs.servicePackage.booking.requestDateRange,
            requestConfirmAc: acs.servicePackage.booking.requestConfirm,
            saveServiceBooking: acs.service.booking.saveServiceBooking,
            resetServiceBooking: acs.service.booking.resetServiceBooking,
            resetServiceBookingTransfer:
            acs.service.booking.resetServiceBookingTransfer,
            createServiceBooking: acs.service.booking.createServiceBooking,
            requestBookingUserInfo: acs.service.booking.requestBookingUserInfo,
            requestCancelBookingOrder: acs.service.booking.requestCancelBookingOrder,
            requestCancelBookedOrder: acs.service.booking.requestCancelBookedOrder,
            requestBookingListDetail:
            acs.service.bookingList.requestBookingListDetail,
            requestRepayData: acs.service.booking.requestRepayData,
            requestAdvancePaymentConfig: acs.service.booking.requestAdvancePaymentConfig,
            // 到店支付isOnlinePay
            saveIsOnlinePay: acs.service.bookingCouponList.saveIsOnlinePay,
            showToast: acs.global.toast.show,
            updateActiveBookingDeduction:
            acs.servicePackage.booking.updateActiveBookingDeduction,
            confirmIsUseVipDiscount:
            acs.service.bookingCouponList.confirmIsUseVipDiscount,

            requestPosOrderResult: acs.service.booking.requestPosOrderResult,
            requestPosOrderRefund: acs.service.booking.requestPosOrderRefund,
            getPosOrderStatus: acs.service.booking.getPosOrderStatus,
            changeBookingBalanceCard:
            acs.service.bookingCouponList.changeBookingBalanceCard,
        },
        dispatch
    );

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
