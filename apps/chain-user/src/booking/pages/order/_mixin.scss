@import "config/theme";
$primary: map-get($lucky-theme, primary);
$accent: map-get($lucky-theme, accent);
$warn: map-get($lucky-theme, warn);
$foreground: map-get($lucky-theme, foreground);
$background: map-get($lucky-theme, background);

@mixin mp-crown-order-theme($theme) {
  @include mp-crown-order-other-theme($theme);
  @include mp-crown-order-user-theme($theme);
  @include mp-crown-order-pet-theme($theme);
  @include mp-crown-order-refund-progress-theme($theme);
  @include mp-crown-order-service-theme($theme);
  @include mp-crown-order-shuttle-theme($theme);
  @include mp-crown-order-pay-theme($theme);
  @include mp-crown-order-notice-theme($theme);
  @include mp-crown-order-confirm-theme($theme);
  @include mp-crown-order-buttons-theme($theme);
  @include mp-crown-order-detail-theme($theme);
  @include mp-crown-order-dialog-theme($theme);

  @include mp-crown-order-vip-info-theme($theme);
}

@mixin mp-crown-order-other-theme($theme) {
  .mp-crown-order {
    &__container {
      position: relative;
      padding: u(24);
    }

    &__background {
      position: absolute;
      z-index: -1;
      top: 0;
      left: 0;
      right: 0;
      height: u(410);
    }

    &__card {
      background-color: #fff;
      border-radius: u(8);
      margin-top: u(24);
      padding: u(24);

      &__pb-sm {
        padding-bottom: u(12);
      }
    }

    &__card--price {
      position: relative;
      box-shadow: 0 u(6) u(8) 0 rgba(243,242,242,0.9);
    }

    // 联系商家
    &__contact {
      margin-top: u(2);
      text-align: center;
      height: u(96);
      line-height: u(96);
      border-bottom-left-radius: u(16);
      border-bottom-right-radius: u(16);
      background-color: #fff;

      &--text {
        font-size: u(28);
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight: 400;
        color: #2F2F2F;
        line-height: u(56)
      }

      &--icon {
        font-size: u(32);
        color: #2F2F2F;
        margin-right: u(4);
      }
    }

    &__border {
      border-bottom: 1px solid #ebebeb;
      margin: u(16) 0;
      overflow: hidden;
    }

    &__bar-border {
      width: 236rpx;
      height: 4rpx;
      background: #e3e3e3;
      transform: translateY(-50%);
    }

    &__bar-border_active {
      background: mat-color($primary);
    }

    &__circle {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background: #d4d4d4;
      transform: translateY(-50%);
    }

    &__circle_active {
      background: mat-color($primary);
    }
  }
}

@mixin mp-crown-order-user-theme($theme) {
  .mp-crown-order {
    &__user {
      margin: -24rpx -24rpx u(24);
      padding: 30rpx 24rpx;
      border-top: 1px solid #ebebeb;
      background-color: #ffffff;

      &-user-heading-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &-hint {
        color: map-get($foreground, hint-text);
        font-size: u(28);
        display: inline-block;
      }

      &-cancel-booking {
        color: mat-color($primary);
        font-size: u(28);
        float: right;
      }

      &-name {
        display: inline;
        color: map-get($foreground, hint-text);
        font-size: u(44);
      }

      &-mobile {
        display: inline;
        color: map-get($foreground, hint-text);
        font-size: u(44);

        margin-left: u(24);
      }

      &-address {
        font-size: u(28);
      }
    }
  }
}

@mixin mp-crown-order-pet-theme($theme) {
  .mp-crown-order {
    &__pet {
      &-container {
        margin-bottom: u(8);
        display: flex;
        align-items: center;
      }

      &-img {
        border-radius: 50%;
        width: u(84);
        height: u(84);

        flex-shrink: 0;

        margin-right: u(12);
      }

      &-text {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &-name {
        color: #2f2f2f;
        font-size: u(28);
        line-height: 1.4em;
      }

      &-breed {
        color: #9397a2;
        font-size: u(28);
        line-height: 1.4em;
      }
    }
  }
}

@mixin mp-crown-order-refund-progress-theme($theme) {
  .mp-crown-order {
    &__refund-progress {
      &-title {
        font-size: 28rpx;
      }

      &-bar {
        display: flex;
        justify-content: center;
        margin: 34rpx 0;
      }

      &-status {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-string {
          font-size: u(28);

          &-left {
            transform: translateX(20%);
          }

          &-right {
            transform: translateX(-20%);
          }

          &_active {
            color: #23b9de;
          }
        }
      }
    }
  }
}

@mixin mp-crown-order-service-theme($theme) {
  .mp-crown-order {
    &__service {
      &-store {
        color: #9397a2;

        &-name {
          color: map-get($foreground, hint-text);
          font-size: u(28);
          font-weight: bold;

          margin-bottom: u(12);
        }

        &-icon {
          width: u(24);
          height: u(30);

          display: inline-block;
        }

        &-address {
          font-size: u(24);
          color: #9397a2;
        }
      }

      &-paytype {
        display: flex;

        height: u(52);

        &__wrapper {
          display: flex;
          align-items: center;

          padding-right: u(48);

          color: map-get($foreground, hint-text);
          font-size: u(28);
          line-height: u(32);
          font-weight: 500;

          &-check {
            padding-right: u(16);
          }
        }
      }

      &-click {
        height: u(50);

        display: flex;
        align-items: center;

        color: map-get($foreground, hint-text);
        font-size: u(28);
        font-weight: 500;

        justify-content: space-between;
        padding: u(24);
        border-radius: u(8);
        background-color: #ffffff;

        &__wrapper {
          display: flex;
          align-items: center;

          & > .icon-WeChatpay {
            color: #28d126;
            font-size: u(36);
          }

          & > .icon-VIP {
            color: #f8cb2c;
            font-size: u(36);
          }

          &-font {
            padding: 0 u(12);
          }

          &-right {
            font-size: u(18);
            color: #b0b0b0;
            padding-top: u(2);
            font-weight: 700;
          }
        }
      }

      &-item {
        display: flex;
        align-items: center;
        position: relative;

        &_only {
          align-items: flex-start;

        }
        &-desc {
          display: flex;
          flex-direction: column;
          flex:1;
          margin-left: u(24);

          &-top {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            &-name {
              font-size: u(28);
              font-family:PingFangSC-Regular;
              font-weight:400;
              color:rgba(47,47,47,1);
              margin-right: u(40);
              line-height: u(28);

            }

            &-price {
              font-size: u(28);
              font-family:PingFangSC-Regular;
              font-weight:400;
              color:rgba(47,47,47,1);
              line-height: u(28);
            }
          }

          &-bottom {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-top: u(16);

            &-type {
              font-size: u(24);
              font-family:PingFangSC-Regular;
              font-weight:400;
              color:rgba(147,151,162,1);
            }

            &-check {

              &-content {
                font-size: u(24);
                font-family:PingFangSC-Regular;
                font-weight:400;
                color:rgba(87,93,106,1);
              }
            }
          }
        }

        &-img-wrapper {
          flex-shrink: 0;

          box-sizing: border-box;
          height: u(112);
          width: u(112);
          padding: u(16);

          display: flex;
          align-items: center;
          justify-content: center;

          border: 1px solid #e3e3e3;
          border-radius: u(8);
        }

        &-img {
          width: 100%;
          height: 100%;
        }

        &-name {
          flex-grow: 1;

          padding-left: u(24);
          margin-right: u(24);
          color: map-get($foreground, hint-text);
          font-size: u(28);

          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-top: u(-60);
        }

        &-type {
          position: absolute;
          color: #9397a2;
          font-size: u(24);
          left: u(138);
          margin-top: u(16);
        }

        &-price {

          padding-left: u(24);
          margin-right: u(24);
          color: map-get($foreground, hint-text);
          font-size: u(28);

          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-top: u(-60);
        }

        &-amount {
          flex-grow: 1;
          color: map-get($foreground, hint-text);
          font-size: u(32);

          text-align: right;
        }
      }

      &-date {
        color: map-get($foreground, hint-text);
        font-size: u(28);

        display: flex;
        justify-content: space-between;
        padding: u(12) 0;

        &-text {
          font-size: u(28);
        }
      }

      &-duration {
        color: map-get($foreground, hint-text);
        font-size: u(28);

        display: flex;
        justify-content: space-between;
        padding: u(12) 0;

        &-text {
          font-size: u(28);
        }
      }

      &-purchase {
        display: flex;
        justify-content: space-between;
        padding: u(12) 0;

        &-text {
          color:map-get($foreground, hint-text);
          font-size: u(28);
        }

        &-price {
          color: map-get($foreground, hint-text);
          font-size: u(28);
        }
      }

      &-coupon {
        display: flex;
        justify-content: space-between;
        padding: u(12) 0;

        &-text {
          color: map-get($foreground, hint-text);
          font-size: u(28);
        }

        &-price {
          color: map-get($foreground, hint-text);
          font-size: u(28);
        }
      }

      &-vip-points {
        display: flex;
        justify-content: space-between;
        padding: u(12) 0;

        &__icon {
          display: flex;
          align-items: center;
        }

        &-url {
          width: u(116);
          height: u(24);
          margin-left: u(16);
        }

        &-text {
          color: map-get($foreground, hint-text);
          font-size: u(28);
        }

        &-price {
          color: map-get($foreground, hint-text);
          font-size: u(28);
          font-weight:500;
        }
      }

      &-remark {
        display: flex;
        align-items: center;
        padding: u(12) 0;

        &-text {
          font-size: u(28);
          font-weight: 500;
        }

        &-input {
          flex-grow: 1;
          font-size: u(28);
        }

        &-placeholder {
          color: #d4d4d4;
        }
      }

      &-get-coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: u(12) 0;

        &-limit {
          width: u(78);
          height: u(28);
          border: 1px solid #23b9de;
          background-color: #d7f7ff;
          color: #23b9de;
          text-align: center;
          line-height: u(28);
          margin-right: u(16);
        }

        &-discount {
          width: u(78);
          height: u(28);
          border: 1px solid #8cd23c;
          background-color: #f9ffed;
          color: #8cd23c;
          text-align: center;
          line-height: u(28);
          margin-right: u(16);
        }

        &-text {
          color: map-get($foreground, hint-text);
          font-size: u(28);
          position: absolute;
          left: u(100);
          width: u(300);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &-price {
          color: map-get($foreground, hint-text);
          font-size: u(28);
        }
      }

      &-booking-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: u(12) 0;

        &-title {
          font-size: u(28);
          font-weight: 500;
        }

        &-status {
          font-size: u(28);
          color: mat-color($primary);
        }
      }
    }
  }
}

@mixin mp-crown-order-shuttle-theme($theme) {
  $foreground: map-get($theme, foreground);
  $primary: map-get($theme, primary);
  .mp-crown-order {
    &__shuttle {
      display: flex;
      align-items: center;
      padding-top: u(12);
      padding-bottom: u(24);
      background-color: #ffffff;

      &--active {
        display: flex;
        align-items: center;
        padding-top: u(12);
        padding-bottom: u(30);
        background-color: #ffffff;
        margin-bottom: u(24);
        padding: u(24);
        border-radius: u(8);
      }

      &-text {
        flex-grow: 1;

        font-size: u(28);
        line-height: u(28);
        font-weight: 500;
        color: #b0b0b0;

        position: relative;

        &.active {
          color: map-get($foreground, hint-text);
        }
      }

      &-free {
        width: u(72);
        height: u(26);

        position: absolute;
        top: u(-24);
        left: u(152);
      }

      &-link {
        flex-shrink: 0;

        color: #b0b0b0;
        font-size: u(28);
        line-height: u(28);

        margin-right: u(8);

        &.active {
          color: mat-color($primary);
        }

        &-detail {
          @extend .mp-crown-order__shuttle-link;
          color: map-get($foreground, hint-text);

          max-width: u(240);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    &__more {
      &-coupon {
        display: flex;
        align-items: center;
        padding-bottom: u(24);

        &-text {
          flex-grow: 1;

          font-size: u(28);
          line-height: u(28);
          font-weight: 400;
          color: map-get($foreground, hint-text);

          position: relative;
        }
      }
    }
    &__coupon {
      display: flex;
      align-items: center;
      padding-top: u(12);
      padding-bottom: u(24);
      justify-content: space-between;

      &:last-child {
        padding-bottom: 0;
      }

      &__show {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      &_block {
        display: flex;
        align-items: center;
        width: 100%;
      }

      &-vip {
        display: flex;
        align-items: center;
        width: 100%;
      }

      &-url {
        width: u(58);
        height: u(24);
        margin-right: u(8);
      }

      &-text {
        flex-grow: 1;

        font-size: u(28);
        line-height: u(28);
        font-weight: 500;
        color: map-get($foreground, hint-text);

        position: relative;
        font-family:PingFangSC-Medium,PingFang SC;

        &-disabled {
          line-height: u(26);
          font-weight: 500;
          font-size: u(26);
          color: #B0B0B0;
        }
      }

      &-link {
        flex-shrink: 0;
        color: #fbc02d;
        font-size: u(28);
        line-height: u(28);
        font-weight: 500;

        margin-right: u(8);

        &-disabled {
          color: #B0B0B0;
          font-size: u(26);
          margin-right: u(8);
        }

        &-active {
          font-size: u(28);
          color: map-get($foreground, secondary-text);
        }

        &--active {
          color: map-get($foreground, secondary-text);
        }

        &-detail {
          @extend .mp-crown-order__coupon-link;
          color: map-get($foreground, hint-text);

          width: u(240);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &-orange-color {
        color: #fb9e0f;
      }

      &__package-price {
        font-size: u(26);
        font-weight: 400;
        color: #FA7268;
        margin-left: u(16);
        margin-right: u(8);
      }
    }

    &__pd-6 {
      padding-bottom: u(6);
    }
  }
}

@mixin mp-crown-order-pay-theme($theme) {
  .mp-crown-order {
    &__pay {
      .mp-crown-order__border {
        margin-top: 0;
      }

      &-title {
        color: map-get($foreground, hint-text);
        font-size: u(28);
        font-weight: bold;

        margin-bottom: u(16);
      }

      &-purchase {
        display: flex;
        margin-bottom: u(16);

        &-text {
          color: #575d6a;
          font-size: u(24);
          margin-right: u(24);
        }

        &-number {
          color: #575d6a;
          font-size: u(24);
        }
      }

      &-type {
        float: right;
        display: flex;
      }

      &-icon {
        width: u(40);
        height: u(36);
        color: #28D126;
      }

      &-vip {
        display: inline-block;
        color: #f8cb2c;
        vertical-align: middle;
      }

      &-method {
        display: inline-block;
        vertical-align: middle;
        margin-left: u(8);

        color: map-get($foreground, hint-text);
        font-size: u(28);
        font-weight: bold;
      }

      &-text {
        color: #b0b0b0;
        font-size: u(20);
      }

      &-number {
        color: #575d6a;
        font-size: u(24);
        margin-bottom: u(16);

        &-text {
          float: right;
          margin-right: u(400);
        }
      }
    }
  }
}

@mixin mp-crown-order-notice-theme($theme) {
  .mp-crown-order {
    &__notice {
      margin-bottom: u(140);

      &-title {
        color: map-get($foreground, hint-text);
        font-size: u(28);
        font-weight: bold;

        margin-bottom: u(16);
      }

      &-text {
        color: #9397a2;
        font-size: u(24);
      }

      &--isIphone {
        margin-bottom: u(200);
      }
    }
  }
}

@mixin mp-crown-order-confirm-theme($theme) {
  .mp-crown-order {
    &__confirm {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;

      display: flex;
      align-items: center;
      justify-content: flex-end;

      background-color: #fff;

      border-top: 1px solid #ebebeb;

      z-index: 99;
      height: u(128);

      &__wrapper {
        &-top {
          display: flex;
          line-height: u(40);
        }

        &-bottom {
          text-align: right;
          margin-right: u(16);
          font-size: u(20);
          color: mat-color($primary);

          position: relative;
          float: right;
        }
      }

      &-text {
        color: #575d6a;
        font-size: u(28);
      }

      &-currency {
        color: map-get($foreground, hint-text);
        font-size: u(40);
      }

      &-amount {
        color: map-get($foreground, hint-text);
        font-size: u(40);

        margin-right: u(16);
      }

      &-btn {
        width: u(208);
        height: u(80);

        display: flex;
        justify-content: center;
        align-items: center;
        background-color: mat-color($primary);

        color: #fff;
        font-size: u(32);
        line-height: u(80);

        border-radius: u(48);
        margin-right: u(24);
        margin-left: u(12);
      }
    }
  }
}

@mixin mp-crown-order-buttons-theme($theme) {
  .mp-crown-order {
    &__buttons {
      display: flex;
      box-sizing: border-box;
      padding-top: 24rpx;
      justify-content: flex-end;
      align-items: center;

      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;

      background-color: #fff;

      border-top: 1px solid #ebebeb;

      &-button {
        box-sizing: border-box;
        border-radius: 26rpx;
        width: 144rpx;
        height: 56rpx;
        font-size: 24rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;

        display: flex;
        align-items: center;
        justify-content: center;

        &-cancel {
          font-size: u(24);
          color: mat-color($primary);
          border: mat-color($primary) u(2) solid;
        }

        &-pay {
          font-size: 24rpx;
          color: #fff;
          background-color: mat-color($primary);
        }
      }
    }
  }
}

@mixin mp-crown-order-detail-theme($theme) {
  .mp-crown-order-detail {
    &__container {
      .mp-crown-order {
        &__shuttle {
          padding-top: u(30);
        }

        &__service-item {
          margin-bottom: u(16);

          &:last-child {
            margin-bottom: 0;
          }

          &-check {
            position: absolute;
            right: 0;
            color: #9397a2;

            &-wrapper {
              flex-shrink: 0;
              text-align: right;
            }
          }
        }

        &__pet {
          &-title {
            color: #2f2f2f;
            font-size: u(28);
            font-weight: bold;
            line-height: 1em;
            margin-bottom: u(24);
          }

          &-name {
            color: #2f2f2f;
            font-size: u(28);
            line-height: 1em;
          }

          &-img-wrapper {
            float: right;
            width: u(84);
            height: u(84);
          }

          &-img {
            border-radius: 50%;
            width: u(84);
            height: u(84);
          }
        }
      }
    }
  }
}

@mixin mp-crown-order-dialog-theme($theme) {
  .mp-crown-dialog > .mp-crown-dialog__container {
    transform: translateY(0);
  }

  .mp-crown-dialog > .mp-crown-dialog__mask {
    display: block;
  }

  .mp-crown-dialog {
    &__mask {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10;
      background: rgba(0, 0, 0, 0.7);
      display: none;
    }

    &__container {
      position: fixed;
      left: 0;
      bottom: 0;
      width: u(750);
      background: white;
      transform: translateY(150%);
      transition: all 0.4s ease;
      z-index: 11;

      &__content {
        background-color: #fff;
        padding-bottom: u(280);
      }
    }
  }

  .dialog {
    &-header {
      display: flex;
      align-items: center;

      height: u(100);

      position: relative;
      border-bottom: u(2) solid #ebebeb;

      &__title {
        text-align: center;
        flex: 1;

        font-size: u(32);
        color: #2f2f2f;
      }

      &__close {
        font-size: u(40);
        color: #b0b0b0;
        line-height: u(40);

        position: absolute;
        top: u(32);
        right: u(32);
      }
    }

    &-info {
      font-size: u(26);
      color: #9397a2;
      line-height: u(34);
      height: u(96);
      padding: u(14) u(32);
      border-bottom: u(2) solid #ebebeb;

      display: flex;
      align-items: center;
      background-color: #f9f9f9;
    }

    &-item {
      display: flex;
      align-items: center;
      padding: u(27) u(32);
      border-bottom: u(2) solid #ebebeb;

      &__icon {
        font-size: u(40);
        margin-right: u(20);
      }

      &__text {
        flex: 1;

        &-title {
          color: #2f2f2f;
          font-size: u(28);
          margin-bottom: u(2);
        }

        &-subtitle {
          color: #b0b0b0;
          font-size: u(24);
        }
      }

      &__icon--wx {
        color: #28d126;
      }

      &__icon--vip {
        color: #f8cb2c;
      }
    }

    &-item--special {
      display: flex;
      align-items: center;
      padding: u(27) u(32);
      border-bottom: u(2) solid #ebebeb;

      &__icon {
        font-size: u(40);
        margin-right: u(20);
        color: #e3e3e3;
      }

      &__text {
        flex: 1;

        &-title {
          font-size: u(28);
          margin-bottom: u(2);
          display: flex;
          justify-content: space-between;

          &-font {
            color: #2f2f2f;
          }

          &-recharge {
            $parent: &;

            color: map-get($foreground, secondary-text);
            display: flex;
            font-size: u(32);
            align-items: center;

            &-row {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: flex-end;

              margin-bottom: u(-48);
            }

            &-wechat {
              @extend #{$parent};

              font-size: u(32);
              color: #575d6a;
            }

            &-navigate {
              display: flex;
              align-items: center;
            }

            i {
              font-size: u(22);
              margin-left: u(6);
            }
          }
        }

        &-subtitle {
          color: #b0b0b0;
          font-size: u(24);
        }
      }
    }
  }
}

@mixin mp-crown-order-vip-info-theme($theme) {
  .mp-crown-order {
    &__vip-info {
      &-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &-border {
        margin: u(16) 0;
        overflow: hidden;
      }

      &-main-content {
        display: flex;
        align-items: center;

        &-title {
          font-size: u(28);
          font-family: PingFangSC-Medium,PingFang SC;
          font-weight: 500;
          margin-right: u(28);
        }
      }

      &-icon {
        width: u(24);
        height: u(22);
        margin-right: u(16);

        &-explain {
          margin-left: u(8);

          font-size: u(28);
          color: #9397a2;
        }
      }

      &-text {
        display: flex;

        &-emphasize {
          font-weight: 500;
        }
      }

      &-description {
        font-size: u(28);
        margin-left:u(8);
        line-height: u(28);
      }

      &-popup {

        padding: u(0) u(32);
        display: flex;
        flex-direction: column;

        &-top {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          position: relative;
          padding: u(32) u(0);
          border-bottom: u(4) solid hsla(0, 0%, 95%, 1);

          &-desc {
            font-size: u(32);
            font-family:PingFangSC-Medium,PingFang SC;
            font-weight:500;
            color:rgba(47,47,47,1);
          }

          &-close {
            position: absolute;
            right: u(32);
            width: u(24);
            height: u(24);

            & > image {
              width: 100%;
              height: 100%;
            }
          }
        }

        &-bottom {

          &-use {
            font-size: u(32);
            font-family:PingFangSC-Medium;
            font-weight:500;
            color:rgba(47,47,47,1);
            line-height: u(32);
            margin-top: u(48);
          }

          &-ol {
            font-size:14px;
            font-family:PingFangSC-Regular;
            font-weight:400;
            color:rgba(87,93,106,1);
            line-height: u(28);
            margin-top: u(24);
          }
        }

        &-button {
          width: 100%;
          height: u(84);
          margin-top: u(176);
          margin-bottom: u(32);
          background: rgba(23,39,145,1);
          font-size: u(36);
          font-family:PingFangSC-Medium;
          font-weight:500;
          color:rgba(255,255,255,1);
          border-radius: u(50);
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }
      }


      &-preferentialPopup {
        border-radius: u(16) u(16) 0 0;
        &-title {
          font-size: u(32);
          font-family:PingFangSC-Medium,PingFang SC;
          font-weight:500;
          color:rgba(47,47,47,1);
          line-height: u(32);
          margin-top: u(32);
          text-align: center;
        }

        &-check {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          padding: u(0) u(60);
          margin-top: u(32);
          &-text {
            font-size: u(28);
            font-family:PingFangSC-Regular,PingFang SC;
            font-weight:400;
            color:rgba(47,47,47,1);
          }

          &-circle {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-content: center;

            &-selected {
              color: mat-color($primary);
              font-size: u(40);
            }

            &-noselected {
              font-size: u(40);
            }
          }
        }

        &-button {
          height: u(96);
          margin: u(350) u(60) u(48) u(60);
          background-color: mat-color($primary);
          border-radius: u(48);
          font-size: u(32);
          font-family:PingFangSC-Medium,PingFang SC;
          font-weight:500;
          color:rgba(255,255,255,1);
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
