@import 'config/theme';
@import 'styles/component/purchase-card';
@import 'styles/icon';
@import './mixin';

@mixin mp-crown-order-purchase-theme($theme) {
  $primary: map-get($theme, primary);
  .mp-crown-order-purchase {
    &__icon {
      &-left {
        margin-right: u(24);
      }

      &-right {
        margin-left: u(24);
      }

      &-minus, &-plus {
        font-size: u(40);
        color: mat-color($primary);
      }

      &-minus.active {
        color: mat-color($primary) !important;
      }
    }
  }
}

@mixin mp-order-prepurchase__selsect($theme) {
  $primary: map-get($theme, primary);
  .mp-order-prepurchase__selsect {

    &__icon-xuanzhong {
      font-size: u(40);
      color: mat-color($primary);
    }

    &__icon-circle {
      font-size: u(40);
      color: rgba(176, 176, 176, 1);
    }
  }
}


@include mp-order-prepurchase__selsect($lucky-theme);
@include mp-crown-icon($lucky-theme);
@include mp-purchase-card-theme($lucky-theme);
@include mp-common-purchase-card-theme($lucky-theme);
@include mp-crown-order-purchase-theme($lucky-theme);
