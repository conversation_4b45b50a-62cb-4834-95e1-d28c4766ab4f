// 此样式文件用于 shopping/order-purchase/order-purchase.scss 文件
@mixin mp-common-purchase-card-theme($theme) {
  $primary: map-get($theme, primary);

  .mp-order-prepurchase {
    height: 100%;
    width: 100%;
    overflow: hidden;

    display: flex;
    flex-direction: column;
  }

  .mp-crown-order-prepurchase__main {
    flex-grow: 1;
    width: 100%;
    overflow: auto;
  }

  .mp-crown-order-prepurchase__buttons {
    flex-shrink: 0;
    background-color: mat-color($primary);
    color: #fff;
    height: u(96);
    width: 100%;
    text-align: center;
    line-height: u(96);
    font-size: u(36);
    font-weight: 500;
    border-radius: 0;

    &[class][disabled] {
      background-color: #e3e3e3;
      color: #fff;
    }
  }

  .mp-order-prepurchase__use {
    flex: 0 0 auto;
    background-color: #fff;
    height: u(96);
    width: u(694);
    margin: u(24) auto;
    border-radius: u(8);
    display: flex;
    justify-content: space-between;

    .mp-order-prepurchase__text {
      font-size: u(28);
      margin-left: u(16);
      line-height: u(96);
      color: #2f2f2f;
    }

    .mp-order-prepurchase__selsect {
      line-height: u(96);
      margin-right: u(16);
    }
  }

  .mp-purchase-card--normal .mp-purchase-card__divider-container {
    height: u(10);
  }

  .mp-purchase-card__main-tips {
    height: u(54);
    display: flex;
    align-items: center;
    margin-bottom: u(10);
  }

  .mp-purchase-card--normal {
    flex: 1 0 auto;
  }

  .mp-purchase-card--normal .mp-purchase-card__main-content-item {
    width: 100%;
    height: u(112);
    border-top: u(2) solid #f1f1f1;
    line-height: u(112);
    font-size: u(28);
    display: flex;
    justify-content: space-between;

  }

  .mp-purchase-card__main-content-item {
    display: flex;
    height: u(112);

    .mp-purchase-card__main-content-item-left {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 50%;
    }

    .mp-purchase-card__main-content-item-right {
      display: flex;

      .mp-purchase-card__main-content-item-right-text {
        font-size: u(28);
        height: u(40);
        margin-right: u(26);
      }

      .icon-plus {
        font-size: u(40);
        color: #7EAFE1;
      }

      .icon-minus {
        font-size: u(40);
        color: #f1f1f1;
      }

      .mp-purchase-card__main-content-item-right-image {
        font-size: u(28);
        height: u(40);
        margin-bottom: u(20);

        .add {
          margin-top: u(20)
        }
      }

      image {
        display: inline-block;
      }

      span {
        display: inline-block;
        margin-right: u(10);
        padding-left: u(10);
      }
    }
  }


  .wp-crown-booking-service__service-single-icon {
    width: u(40);
    height: u(40);
    line-height: u(40);
    margin-top: u(36);
  }

  .mp-crown-icon__circle {
    margin-top: u(10);
  }

  .disabled {
    color: #b0b0b0;
  }

  .mp-purchase-card__main .mp-purchase-card__main-tips {
    padding-bottom: 0;

    .mp-purchase-card__main-star {
      width: u(6);
      height: u(6);
      background: #e3e3e3;
      border-radius: 50%;
    }
  }

  .mp-purchase-card--normal .mp-purchase-card__main {
    padding: 0 u(30) 0;
  }
}
