import {
  connect
} from 'utils/weapp-redux';
import {
  bindActionCreators,
} from 'redux';
import {
  acs,
} from '@petkit/redux';
import {
  once,
} from 'lodash-es';
import moment from 'moment';
import {
  cloneDeep,
} from 'lodash-es';

const pageConfig = {
  data: {
    isUseCoupon: false,
    type: '',
    initPagePrepurchases: null,
    isChangePrepurchases: false,
    isReturn: false,
    initPageSelectedPrepurchaseNum: 0,
    isClick: true,
    deductionNumber: 0, // 兑换券number
    activeDeduction: null, //当前生效的兑换券
  },

  onLoad() {
    this.onSendTap = once(this.onSendTap.bind(this));
  },
  onUnload() {
    // isChangePrepurchases 判断是是否改变预购券  isReturn 判断是否直接返回
    if (this.data.isChangePrepurchases === true && this.data.isReturn === false) {
      const prePurechases = this.data.pagePrepurchases = this.data.initPagePrepurchases;
      const storeId = this.data.storeInfo.id;
      const selectedPrepurchaseNum = this.data.selectedPrepurchaseNum;
      const isUnload = true;
      const paymentSystem = this.data.paymentSystem;
      this.requestBookingPrepurchaseMaxCount({
        storeId,
        prePurechases,
        selectedPrepurchaseNum,
        isUnload,
        paymentSystem,
      });
    }
  },
  onShow() {
    if (this.data.pagePrepurchases) {
      this.data.initPagePrepurchases = cloneDeep(this.data.pagePrepurchases);
    } else {
      this.data.initPagePrepurchases = this.data.initPrepurchases;
    }

    if (!this.data.activeBookingDeduction.consumePackageId) {
      this._resetDeductionHandle();
    } else {
      this.setData({
        deductionNumber: 1
      });
    }
  },
  // 兑换券右侧radio选中事件 切换状态
  onDeductionCheckTap(evt) {
    this.bookingPrepurchaseClear();
    let data = evt.detail.data;
    let _item = {};
    let param = {};
    this.data.list.forEach((item) => {
      if (item.couponType === 'DEDUCTION') {
        if (item.consumePackageId === data.consumePackageId) {
          item.active = data.active === undefined ? true : !data.active;
          _item = item;
        } else {
          item.active = false;
        }
      }
    });
    param = _item.active ? _item : {};
    // 关联兑换券状态
    this.setData({
      activeDeduction: _item,
      list: this.data.list,
      deductionNumber: _item.active ? 1 : 0
    });
    // 使用当前点击的兑换券
    this.onDeductionCouponTap(param);
  },
  // 清空兑换券状态
  _resetDeductionHandle() {
    this.updateActiveBookingDeduction({});
    this.data.list.forEach((item) => {
      item.active = false;
    });
    this.setData({
      list: this.data.list,
      deductionNumber: 0
    });
  },
  // 使用兑换券
  onDeductionCouponTap(deduction) {
    // 设置当前兑换券
    this.updateActiveBookingDeduction(deduction);
    // 清空预购券
    this._resetPrepurchase();
  },
  // 不使用优惠券
  onSendTap() {
    // 清兑换券
    this._resetCouponStatus();
  },
  // 重置状态并返回
  _resetCouponStatus() {
    this.updateActiveBookingDeduction({});
    this.bookingPrepurchaseClear();
    let obj = {};
    obj.isUseCoupon = false;
    obj.selectedCoupon = [];
    this.data.type = 'prepurchase';
    obj.type = this.data.type;
    this.selectBookingCouponList(obj);
    this._resetCoupons();
    wx.navigateBack();
  },

  _resetPrepurchase() {
    let obj = {};
    obj.isUseCoupon = this.data.isUseCoupon;
    obj.selectedCoupon = this.data.selectedCoupon;
    obj.type = this.data.type;
    this.selectBookingCouponList(obj);
  },
  onServiceCardAdd(
    {
      currentTarget: {
        dataset: {
          item,
        }
      }
    }) {
    if (!this.data.isClick) {
      return;
    }
    // 重置兑换券
    this._resetDeductionHandle();
    this.data.isClick = false;
    this.data.isChangePrepurchases = true;
    const initPrepurchasesBak = this.data.initPrepurchases.map(item => ({
      count: 0,
      id: item.id
    }));
    const initPrepurchases = this.data.pagePrepurchases ? this.data.pagePrepurchases : initPrepurchasesBak;
    if (item.limitNumber > 0 && item.count <= item.maxCount) {
      item.limitNumber -= 1;
      item.count += 1;
      this.data.selectedPrepurchaseNum++;
      initPrepurchases.map(initItem => {
        if (item.id === initItem.id) {
          initItem.count = item.count;
        }

      });
      const storeId = this.data.storeInfo.id;
      const selectedPrepurchaseNum = this.data.selectedPrepurchaseNum;
      const paymentSystem = this.data.paymentSystem;
      this.requestBookingPrepurchaseMaxCount({
        storeId,
        prePurechases: initPrepurchases,
        selectedPrepurchaseNum,
        paymentSystem,
      }, {
        complete: () => {
          this.data.isClick = true;
        }
      });
    } else {
      this.data.isClick = true;
    }

  },
  onServiceCardSub(
    {
      currentTarget: {
        dataset: {
          item,
        }
      }
    }) {
    if (!this.data.isClick) {
      return;
    }
    // 重置兑换券
    this._resetDeductionHandle();
    // 清空兑换券
    this.data.isChangePrepurchases = true;
    const initPrepurchases = this.data.pagePrepurchases;
    // 如果当前剩余可点击次数大于0
    if (this.data.selectedPrepurchaseNum > 0 && item.count > 0) {
      this.data.isClick = false;
      item.limitNumber += 1;
      item.count -= 1;
      this.data.selectedPrepurchaseNum--;
      initPrepurchases.map(initItem => {
        if (item.id === initItem.id) {
          initItem.count = item.count;
        }
      });
      this.setData({
        selectedPrepurchaseNum: this.data.selectedPrepurchaseNum
      });

      const storeId = this.data.storeInfo.id;
      const selectedPrepurchaseNum = this.data.selectedPrepurchaseNum;
      const paymentSystem = this.data.paymentSystem;
      //调获取预购券最大使用次数的接口
      this.requestBookingPrepurchaseMaxCount({
        storeId,
        prePurechases: initPrepurchases,
        selectedPrepurchaseNum,
        paymentSystem,
      }, {
        complete: () => {
          // 如果当前没有选中任何预购券，恢复列表数据(包含兑换券)
          if (this.data.selectedPrepurchaseNum === 0) {
            this.bookingPrepurchaseClear();
          }
          this.data.isClick = true;
        }
      });
    } else {
      this.data.isClick = true;
    }
  },

  //点击已选择按钮 把已选择的预购券传到上一个页面
  onSelectedPrepurchase() {
    this.data.isReturn = true;
    // 如果当前有选中的兑换券，直接返回
    if (this.data.deductionNumber !== 0) {
      wx.navigateBack();
    } else {
      if (this.data.selectedPrepurchaseNum > 0) {
        this.data.type = 'prepurchase';
        this.data.isUseCoupon = true;
        this.data.selectedCoupon = [];
        let list = this.data.list;
        list.forEach(item => {
          if (item.serviceSkies && item.serviceSkies.length && item.couponType !== 'DEDUCTION') {
            item.serviceSkies.forEach(outService => {
              if (outService.serviceSkies && outService.serviceSkies.length && outService.count > 0) {
                outService.serviceSkies.forEach(innerService => {
                  this.data.selectedCoupon.push({
                    id: outService.id,
                    count: outService.count,
                    serviceSkuId: innerService.id,
                    // 次卡改版
                    serviceCardType: item.serviceCardType,
                    serviceType: outService.serviceType
                  });
                });
              }
            });
          }
        });
        let obj = {};
        obj.isUseCoupon = this.data.isUseCoupon;
        obj.selectedCoupon = this.data.selectedCoupon;
        obj.type = this.data.type;
        this.selectBookingCouponList(obj);
        wx.navigateBack();
      } else {
        this.bookingPrepurchaseClear();
        let obj = {};
        obj.isUseCoupon = false;
        obj.selectedCoupon = [];
        this.data.type = 'prepurchase';
        obj.type = this.data.type;
        this.selectBookingCouponList(obj);
        wx.navigateBack();
      }
      this._resetCoupons();
    }
  },
  _resetCoupons() {
    this.resetCouponList({
      type: 'online',
    });
  }
};

const mapStateToData = state => {
  return {
    list: state.service.bookingCouponList.prepurchases,
    pets: state.servicePackage.booking.selectedPets,
    couponlist: state.service.bookingCouponList,
    storeInfo: state.wp.store.warehouse.storeInfo,
    state: state.service.booking.serviceBooking,
    initPrepurchases: state.service.bookingCouponList.initPrepurchases,
    bakPrepurchases: state.service.bookingCouponList.initPrepurchases,
    pagePrepurchases: state.service.bookingCouponList.pagePrepurchases,
    selectedPrepurchaseNum: state.service.bookingCouponList.selectedPrepurchaseNum,
    paymentSystem: state.service.bookingCouponList.paymentSystem,
    activeBookingDeduction: state.servicePackage.booking.activeBookingDeduction || {}
  };
};

const mapDispatchToPage = dispatch => bindActionCreators({
  selectBookingCouponList: acs.service.bookingCouponList.selectBookingCouponList,
  requestBookingPrepurchaseMaxCount: acs.service.bookingCouponList.requestBookingPrepurchaseMaxCount,
  requestBookingPrepurchaseMaxCountSuccess: acs.service.bookingCouponList.requestBookingPrepurchaseMaxCountSuccess,
  bookingPrepurchaseClear: acs.service.bookingCouponList.bookingPrepurchaseClear,
  resetCouponList: acs.service.bookingCouponList.resetCouponList,
  updateActiveBookingDeduction: acs.servicePackage.booking.updateActiveBookingDeduction
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
