<view class="mp-order-prepurchase">
  <view class="mp-crown-order-prepurchase__main">
    <view class="mp-order-prepurchase__use" bindtap="onSendTap">
      <view class="mp-order-prepurchase__text">不使用优惠</view>
      <view class="mp-order-prepurchase__selsect">
        <text class="PETKIT icon-circle mp-order-prepurchase__selsect__icon-circle" wx:if="{{ selectedPrepurchaseNum+deductionNumber>0 }}"></text>
        <text class="PETKIT icon-xuanzhong mp-order-prepurchase__selsect__icon-xuanzhong" wx:if="{{ selectedPrepurchaseNum+deductionNumber==0 }}"></text>
      </view>
    </view>
    <view class="mp-purchase-card__container mp-purchase-card--normal"
          wx:for="{{list}}"
          wx:for-item="item"
          wx:key="index">
      <!--兑换-->
      <view style="padding: 0 30rpx;" wx:if="{{item.couponType==='DEDUCTION'}}">
        <deduction-card showCheck="true"
                        bind:onCheckTap="onDeductionCheckTap"
                        item="{{item}}"></deduction-card>
      </view>
      <!-- 服务预购券 -->
      <view wx:if="{{item.serviceSkies && item.serviceSkies.length > 0 && item.couponType!=='DEDUCTION'}}"
            class="mp-purchase-card__wrapper" >
        <view class="mp-purchase-card__header">
          <view class="mp-purchase-card__header-title">
            {{item.strategyName}}
            <text class="mp-purchase-card__header-icon mp-purchase-card__header-icon--warn" wx:if="{{item.type === 'SERVICE'}}">服务</text>

          </view>
          <view class="mp-purchase-card__header-time">
            有效期
            <text>{{item._effectiveDate}}</text>
            至
            <text>{{item._expirationDate}}</text>
          </view>
          <view wx:if="{{item.couponType}}" class="mp-purchase-card__header-time">
            {{item.limitDesc}}
          </view>
        </view>
        <view class="mp-purchase-card__divider-container">
          <view class="mp-purchase-card__divider"></view>
          <view class="mp-purchase-card__divider-circle-left"></view>
          <view class="mp-purchase-card__divider-circle-right"></view>
        </view>
        <view class="mp-purchase-card__main">
          <view class="mp-purchase-card__main-tips" wx:if="{{item.storeId === 0}}">*可使用门店：所有门店</view>
          <view class="mp-purchase-card__main-tips" wx:if="{{item.storeId !== 0}}">*可使用门店：{{item.storeName}}</view>
            <!-- 服务内容 -->
          <view class="mp-purchase-card__main-content">
            <view class="mp-purchase-card__main-content-item"
                  wx:for="{{item.serviceSkies}}" wx:for-item="serviceItem" wx:key="index" wx:if="{{serviceItem.limitNumber !== 0 || serviceItem.count !== 0}}">
              <view class="mp-purchase-card__main-content-item-left {{serviceItem.disabled? 'disabled' :  '' }}" >
                {{serviceItem.serviceName}}
              </view>
              <view class="mp-purchase-card__main-content-item-right {{serviceItem.disabled ? 'disabled' :  '' }}">
                <view class="mp-purchase-card__main-content-item-right-text {{serviceItem.disabled ? 'disabled' :  '' }}">
                  剩{{serviceItem.limitNumber}}次
                </view>
                <text wx:if="{{serviceItem.count>0}}"
                      class="PETKIT icon-minus wp-crown-booking-service__service-single-icon mp-crown-order-purchase__icon-left add mp-crown-order-purchase__icon-minus {{serviceItem.count>0?'active':''}}"
                      data-item="{{serviceItem}}"
                      bind:tap="onServiceCardSub"></text>

                <text wx:if="{{serviceItem.count==0}}"
                      class="PETKIT icon-minus mp-crown-order-purchase__icon-left"
                      data-item="{{serviceItem}}"
                      bind:tap="onServiceCardSub"></text>

                <text>{{serviceItem.count}}</text>

                <text data-item="{{serviceItem}}"
                      bind:tap="onServiceCardAdd"
                      class="PETKIT icon-plus wp-crown-booking-service__service-single-icon mp-crown-order-purchase__icon-right sub mp-crown-order-purchase__icon-plus"
                      wx:if="{{!serviceItem.disabled}}"></text>
                <text wx:if="{{serviceItem.disabled}}" class="PETKIT icon-plus mp-crown-order-purchase__icon-right"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <button class="mp-crown-order-prepurchase__buttons" bind:tap="onSelectedPrepurchase">
    已选择
    <text wx:if="{{deductionNumber>0}}">{{ deductionNumber }}</text>
    <text wx:else>{{ selectedPrepurchaseNum }}</text>
  </button>
</view>
