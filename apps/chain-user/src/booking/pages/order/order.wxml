<!-- 确认订单页面 -->
<view class="mp-crown-order__container" wx:if='{{id === 0}}'>
  <view class="mp-crown-order__background"></view>
  <view class="mp-crown-order__user">
    <view class='mp-crown-order__user-heading-container'>
      <view class="mp-crown-order__user-hint">确认预约人信息</view>
    </view>
    <view class="mp-crown-order__user-name">{{registerName}}</view>
    <view class="mp-crown-order__user-mobile">{{userMobile}}</view>
    <view class="mp-crown-order__user-address">{{state.address}}</view>
  </view>
  <view class="mp-crown-order__pet mp-crown-order__card">
    <view class="mp-crown-order__service-store">
      <view class="mp-crown-order__service-store-name">{{storeInfo.shellName}}</view>
      <text class="PETKIT icon-location1"></text>
      {{storeInfo.address}}
      <view class="mp-crown-order__service-store-address"></view>
    </view>
    <view class="mp-crown-order__border"></view>
    <view class="mp-crown-order__service-paytype">
      <view wx:if="{{bookingPaymentTypes.WECHAT}}" class="mp-crown-order__service-paytype__wrapper"
            bindtap="selectIsOnlinePay" data-type="online">
        <form-check class="mp-crown-order__service-paytype__wrapper-check" checked="{{isOnlinePay}}"></form-check>
        <text>线上支付</text>
      </view>
      <view wx:if="{{bookingPaymentTypes.STORE}}" class="mp-crown-order__service-paytype__wrapper"
            bindtap="selectIsOnlinePay" data-type="store">
        <form-check class="mp-crown-order__service-paytype__wrapper-check" checked="{{!isOnlinePay}}"></form-check>
        <text>到店支付</text>
      </view>
    </view>
  </view>
  <view class="mp-crown-order__service mp-crown-order__card" wx:for="{{pets}}" wx:for-item="pet" wx:key="id">
    <view class="mp-crown-order__pet-container">
      <image class="mp-crown-order__pet-img" src="{{pet.pet.imgUrl}}"></image>
      <view class="mp-crown-order__pet-text">
        <view class="mp-crown-order__pet-name">{{pet.pet.name}}</view>
        <view class="mp-crown-order__pet-breed">{{pet.pet.breedName}}</view>
      </view>
    </view>
    <view class="mp-crown-order__border"></view>
    <block wx:for="{{pet.packageSkus}}" wx:key="index">
      <view class="mp-crown-order__service-item">
        <view class="mp-crown-order__service-item-img-wrapper">
          <image class="mp-crown-order__service-item-img" src="{{item.imgUrl}}"></image>
        </view>
        <view class="mp-crown-order__service-item-name">{{item.spuName}}</view>
        <view class="mp-crown-order__service-item-amount">
          {{currencySymbol}}{{item.storePrice}}
        </view>
      </view>
      <view class="mp-crown-order__border"></view>
    </block>
    <block wx:for="{{pet.singleSkus}}" wx:key="imgUrl">
      <view class="mp-crown-order__service-item">
        <view class="mp-crown-order__service-item-img-wrapper">
          <image class="mp-crown-order__service-item-img" src="{{item.imgUrl}}"></image>
        </view>
        <view class="mp-crown-order__service-item-name">{{item.spuName}}</view>
        <view class="mp-crown-order__service-item-amount">
          {{currencySymbol}}{{item.storePrice}}
        </view>
      </view>
      <view class="mp-crown-order__border"></view>
    </block>
    <view class="mp-crown-order__service-date">
      <view class="mp-crown-order__service-date-text">预约服务时间</view>
      <view>{{pet.days[0] && pet.days[0].day}} {{pet.times[0] && pet.times[0].time}}</view>
    </view>
    <view class="mp-crown-order__border"></view>
    <view class="mp-crown-order__service-duration">
      <view class="mp-crown-order__service-duration-text">服务结束</view>
      <view>预计{{pet.times[0] && pet.times[0].endTime}}结束
      </view>
    </view>
  </view>
  <!-- 支付方式 -->
  <view class="mp-crown-order__service-click"
        bindtap="toggleDialog">
    <view class="mp-crown-order__service-click__wrapper">支付方式</view>
    <view class="mp-crown-order__service-click__wrapper">
      <text
        class="PETKIT {{!user.level.id && !userInfo.vipInfo || selectPaymentType === 'WX' || (activeBookingBalanceCard.cardType === 'BENEFIT_CARD')  ? 'icon-WeChatpay' : 'icon-VIP'}}"></text>
      <view class="mp-crown-order__service-click__wrapper-font">{{!user.level.id && !userInfo.vipInfo || selectPaymentType === 'WX' || (activeBookingBalanceCard.cardType === 'BENEFIT_CARD') ? '微信支付' : '会员余额支付'}}{{ activeBookingBalanceCard.cardType === 'BENEFIT_CARD' ? ' | ' +activeBookingBalanceCard.name : '' }}
      </view>
      <text class="PETKIT icon-jiantou mp-crown-order__service-click__wrapper-right"></text>
    </view>
  </view>
  <view class="{{!isOnlinePay ? '' : 'mp-crown-order__pay mp-crown-order__card'}}">
    <block wx:if="{{isOnlinePay  && couponlist.prepurchases.length}}">
      <view class="mp-crown-order__coupon" bind:tap="onPrepurchaseTap">
        <view class="mp-crown-order__coupon-text">预购券</view>
        <block wx:if="{{activeBookingDeduction.couponType==='DEDUCTION'}}">
          <view class="mp-crown-order__coupon-link">
            -{{currencySymbol}}{{selectPaymentType === 'WX' ? couponlist.purchasePrice : couponlist.vipPurchasePrice}}
          </view>
        </block>
        <block wx:else>
          <view
            class="mp-crown-order__coupon-link {{(usePrepurchaseNum && usePrepurchaseNum>=1)?'mp-crown-order__coupon-link--active':''}}"
            wx:if="{{!couponlist.isUsePrepurchase}}">
            {{ !usePrepurchaseNum ? '无可用预购券' : '可用预购券' + usePrepurchaseNum + '张'}}
          </view>
          <view class="mp-crown-order__coupon-link"
                wx:if="{{couponlist.isUsePrepurchase}}">
            -{{currencySymbol}}{{selectPaymentType === 'WX' ? couponlist.purchasePrice : couponlist.vipPurchasePrice}}
          </view>
        </block>
        <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></text>
      </view>
    </block>
    <block wx:if="{{isOnlinePay && (onlineCouponCount > 0 || couponlist.isUseOnlineCoupon)}}">
      <view class="mp-crown-order__coupon" bind:tap="onCouponOnLineTap">
        <view class="mp-crown-order__coupon-text">线上优惠</view>
        <view class="mp-crown-order__coupon-link mp-crown-order__coupon-orange-color"
              wx:if="{{!couponlist.isUseOnlineCoupon}}">
          可用预约优惠券{{onlineCouponCount}}张
        </view>
        <view class="mp-crown-order__coupon-link mp-crown-order__coupon-orange-color"
              wx:if="{{couponlist.isUseOnlineCoupon}}">
          -{{currencySymbol}}{{selectPaymentType === 'WX' ? couponlist.serviceCouponPrice : couponlist.vipServiceCouponPrice}}
        </view>
        <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></text>
      </view>
    </block>
    <block wx:if="{{isOnlinePay}}">
      <!--会员立减与优惠券不可同享-->
      <view class="mp-crown-order__coupon" wx:if="{{selectPaymentType !== 'WX'}}">
        <block wx:if="{{use}}">
          <block wx:if="{{useCouponsNum > 0 || disabledCouponsNum > 0}}">
            <view class="mp-crown-order__coupon__show" bind:tap="onCouponTap">
              <view class="mp-crown-order__coupon-text">优惠券</view>
              <view class="mp-crown-order__coupon-link-disabled">优惠券暂无法与会员折扣同享</view>
              <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></text>
            </view>
          </block>
          <block wx:else>
            <view class="mp-crown-order__coupon-text">优惠券</view>
            <view class="mp-crown-order__coupon-link-disabled">暂无可用券</view>
          </block>
        </block>
        <block wx:if="{{!use}}">
          <block>
            <view class="mp-crown-order__coupon__show">
              <view class="mp-crown-order__coupon-text">优惠券</view>
              <view class="mp-crown-order__coupon-link-disabled"
                    wx:if="{{!couponlist.isUseCoupon && !couponlist.useCouponsNum}}">
                暂无可用券
              </view>
              <view class="mp-crown-order__coupon-link"
                    wx:if="{{!couponlist.isUseCoupon && couponlist.useCouponsNum}}"
                    bind:tap="onCouponTap">
                {{'可用优惠券' + couponlist.useCouponsNum + '张'}}
              </view>
              <view class="mp-crown-order__coupon-link"
                    wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy !== 'POINT_LIMIT_ADD' && couponlist.couponPrice}}"
                    bind:tap="onCouponTap">
                -{{currencySymbol}}{{couponlist.couponPrice}}
              </view>
              <view class="mp-crown-order__coupon-link-disabled"
                    wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy !== 'POINT_LIMIT_ADD' && !couponlist.couponPrice}}">
                暂无可用券
              </view>
              <view class="mp-crown-order__coupon-link"
                    wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy === 'POINT_LIMIT_ADD' }}"
                    bind:tap="onCouponTap">+{{selectedCoupon.limitReduce || 0}}积分
              </view>
              <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"
                    wx:if="{{couponlist.useCouponsNum > 0}}"></text>
            </view>
          </block>
        </block>
      </view>
      <view class="mp-crown-order__coupon" wx:if="{{selectPaymentType === 'WX'}}">
        <view class="mp-crown-order__coupon-text">优惠券</view>
        <view
          class="mp-crown-order__coupon-link-disabled"
          wx:if="{{!couponlist.isUseCoupon && !couponlist.useCouponsNum}}">
          暂无可用券
        </view>
        <view
          class="mp-crown-order__coupon-link"
          wx:if="{{!couponlist.isUseCoupon && couponlist.useCouponsNum}}"
          bindtap="onCouponTap">
          {{'可用优惠券' + couponlist.useCouponsNum + '张'}}
        </view>
        <view
          class="mp-crown-order__coupon-link"
          wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy !== 'POINT_LIMIT_ADD' && couponlist.couponPrice}}"
          bind:tap="onCouponTap">
          -{{currencySymbol}}{{couponlist.couponPrice}}
        </view>
        <view
          class="mp-crown-order__coupon-link-disabled"
          wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy !== 'POINT_LIMIT_ADD' && !couponlist.couponPrice}}">
          暂无可用券
        </view>
        <view
          class="mp-crown-order__coupon-link"
          wx:if="{{couponlist.isUseCoupon && selectedCoupon.consumptionStrategy === 'POINT_LIMIT_ADD' }}"
          bind:tap="onCouponTap">
          +{{selectedCoupon.limitReduce || 0}}积分
        </view>
        <text
          class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"
          wx:if="{{couponlist.useCouponsNum > 0}}"></text>
      </view>
    </block>
    <!--会员专享-->
    <block wx:if="{{isOnlinePay}}">
      <block wx:if="{{selectPaymentType !== 'WX'}}">
        <view class="mp-crown-order__coupon">
          <view class="mp-crown-order__coupon_block" bind:tap="onShowVipDiscountPopup">
            <view class="mp-crown-order__coupon-vip">
              <image class="mp-crown-order__coupon-url"
                     src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.ZowXlrWzTZSy7838fc0a2470870dd6c9fd8d64bb9b6e.svg"/>
              <view class="mp-crown-order__coupon-text">会员专享</view>
              <view class="mp-crown-order__coupon__package-price" wx:if="{{use}}">
                节省{{couponlist.vipDiscountPrice ? couponlist.vipDiscountPrice : 0}}元：服务折扣专享
              </view>
              <view class="mp-crown-order__coupon__package-price" wx:if="{{notuse}}">不使用会员优惠</view>
            </view>
            <view class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></view>
          </view>
        </view>
      </block>
      <block wx:if="{{selectPaymentType === 'WX'}}">
        <view class="mp-crown-order__coupon">
          <view class="mp-crown-order__coupon_block">
            <view class="mp-crown-order__coupon-vip">
              <image class="mp-crown-order__coupon-url"
                     src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.ZowXlrWzTZSy7838fc0a2470870dd6c9fd8d64bb9b6e.svg"/>
              <view class="mp-crown-order__coupon-text">会员专享</view>
              <view class="mp-crown-order__coupon-text-disabled">微信支付不可使用会员专享</view>
            </view>
          </view>
        </view>
      </block>
      <view class="mp-crown-order__coupon">
        <view class="mp-crown-order__coupon_block" bind:tap="onShowStorePointsPopup">
          <view class="mp-crown-order__coupon-vip">
            <view class="mp-crown-order__coupon-text">可用积分</view>
            <view class="mp-crown-order__coupon__package-price" wx:if="{{isUseStorePoints}}">
              {{deductionPoint}}积分抵扣{{pointDeductionPrice}}
            </view>
            <view class="mp-crown-order__coupon-text-disabled" wx:if="{{!isUseStorePoints}}">不使用会员积分</view>
          </view>
          <view class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></view>
        </view>
      </view>

    </block>
    <!--闲时特惠 套餐价格 线上支付-->
    <block wx:if="{{isOnlinePay && couponlist.userPackageDiscountPrice && selectPaymentType === 'WX'}}">
      <view class="mp-crown-order__coupon">
        <view class="mp-crown-order__coupon-text">套餐优惠</view>
        <view class="mp-crown-order__coupon__package-price">
          -{{currencySymbol}}{{couponlist.userPackageDiscountPrice}}</view>
      </view>
    </block>
    <block wx:if="{{isOnlinePay && couponlist.packageDiscountPrice && selectPaymentType !== 'WX'}}">
      <view class="mp-crown-order__coupon">
        <view class="mp-crown-order__coupon-text">套餐优惠</view>
        <view class="mp-crown-order__coupon__package-price">
          -{{currencySymbol}}{{couponlist.packageDiscountPrice}}</view>
      </view>
    </block>
    <block wx:if="{{hasShuttleService}}">
      <view class="mp-crown-order__shuttle {{isOnlinePay ? '' : 'mp-crown-order__shuttle--active'}}">
        <view class="mp-crown-order__shuttle-text {{pets && pets[0].times[0] && pets[0].times[0].time ? 'active': ''}}">
          洗护接送服务
          <image class="mp-crown-order__shuttle-free"
                 src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.TrIelI9N3iFp3d48d02a5589d05610e246011a2320eb.svg"></image>
        </view>
        <view wx:if="{{state.transfer.receiveTime || state.transfer.sendTime}}"
              class="mp-crown-order__shuttle-link-detail" bind:tap="onShuttleTap">
          <!-- 查看详情 -->
          {{state.transfer.communityName}} {{state.transfer.address}}
        </view>
        <view wx:else
              class="mp-crown-order__shuttle-link {{pets && pets[0].times[0] && pets[0].times[0].time ? 'active': ''}}"
              bind:tap="onShuttleTap">
          请选择小区、时间
        </view>
        <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></text>
      </view>
    </block>
  </view>
  <!-- 洗护预约不使用积分 -->
  <!--  <view class="mp-crown-order__card" wx:if="{{false}}">-->
  <!--    <block>-->
  <!--      <view class="mp-crown-order__vip-info-wrapper">-->
  <!--        <view class="mp-crown-order__vip-info-main-content">-->
  <!--          <view class="mp-crown-order__vip-info-main-content-title">使用积分</view>-->
  <!--          <image src="http://img3.petkit.cn/images/f0d4f8a3ca494954bb14c261eb48ebc3"-->
  <!--                 class="mp-crown-order__vip-info-icon"></image>-->
  <!--          <block wx:if="{{isOnlinePay && couponlist.pointDeduction > 0}}">-->
  <!--            <text class="mp-crown-order__vip-info-text">可使用{{couponlist.pointDeduction}}积分<text-->
  <!--              class="mp-crown-order__vip-info-text-emphasize">抵扣{{couponlist.pointDeductionPrice}}元</text></text>-->
  <!--          </block>-->
  <!--          <block wx:if="{{!isOnlinePay && offlinePoints > 0}}">-->
  <!--            <text class="mp-crown-order__vip-info-text">可使用{{offlinePoints}}积分<text-->
  <!--              class="mp-crown-order__vip-info-text-emphasize">抵扣{{offlinePoints / 100}}元</text></text>-->
  <!--          </block>-->
  <!--          <view class="PETKIT icon-description mp-crown-order__vip-info-description" catch:tap="descriptionShow"></view>-->
  <!--        </view>-->
  <!--        <view class="mp-crown-order__vip-info-check" bindtap="onSelectUseVipPointsTap">-->
  <!--          <form-check class="mp-crown-order__service-paytype__wrapper-check" checked="{{isUseVipPoints}}"></form-check>-->
  <!--        </view>-->
  <!--      </view>-->
  <!--    </block>-->
  <!--  </view>-->
  <view class="mp-crown-order__card">
    <view class="mp-crown-order__service-remark">
      <view class="mp-crown-order__service-remark-text">备注：</view>
      <input class="mp-crown-order__service-remark-input" placeholder-class="mp-crown-order__service-remark-placeholder"
             placeholder="亲亲可以在这里备注!" bindinput="onRemarkInput"/>
    </view>
  </view>
  <view class="mp-crown-order__notice mp-crown-order__card {{isIphone ? 'mp-crown-order__notice--isIphone' : ''}}">
    <view class="mp-crown-order__notice-title">关于违约、改约</view>
    <view class="mp-crown-order__notice-text">* 迟到不保留预约资格</view>
    <view class="mp-crown-order__notice-text">* 请提前2小时致电门店修改预约，并调整时间</view>
  </view>
  <!-- 非线上支付 -->
  <view class="mp-crown-order__confirm safe-area-inset-bottom" wx:if="{{!isOnlinePay}}">
    <block wx:if="{{!isUseStorePoints}}">
      <view class="mp-crown-order__confirm__wrapper">
        <view
          class="mp-crown-order__confirm__wrapper-top {{isIphone ? 'mp-crown-order__confirm__wrapper-top--isIphone' : ''}}">
          <view class="mp-crown-order__confirm-text">预约金额：</view>
          <view class="mp-crown-order__confirm-currency">{{currencySymbol}}</view>
          <view class="mp-crown-order__confirm-amount">{{bookingPaymentTypes.STORE_NO_DEPOSIT ? '0' : '5'}}</view>
        </view>
      </view>
    </block>

    <block wx:if="{{isUseStorePoints}}">
      <view class="mp-crown-order__confirm__wrapper">
        <view
          class="mp-crown-order__confirm__wrapper-top {{isIphone ? 'mp-crown-order__confirm__wrapper-top--isIphone' : ''}}">
          <view class="mp-crown-order__confirm-text">合计：</view>
          <view class="mp-crown-order__confirm-currency">{{currencySymbol}}</view>
          <view class="mp-crown-order__confirm-amount">{{offlinePrice}}</view>
        </view>
      </view>
    </block>
    <view class="mp-crown-order__confirm-btn {{isIphone ? 'mp-crown-order__confirm-btn--isIphone' : ''}}"
          bind:tap="onConfirmTap">提交订单
    </view>
  </view>
  <block wx:if="{{isOnlinePay}}">
    <!-- 线上支付不使用优惠 -->
    <view class="mp-crown-order__confirm safe-area-inset-bottom"
          wx:if="{{!couponlist.isUseOnlineCoupon && !couponlist.isUseCoupon && !couponlist.isUsePrepurchase && selectPaymentType === 'WX' }}">
      <view class="mp-crown-order__confirm__wrapper">
        <view
          class="mp-crown-order__confirm__wrapper-top {{isIphone ? 'mp-crown-order__confirm__wrapper-top--isIphone' : ''}}">
          <view class="mp-crown-order__confirm-text">合计：</view>
          <view class="mp-crown-order__confirm-currency">{{currencySymbol}}</view>
          <view class="mp-crown-order__confirm-amount" wx:if="{{!isUseStorePoints}}">{{ couponlist.totalPrice}}</view>
          <view class="mp-crown-order__confirm-amount" wx:if="{{isUseStorePoints}}">{{ totalPrice}}</view>
        </view>
      </view>
      <view class="mp-crown-order__confirm-btn {{isIphone ? 'mp-crown-order__confirm-btn--isIphone' : ''}}"
            bind:tap="onConfirmTap">提交订单
      </view>
    </view>
    <!-- 线上支付使用优惠 -->
    <view class="mp-crown-order__confirm safe-area-inset-bottom"
          wx:if="{{couponlist.isUseOnlineCoupon || couponlist.isUseCoupon || couponlist.isUsePrepurchase || selectPaymentType !== 'WX' }}">
      <view class="mp-crown-order__confirm__wrapper">
        <view
          class="mp-crown-order__confirm__wrapper-top {{isIphone ? 'mp-crown-order__confirm__wrapper-top--isIphone' : ''}}">
          <view class="mp-crown-order__confirm-text">合计：</view>
          <view class="mp-crown-order__confirm-currency">{{currencySymbol}}</view>
          <view class="mp-crown-order__confirm-amount" wx:if="{{!isUseStorePoints}}">
            <text
              wx:if="{{selectPaymentType === 'WX' && isUseVipDiscount}}">{{couponlist.totalPrice - couponlist.vipDiscountPrice}}</text>
            <text wx:if="{{selectPaymentType === 'WX' && !isUseVipDiscount}}">{{couponlist.totalPrice}}</text>
            <text wx:if="{{selectPaymentType !== 'WX' && (isUseVipDiscount || use)}}">{{couponlist.vipPrice}}</text>
            <text wx:if="{{selectPaymentType !== 'WX' && !isUseVipDiscount && !use}}">{{couponlist.totalPrice}}</text>
          </view>
          <view class="mp-crown-order__confirm-amount" wx:if="{{isUseStorePoints}}">
            <!-- {{selectPaymentType === 'WX' ? totalPrice : vipPrice}} -->
            <text wx:if="{{selectPaymentType === 'WX' && isUseVipDiscount}}">{{totalPriceWithVip}}</text>
            <text wx:if="{{selectPaymentType === 'WX' && !isUseVipDiscount}}">{{totalPrice}}</text>
            <text wx:if="{{selectPaymentType !== 'WX' && isUseVipDiscount}}">{{vipPrice}}</text>
            <text wx:if="{{selectPaymentType !== 'WX' && !isUseVipDiscount}}">{{pointPrice}}</text>
          </view>
        </view>
      </view>
      <view class="mp-crown-order__confirm-btn {{isIphone ? 'mp-crown-order__confirm-btn--isIphone' : ''}}"
            bind:tap="onConfirmTap">
        提交订单
      </view>
    </view>
  </block>
</view>
<!-- 订单详情页面 -->
<view class="mp-crown-order-detail__container mp-crown-order__container" wx:if='{{id !== 0}}'>
  <view class="mp-crown-order__background"></view>
  <view class="mp-crown-order__user">
    <view class='mp-crown-order__user-heading-container'>
      <view class="mp-crown-order__user-hint">确认预约人信息</view>
      <view class='mp-crown-order__user-cancel-booking' wx:if="{{bookingListDetail.bookingStatus === 'cancel'}}">
        取消预约
      </view>
      <view class='mp-crown-order__user-cancel-booking' wx:if="{{bookingListDetail.bookingStatus === 'booking'}}">
        预约成功
      </view>
    </view>
    <view class="mp-crown-order__user-name">{{bookingListDetail.name}}</view>
    <view class="mp-crown-order__user-mobile">{{bookingListDetail.mobile}}</view>
    <view class="mp-crown-order__user-address" wx:if="{{bookingListDetail.address}}">{{bookingListDetail.address}}
    </view>
  </view>
  <view class="mp-crown-order__pet mp-crown-order__card">
    <view class="mp-crown-order__pet-img-wrapper">
      <image class="mp-crown-order__pet-img" src="{{bookingListDetail.petImagePath}}"></image>
    </view>
    <view class="mp-crown-order__pet-title">本次服务宠物</view>
    <view class="mp-crown-order__pet-name">{{bookingListDetail.pets[0].name}}</view>
  </view>
  <view class='mp-crown-order__refund-progress mp-crown-order__card' wx:if='{{bookingListDetail.isRefund}}'>
    <view class='mp-crown-order__refund-progress-title'>退款进度</view>
    <view class='mp-crown-order__refund-progress-bar'>
      <view
        class="mp-crown-order__circle {{bookingListDetail.status === 'REPAY'? 'mp-crown-order__circle_active':''}}"></view>
      <view
        class="mp-crown-order__bar-border {{bookingListDetail.status === 'REFUNDING'? 'mp-crown-order__bar-border_active':''}}"></view>
      <view
        class="mp-crown-order__circle {{bookingListDetail.status === 'REFUNDING'? 'mp-crown-order__circle_active':''}}"></view>
      <view
        class="mp-crown-order__bar-border {{bookingListDetail.status === 'REFUNDED' || bookingListDetail.status === 'DISCARD'? 'mp-crown-order__bar-border_active':''}}"></view>
      <view
        class="mp-crown-order__circle {{bookingListDetail.status === 'REFUNDED' || bookingListDetail.status === 'DISCARD'? 'mp-crown-order__circle_active':''}}"></view>
    </view>
    <view class='mp-crown-order__refund-progress-status'>
      <view
        class="mp-crown-order__refund-progress-status-string mp-crown-order__refund-progress-status-string-left {{bookingListDetail.status === 'REPAY'? 'refund-progress-status-string_active':''}}">
        取消预约
      </view>
      <view
        class="mp-crown-order__refund-progress-status-string {{bookingListDetail.status === 'REFUNDING'? 'refund-progress-status-string_active':''}}">
        微信退款中
      </view>
      <view
        class="mp-crown-order__refund-progress-status-string mp-crown-order__refund-progress-status-string-right {{bookingListDetail.status === 'REFUNDED' || bookingListDetail.status === 'DISCARD'? 'refund-progress-status-string_active':''}}">
        退款成功
      </view>
    </view>
  </view>
  <view class="mp-crown-order__service mp-crown-order__card">
    <view class="mp-crown-order__service-store">
      <view class="mp-crown-order__service-store-name">
        PETKIT小佩宠物{{bookingListDetail.storeName}}
      </view>
      <text class="PETKIT icon-location1"></text>
      {{bookingListDetail.storeAddress}}
      <view class="mp-crown-order__service-store-address"></view>
    </view>
    <view class="mp-crown-order__border"></view>
    <view wx:for="{{bookingListDetail.pets[0].services}}" wx:key="imgUrl"
          wx:for-item="service" class="mp-crown-order__service-item mp-crown-order__service-item_only">
      <view class="mp-crown-order__service-item-img-wrapper">
        <image class="mp-crown-order__service-item-img" src="{{service.imgUrl}}"></image>
      </view>
      <view class="mp-crown-order__service-item-desc">
        <view class="mp-crown-order__service-item-desc-top">
          <view class="mp-crown-order__service-item-desc-top-name">{{service.name}}</view>
          <view class="mp-crown-order__service-item-desc-top-price" wx:if="{{service.vipPrice}}">
            {{currencySymbol}}{{service.vipPrice}}
          </view>
          <view class="mp-crown-order__service-item-desc-top-price" wx:if="{{service.storePrice}}">
            {{currencySymbol}}{{service.storePrice}}
          </view>
        </view>
        <view class="mp-crown-order__service-item-desc-bottom">
          <view class="mp-crown-order__service-item-desc-bottom-type">{{service.seriviceSkuPetCategory}}</view>
          <view class="mp-crown-order__service-item-desc-bottom-check">
            <text class="mp-crown-order__service-item-desc-bottom-check-content">{{bookingListDetail.storeName}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="mp-crown-order__service-date">
      <view class="mp-crown-order__service-date-text">预约服务时间</view>
      <view>{{bookingListDetail.serviceStartTime}}</view>
    </view>
    <view class="mp-crown-order__service-duration">
      <view class="mp-crown-order__service-duration-text">服务结束</view>
      <view>预计{{bookingListDetail.serviceEndTime}}结束</view>
    </view>
    <view class="mp-crown-order__service-remark">
      <view class="mp-crown-order__service-remark-text">
        备注：{{bookingListDetail.remark || '无'}}
      </view>
    </view>
  </view>
  <view class="mp-crown-order__card mp-crown-order__card--price">
    <!-- 线上全额支付使用优惠券或预购券：原先的摒弃掉是因为现在需要展示具体使用的折扣、券名、价格 -->
    <view class="mp-crown-order__service-get-coupon"
          wx:if="{{bookingListDetail.source === 'WECHAT' && bookingListDetail.serviceCoupon}}">
      <view class="mp-crown-order__service-get-coupon-limit"
            wx:if="{{bookingListDetail.serviceCoupon[0].consumptionType === 'LIMIT' || bookingListDetail.serviceCoupon[0].consumptionType === 'CASH_VOUCHER'}}">
        满减
      </view>
      <view class="mp-crown-order__service-get-coupon-discount"
            wx:if="{{bookingListDetail.serviceCoupon[0].consumptionType === 'DISCOUNT'}}">
        折扣
      </view>
      <view class="mp-crown-order__service-get-coupon-text">
        {{bookingListDetail.serviceCoupon[0].couponName}}
      </view>
      <view class="mp-crown-order__service-get-coupon-price">
        -{{currencySymbol}}{{bookingListDetail.serviceCoupon[0].price}}
      </view>
    </view>
    <view class="mp-crown-order__service-purchase" wx:if="{{bookingListDetail.prePurechase}}">
      <view class="mp-crown-order__service-purchase-text">预购券</view>
      <view class="mp-crown-order__service-purchase-price">
        -{{currencySymbol}}{{bookingListDetail.prePurechase[0].price}}
      </view>
    </view>
    <view class="mp-crown-order__service-coupon" wx:if="{{bookingListDetail.coupon}}">
      <!-- 在会员折上折版本上以下文案会修改 -->
      <view class="mp-crown-order__service-coupon-text">
        {{ bookingListDetail.coupon[0].consumptionType === 'CASH_VOUCHER' ? '代金券' : bookingListDetail.coupon[0].consumptionType === 'POINT_LIMIT_ADD' ? '积分券' : '优惠券'}}
      </view>
      <view class="mp-crown-order__service-coupon-price"
            wx:if="{{bookingListDetail.coupon[0].consumptionType !== 'POINT_LIMIT_ADD'}}">
        -{{currencySymbol}}{{bookingListDetail.coupon[0].price}}
      </view>
      <view class="mp-crown-order__service-coupon-price"
            wx:if="{{bookingListDetail.coupon[0].consumptionType === 'POINT_LIMIT_ADD'}}">
        +{{bookingListDetail.coupon[0].points}}积分
      </view>
    </view>
    <view class="mp-crown-order__service-vip-points" wx:if="{{bookingListDetail.points}}">
      <view class="mp-crown-order__service-vip-points-text">积分抵扣</view>
      <view class="mp-crown-order__service-vip-points-price">
        -{{currencySymbol}}{{bookingListDetail.points / 100}}</view>
    </view>
    <view class="mp-crown-order__service-vip-points" wx:if="{{bookingListDetail.packageDiscount}}">
      <view class="mp-crown-order__service-vip-points-text">套餐优惠</view>
      <view class="mp-crown-order__service-vip-points-price">
        -{{currencySymbol}}{{bookingListDetail.packageDiscount}}</view>
    </view>
    <view class="mp-crown-order__service-vip-points" wx:if="{{bookingListDetail.vipDiscountPrice}}">
      <view class="mp-crown-order__service-vip-points__icon">
        <view class="mp-crown-order__service-vip-points-text">会员立减</view>
        <image class="mp-crown-order__service-vip-points-url"
               src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.6JWSPudlNpoJ6a3cce43085d7a67a76ec55b782eba2b.svg"/>
      </view>
      <view class="mp-crown-order__service-vip-points-price">
        -{{currencySymbol}}{{bookingListDetail.vipDiscountPrice}}</view>
    </view>
    <view class="mp-crown-order__service-booking-price">
      <view class="mp-crown-order__service-booking-price-title">
        {{bookingListDetail.source === 'WECHAT' ? '实付金额' : '预约金'}}
      </view>
      <view class='mp-crown-order__service-booking-price-status'>
        {{bookingListDetail.bookingFeeMsg}}
      </view>
    </view>
  </view>

  <!-- 联系商家 -->
  <view bindtap="onShowContactDialog"
        class="mp-crown-order__contact">
    <text class="PETKIT icon-phonecall mp-crown-order__contact--icon"></text>
    <text class="mp-crown-order__contact--text">联系商家</text>
  </view>

  <view wx:if="{{state.transfer.receiveTime || state.transfer.sendTime}}"
        class="mp-crown-order__card mp-crown-order__shuttle">
    <form-check checked="{{true}}"></form-check>
    <view class="mp-crown-order__shuttle-text">洗护接送服务</view>
    <view class="mp-crown-order__shuttle-link-detail" bind:tap="onShuttleTap">
      <!-- 查看详情 -->
      {{state.transfer.communityName}} {{state.transfer.address}}
    </view>
    <text class="PETKIT icon-right-arrow mp-crown-order-icon-right-arrow"></text>
  </view>
  <view
    class="{{bookingListDetail.payment.fullPaid === 1 ? 'mp-crown-order__pay mp-crown-order__card mp-crown-order__card__pb-sm' : ' mp-crown-order__pay mp-crown-order__card'}}"
    wx:if="{{bookingListDetail.source !== 'PC' && bookingListDetail.bookingFeeMsg !== '未支付'}}">
    <view class="mp-crown-order__pay-title">预约单信息</view>
    <view class="mp-crown-order__pay-purchase" wx:if="{{bookingListDetail.payment.orderId}}">
      <view class="mp-crown-order__pay-purchase-text">预约单编号</view>
      <view class="mp-crown-order__pay-purchase-number">{{bookingListDetail.payment.orderId}}</view>
    </view>
    <view class="mp-crown-order__pay-purchase" wx:if="{{bookingListDetail.payment.tradeNo}}">
      <view class="mp-crown-order__pay-purchase-text">交易支付号</view>
      <view class="mp-crown-order__pay-purchase-number">{{bookingListDetail.payment.tradeNo}}</view>
    </view>
    <view class="mp-crown-order__pay-purchase">
      <view class="mp-crown-order__pay-purchase-text">付款时间为</view>
      <view class="mp-crown-order__pay-purchase-number">
        {{bookingListDetail.payment.paymentDate || '--'}}
      </view>
    </view>
    <view class="mp-crown-order__border"></view>
    <view class="mp-crown-order__pay-type" wx:if="{{bookingListDetail.payment.paymentSystem === 'WECHAT'}}">
      <text class="PETKIT icon-WeChatpay mp-crown-order__pay-icon"></text>
      <view class="mp-crown-order__pay-method">微信支付</view>
    </view>
    <view class="mp-crown-order__pay-type" wx:else>
      <text class="mp-crown-order__pay-vip PETKIT icon-VIP"></text>
      <view class="mp-crown-order__pay-method">会员余额支付</view>
    </view>
    <view class="mp-crown-order__pay-title">支付方式</view>
    <view class="mp-crown-order__pay-text" wx:if="{{bookingListDetail.payment.fullPaid !== 1}}">
      * 以上支付为预约定金，预约本身无需付费。定金会在消费完成后原路返还到支付账号上
    </view>
  </view>
  <view class="mp-crown-order__notice mp-crown-order__card">
    <view class="mp-crown-order__notice-title">关于违约、改约</view>
    <view class="mp-crown-order__notice-text">* 迟到不保留预约资格</view>
    <view class="mp-crown-order__notice-text">* 请提前2小时致电门店修改预约，并调整时间</view>
  </view>
  <view class='mp-crown-order__buttons safe-area-inset-bottom' wx:if="{{bookingListDetail.bookingStatus === 'create'}}">
    <view class='mp-crown-order__buttons-button mp-crown-order__buttons-button-cancel' catchtap="cancelOrder">
      取消订单
    </view>
    <view class='mp-crown-order__buttons-button mp-crown-order__buttons-button-pay' catchtap="immediatePay">
      立即支付
    </view>
  </view>
  <view class='mp-crown-order__buttons'
        wx:if="{{bookingListDetail.status === 'PAID' || bookingListDetail.status === 'BOOKING'}}">
    <view class='mp-crown-order__buttons-button mp-crown-order__buttons-button-cancel' catchtap="cancelBookedOrder">
      取消订单
    </view>
  </view>
</view>

<van-popup
  show="{{ integralShow }}"
  position="bottom"
  custom-style="border-radius: 30rpx 30rpx 0rpx 0rpx"
  z-index="{{1000}}"
  bind:click-overlay="closeIntegralPopup"
>
  <view class="mp-crown-order__vip-info-popup">
    <view class="mp-crown-order__vip-info-popup-top">
      <view class="mp-crown-order__vip-info-popup-top-desc">积分说明</view>
    </view>
    <view class="mp-crown-order__vip-info-popup-bottom">
      <view class="mp-crown-order__vip-info-popup-bottom-use">获取条件：</view>
      <block wx:for="{{getRules}}" wx:key="{{index}}">
        <view class="mp-crown-order__vip-info-popup-bottom-ol">
          {{item}}
        </view>
      </block>
      <view class="mp-crown-order__vip-info-popup-bottom-use">使用规则</view>
      <block wx:for="{{useRules}}" wx:key="{{index}}">
        <view class="mp-crown-order__vip-info-popup-bottom-ol">
          {{item}}
        </view>
      </block>
    </view>
    <button class="mp-crown-order__vip-info-popup-button" bind:tap="closeIntegralPopup">确定</button>
  </view>
</van-popup>

<van-popup show="{{isVipDiscountPopup}}" round="true" position="bottom" bind:close="onClose"
           custom-class="mp-crown-order__vip-info-preferentialPopup">
  <view class="mp-crown-order__vip-info-preferentialPopup">
    <view class="mp-crown-order__vip-info-preferentialPopup-title">小佩会员专享</view>
    <view class="mp-crown-order__vip-info-preferentialPopup-check">
      <view class="mp-crown-order__vip-info-preferentialPopup-check-text">不使用优惠</view>
      <view class="mp-crown-order__vip-info-preferentialPopup-check-circle" catch:tap="useClick" data-use="notuse">
        <view class="PETKIT icon-xuanzhong mp-crown-order__vip-info-preferentialPopup-check-circle-selected"
              wx:if="{{notuse}}"></view>
        <view class="PETKIT icon-circle mp-crown-order__vip-info-preferentialPopup-check-circle-noselected"
              wx:if="{{use}}"></view>
      </view>
    </view>
    <view class="mp-crown-order__vip-info-preferentialPopup-check">
      <view class="mp-crown-order__vip-info-preferentialPopup-check-text">
        <text>省{{couponlist.vipDiscountPrice}}元：</text>
        <text wx:if="{{couponlist.vipDiscountRate}}">小佩会员专享{{couponlist.vipDiscountRate / 10}}折</text>
      </view>
      <view class="mp-crown-order__vip-info-preferentialPopup-check-circle" catch:tap="useClick" data-use="use">
        <view class="PETKIT icon-xuanzhong mp-crown-order__vip-info-preferentialPopup-check-circle-selected"
              wx:if="{{use}}"></view>
        <view class="PETKIT icon-circle mp-crown-order__vip-info-preferentialPopup-check-circle-noselected"
              wx:if="{{notuse}}"></view>
      </view>
    </view>
    <view class="mp-crown-order__vip-info-preferentialPopup-button" catch:tap="closeVipDiscountPopup">完成</view>
  </view>
</van-popup>

<!-- 2020-07 切卡 -->
<van-popup show="{{ showDialog }}"
           position="bottom"
           bind:click-overlay="toggleDialog"
           custom-class="bottom">
  <view class="mp-common-pay-picker__wrapper">
    <view class="mp-common-pay-picker__header">选择付款方式
      <text class="PETKIT icon-close mp-common-pay-picker__header-icon" bindtap="toggleDialog"></text>
    </view>
    <view class="mp-common-pay-picker__body">
      <view class="mp-common-pay-picker__body-tips">优先使用您所选的付款方式，如付款失败将尝试使用其他方式完成付款</view>
      <view class="mp-common-pay-picker__body-content">

        <!-- 用户卡列表 -->
        <view
          class="mp-common-pay-picker__body-content-item {{!item.selected ? 'mp-common-pay-picker__body-content-item--disabled' : ''}}"
          wx:for="{{bookingBalanceCardList}}"
          data-card="{{item}}"
          wx:key="cardId"
          bindtap="togglePaymentType"
          data-type="VIP">
          <!-- 禁用的遮罩层 -->
          <view class="mp-common-pay-picker__body-content-item-left">
            <text wx-if="{{item.cardType === 'CHARGE'}}"
                  class="PETKIT icon-balance-pay mp-common-pay-picker__body-content-item-icon-balance"></text>
            <text wx-if="{{item.cardType === 'BENEFIT_CARD'}}"
                  class="PETKIT icon-WeChatpay mp-common-pay-picker__body-content-item-icon-wechat"></text>
          </view>
          <view class="mp-common-pay-picker__body-content-item-right">
            <view class="mp-common-pay-picker__body-content-item-right__pay--card">
              <view class="mp-common-pay-picker__body-content-item-right-title">
                <block wx-if="{{item.cardType === 'CHARGE'}}">
                  <text
                    wx:if="{{item.name}}">{{item.name}}
                    ({{(item.storeId === -1 || item.current === true) ? '小佩门店通用' : item.storeName}})
                  </text>
                  <text
                    wx:else>{{(item.storeId === -1 || item.current === true) ? '小佩门店通用' : item.storeName}}</text>
                </block>
                <text wx-if="{{item.cardType === 'BENEFIT_CARD'}}">微信支付 | {{item.name}}</text>
              </view>
              <!-- TODO 需要根据卡片类型调整 -->
              <view wx-if="{{item.cardType === 'CHARGE'}}"
                    class="mp-common-pay-picker__body-content-item-right-subtitle">
                当前会员账户余额 {{currencySymbol}}{{item.amount || 0.00}} 元
              </view>
              <view wx-if="{{item.cardType === 'BENEFIT_CARD'}}"
                    class="mp-common-pay-picker__body-content-item-right-subtitle">
                有效期：{{item.effectTimeStr}} ~ {{item.expireTimeStr}}
              </view>

            </view>
            <view class="mp-common-pay-picker__body-content-item-right__pay--radio">
              <view
                class="mp-common-pay-picker__body-content-item-right__pay--radio--item {{(selectPaymentType === 'VIP'  &&  activeBookingBalanceCard.cardId === item.cardId && activeBookingBalanceCard.selected) ? 'radio-checked' : ''}}">
                <view class="mp-common-pay-picker__body-content-item-right__pay--radio--item__inner"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 微信支付 -->
        <view class="mp-common-pay-picker__body-content-item"
              bindtap="togglePaymentType"
              data-card="{{}}"
              data-type="WX">
          <view class="mp-common-pay-picker__body-content-item-left">
            <text class="PETKIT icon-WeChatpay mp-common-pay-picker__body-content-item-icon-wechat"></text>
          </view>
          <view class="mp-common-pay-picker__body-content-item-right">
            <view class="mp-common-pay-picker__body-content-item-right__pay--card">
              <view class="mp-common-pay-picker__body-content-item-right-title">微信支付</view>
              <view class="mp-common-pay-picker__body-content-item-right-subtitle">
                选择后使用此支付方式全额支付
              </view>
            </view>
            <view class="mp-common-pay-picker__body-content-item-right__pay--radio">
              <view
                class="mp-common-pay-picker__body-content-item-right__pay--radio--item {{selectPaymentType === 'WX' ? 'radio-checked' : ''}}">
                <view class="mp-common-pay-picker__body-content-item-right__pay--radio--item__inner"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</van-popup>

<van-popup
  show="{{isShowStorePointsPopup}}"
  position="bottom"
  bind:click-overlay="onHiddenStorePointsPopup"
  custom-class="bottom"
>
  <view class="mp-common-pay-picker-points">
    <van-radio-group value="{{ storePointsRadio }}" bind:change="onChangeStorePointsRadio">
      <van-cell-group>
        <van-cell title="不使用积分" clickable data-name="0" bind:click="onClickStorePointsRadio">
          <van-radio slot="right-icon" name="0" checked-color="#fe002a"/>
        </van-cell>
        <van-cell title="使用{{deductionPoint}}积分抵扣{{pointDeductionPrice}}" clickable data-name="1"
                  bind:click="onClickStorePointsRadio">
          <van-radio slot="right-icon" name="1" disabled="{{pointDeductionPrice === 0}}" checked-color="#fe002a"/>
        </van-cell>
      </van-cell-group>
    </van-radio-group>
  </view>
</van-popup>

<contact-trader show="{{isContactDialog}}"
                serviceInfo="{{warehouseContactInfo}}"></contact-trader>
