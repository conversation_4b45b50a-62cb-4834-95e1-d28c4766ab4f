import {
  acs,
  getStorage
} from '@petkit/redux';

import {
  bindActionCreators,
} from 'redux';
import {
  connect,
} from 'utils/weapp-redux';
import {
  keyBy
} from 'lodash-es';

const pageConfig = {
  data: {},


  onShow() {
    this.requestPaidOrderList();
  },

  // 查看订单详情
  onToOrderDetail({
    currentTarget: {
      dataset: {
        id
      }
    }
  }) {
     wx.navigateTo({
      url: `/booking/pages/order/order?id=${id}`,
    });
  },

  // 点击‘取消订单’
  onCancelBookedOrder(ev) {
    const bookingId = ev.currentTarget.dataset.id;
    const that = this;
    wx.showModal({
      title: "",
      content: "是否确定取消该预约的订单？",
      cancelColor: "#2034B5",
      confirmColor: "#2034B5",
      success(res) {
        if (res.confirm) {
          that.requestCancelBookedOrder(
            { bookingId },
            {
              success: () => {
                that.requestPaidOrderList();
              },
            }
          );
        }
      }
    });
  },

  // 点击'再次购买'
  onBuyAginOrder() {
    wx.redirectTo({
      url: '/pages/booking/booking',
    });
  }
};

const mapStateToData = state => ({
  paidOrderList: state.service.booking.paidOrderList,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  requestPaidOrderList: acs.service.booking.requestPaidOrderList,
  requestCancelBookedOrder: acs.service.booking.requestCancelBookedOrder,
  requestBookingList: acs.service.bookingList.requestBookingListAll,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
