@import "config/theme";

@mixin mp-crown-order-paid-theme($theme) {
  $foreground: map-get($theme, foreground);
  .mp-crown-order-paid {
    &__container {
      margin: u(48) u(30) 0 u(30);
    }

    &__item {
      width: u(690);
      height: u(350);
      background-color: #ffffff;
      border-radius: u(16);
      margin-bottom: u(32);
    }

    &__background {
      width: u(48);
      height: u(48);
      background:linear-gradient(270deg,rgba(128,188,253,1) 0%,rgba(80,142,253,1) 100%);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: u(8);

      & > text {
        font-size: u(28);
        color: #ffffff;
      }
    }

    &__info {
      padding: u(28) u(30) u(32) u(30);

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-title {
          display: flex;
          align-items: center;
          justify-content: center;

          & > text {
            font-size: u(24);
            font-weight: 600;
            font-family:PingFangSC-Semibold,PingFang SC;
          }
        }

        &-status {
          color: map-get($foreground, secondary-text);
          font-size: u(24);
          font-weight: 400;
        }
      }

      &-center {
        display: flex;
        align-items: center;
        padding: u(32) 0;
        border-bottom: u(2) solid #F1F1F1;

        &-icon {
          width: u(80);
          height: u(80);
          border-radius: u(8);
          border: 1px solid #E3E3E3;
          margin-right: u(16);
        }

        &-url {
          width: u(80);
          height: u(80);
        }

        &__item {
          width: 100%;

          &-date {
            font-size: u(24);
            font-weight: 600;
            line-height: u(64);
          }

          &-message {
            color: #B0B0B0;
            font-size: u(24);
            font-family:PingFangSC-Regular,PingFang SC;
            display: flex;
            align-items: center;
          }
        }
      }

      &-footer {
        display: flex;
        float: right;
        margin-right: u(30);

        &-delete {
          width: u(156);
          height: u(40);
          border-radius: u(24);
          border: u(2) solid #F1F1F1;
          line-height: u(40);
          margin-right: u(16);
          color: map-get($foreground, hint-text);
          text-align: center;
        }

        &-buy {
          width: u(156);
          height: u(40);
          border-radius: u(24);
          border: 1px solid map-get($foreground, secondary-text);
          line-height: u(40);
          color: map-get($foreground, secondary-text);
          text-align: center;
        }
      }
    }

    &__record {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-top: u(340);

      &-icon {
        width: u(176);
        height: u(176);
        margin-bottom: u(60);
      }

      &-url {
        width: u(176);
        height: u(176);
      }

      &-word {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-family:PingFangSC-Regular,PingFang SC;

        & > text:nth-child(1) {
          font-size: u(32);
          font-weight: 600;
          line-height: u(48);
          color: map-get($foreground, hint-text);
        }

        & > text:nth-child(2) {
          color: #9397A2;
          font-size: u(24);
          line-height: u(64);
        }
      }
    }

    &__border {
      width: u(2);
      background-color: #B0B0B0;
      height: u(20);
      margin: 0 u(12);
    }
  }
}

@include mp-crown-order-paid-theme($lucky-theme);
