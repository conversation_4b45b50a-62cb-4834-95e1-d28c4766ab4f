<view class="mp-crown-order-paid__container">
  <view class="mp-crown-order-paid__show" wx:if="{{paidOrderList && paidOrderList.length}}">
    <view class="mp-crown-order-paid__item" wx:for="{{paidOrderList}}" wx:key='id' wx:for-item="item" data-id="{{item.id}}">
      <view class="mp-crown-order-paid__info"  data-id="{{item.id}}" bindtap="onToOrderDetail">
        <view class="mp-crown-order-paid__info-header">
          <view class="mp-crown-order-paid__info-header-title">
            <view class="mp-crown-order-paid__background">
              <text class="PETKIT icon-ic_bathtub"></text>
            </view>
            <text>专业洗护</text>
          </view>
          <view class="mp-crown-order-paid__info-header-status">预约成功</view>
        </view>
        <view class="mp-crown-order-paid__info-center">
          <view class="mp-crown-order-paid__info-center-icon">
            <image class="mp-crown-order-paid__info-center-url" src="{{item.serviceImagePath}}"/>
          </view>
          <view class="mp-crown-order-paid__info-center__item">
            <view class="mp-crown-order-paid__info-center__item-date">{{item.creationDate}}</view>
            <view class="mp-crown-order-paid__info-center__item-message">
              <text>{{item.bookingSpec}}</text>
              <view class="mp-crown-order-paid__border"></view>
              <text>消费{{item.price}}元</text>
            </view>
          </view>
        </view>
      </view>
      <view class="mp-crown-order-paid__info-footer">
        <view class="mp-crown-order-paid__info-footer-delete" wx:if="{{item.status && item.status !== 'FINISHED'}}" data-id="{{item.id}}" bindtap="onCancelBookedOrder">取消订单</view>
        <view class="mp-crown-order-paid__info-footer-buy" bindtap="onBuyAginOrder">再次购买</view>
      </view>
    </view>
  </view>
  <view class="mp-crown-order-paid__record" wx:if="{{!paidOrderList.length}}">
    <view class="mp-crown-order-paid__record-icon">
      <image class="mp-crown-order-paid__record-url" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs-JpTx5VVwF3zG4XnCxok_4.NR0RcdGN81Y38480a52e31651cfefd962b9204dccd04.svg"/>
    </view>
    <view class="mp-crown-order-paid__record-word">
      <text>暂无记录</text>
      <text>您还没有预约服务记录</text>
    </view>
  </view>
</view>
