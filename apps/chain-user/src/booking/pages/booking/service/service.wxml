<view class="page wp-crown-booking-service__wrapper">
  <view class="wp-crown-booking-service__swiper-container">
    <swiper class="wp-crown-booking-service__swiper"
      indicator-dots="{{indicatorDots}}"
      indicator-color="{{indicatorColor}}"
      indicator-active-color="{{indicatorActiveColor}}"
      autoplay="{{autoplay}}"
      interval="{{interval}}"
      duration="{{duration}}"
      circular="{{circular}}"
      bindchange="onSwiperChanged">
      <block wx:for="{{imgUrls}}" wx:key="index">
        <swiper-item>
          <image src="{{item}}" mode="aspectFill" class="slide-image"/>
        </swiper-item>
      </block>
    </swiper>

    <view class="wp-crown-booking-service__swiper-dots">
      <block wx:for="{{imgUrls}}" wx:key="unique">
        <view class="wp-crown-booking-service__swiper-dot {{index == swiperCurrent ? ' active' : ''}}"></view>
      </block>
    </view>
  </view>
  <view class="wp-crown-booking-service__container">
    <view class="wp-crown-booking-service__store-card">
      <view class="wp-crown-booking-service__store-name">{{storeInfo.shellName}}</view>
      <view class="wp-crown-booking-service__store-booking-time">预约时间：{{storeInfo.bookStartTime}} - {{storeInfo.bookEndTime}}</view>
      <view class="wp-crown-booking-service__store-info">
        <text class="PETKIT icon-mendian wp-crown-booking-service__store-icon"></text>
        <!-- <image class="wp-crown-booking-service__store-icon" src="{{storeIcon}}" /> -->
        <view class="wp-crown-booking-service__store-status">{{storeInfo._operationState}}</view>
        <view class="wp-crown-booking-service__store-line">|</view>
        <view class="wp-crown-booking-service__store-time">营业时间：{{storeInfo.businessStartTime}} - {{storeInfo.businessEndTime}}</view>
      </view>
      <view class="wp-crown-booking-service__store-contact">
        <text class="PETKIT icon-location1"></text>
        <!-- <image class="wp-crown-booking-service__store-location-icon" src="{{locationIcon}}"></image> -->
        <view class="wp-crown-booking-service__store-location">
          <view class="wp-crown-booking-service__store-location-address">{{storeInfo.address}}</view>
          <view class="wp-crown-booking-service__store-location-distance" wx:if="{{distance}}">距您 {{_distance}}</view>
        </view>
        <view class="wp-crown-booking-service__store-contact-line"></view>
        <!-- <image class="wp-crown-booking-service__store-contact-phone" src="{{phoneIcon}}" bindtap="showPhones"></image> -->
        <text class="PETKIT icon-dianhua wp-crown-booking-service__store-contact-phone" bind:tap="showPhones"></text>

      </view>
    </view>

    <view class="wp-crown-booking-service__pet-card">
      <view class="wp-crown-booking-service__pet-title">选择宠物</view>
      <view class="wp-crown-booking-service__pet-list-tip" wx:if="{{isInfoFull !== undefined && !isInfoFull}}">宠物信息不完整，点击管理宠物补充信息</view>
      <view class="wp-crown-booking-service__pet-list">
        <view
          class="wp-crown-booking-service__pet-item {{pet.selected ? 'active' : ''}}"
          bindtap="selectPet"
          wx:for="{{petList}}"
          wx:key="pet"
          wx:for-item="pet"
          data-id="{{pet.id}}">
          <text class="PETKIT icon-xuanzhong wp-crown-booking-service__pet-selected"></text>
          <!-- <image class="wp-crown-booking-service__pet-selected" src="{{selectedIcon}}" /> -->
          <image class="wp-crown-booking-service__pet-avatar" src="{{pet.avatar}}" />
          <view class="wp-crown-booking-service__pet-name">{{pet.name}}</view>
        </view>
        <view class="wp-crown-booking-service__pet-item" bindtap="createPet">
          <!-- <image class="wp-crown-booking-service__pet-selected" src="{{selectedIcon}}" /> -->
          <text class="PETKIT icon-xuanzhong wp-crown-booking-service__pet-selected"></text>
          <image class="wp-crown-booking-service__pet-avatar" src="https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJsw2WyXzqK_2AB5wVYlmGU6I.uAl5gfOuIFyIab51d87f087932542909648719ac1722.svg" />
          <view class="wp-crown-booking-service__pet-name">管理宠物</view>
        </view>
      </view>
    </view>

    <view class="wp-crown-booking-service__service-card">
      <view class="wp-crown-booking-service__service-title">选择服务</view>
      <view class="wp-crown-booking-service__service-subtitle">请选择洗护服务项目</view>
      <view class="wp-crown-booking-service__service-list">
        <view class="wp-crown-booking-service__service-item {{service.selected ? 'active' : ''}}"
          wx:for="{{availableServiceList}}"
          wx:key="service"
          wx:for-item="service"
          data-id="{{service.id}}"
          bindtap="selectService">
          <view class="wp-crown-booking-service__service-item__container">
            <text class="PETKIT icon-xuanzhong wp-crown-booking-service__service-selected"></text>
            <!-- <image class="wp-crown-booking-service__service-selected" src="{{selectedIcon}}" /> -->
            <image class="wp-crown-booking-service__service-icon" src="{{service.icon}}" />
            <view class="wp-crown-booking-service__service-name">{{service.name}}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="wp-crown-booking-service__time-card">
      <view class="wp-crown-booking-service__time-title">选择时间</view>
      <view class="wp-crown-booking-service__time-day-list">
        <view
          class="wp-crown-booking-service__time-day-item {{day.selected ? 'active' : ''}}"
          wx:for="{{days}}" wx:for-item="day" wx:key="day"
          data-time="{{day.timeStampt}}" bindtap="selectDay">
          <view class="wp-crown-booking-service__time-day-container">
            <image class="wp-crown-booking-service__time-day-discount-icon" src="{{discountIcon}}" wx:if="{{day.hasDiscount && !day.isFull}}"></image>
            <image class="wp-crown-booking-service__time-day-full-icon" src="{{fullIcon}}" wx:if="{{day.isFull}}"></image>
            <view class="wp-crown-booking-service__time-day-date" wx:if="{{!day.isToday}}">{{day.date}}</view>
            <view class="wp-crown-booking-service__time-day-name">{{day.dayName}}</view>
          </view>
        </view>
      </view>
      <view class="wp-crown-booking-service__time-list">
        <view
          class="wp-crown-booking-service__time-item {{duration.canBook ? 'canbook' : ''}} {{duration.selected ? 'active' : ''}}"
          wx:for="{{availableDurations}}" wx:for-item="duration" wx:key="duration"
          data-canbook="{{duration.canBook}}" data-time="{{duration.dayTime}}"
          bindtap="selectDuration">
          <view class="wp-crown-booking-service__time-item__container">
            <image class="wp-crown-booking-service__time-item-discount-icon" wx:if="{{duration.canBook && duration.hasDiscount}}" src="{{duration.discountIcon}}"></image>
            <image class="wp-crown-booking-service__time-item-selected" src="{{selectedIcon}}" wx:if="{{duration.selected}}" />
            <view class="wp-crown-booking-service__time-item-time">{{duration.time}}</view>
            <view class="wp-crown-booking-service__time-item-status">{{duration.statusName}}</view>
          </view>
        </view>
      </view>
      <view class="wp-crown-booking-service__time-tips">* 迟到15分钟以上，门店不保留预约资格，请合理安排您的时间</view>
    </view>

  </view>

  <button
   class="wp-crown-booking-service__booking-button"
   bindtap="gotoBook" disabled="{{isBookingFull}}">立即预约</button>
</view>
