import {
  acs,
  store
} from '@petkit/redux';
import {getDistanceByLatAndLon} from 'utils';
import {cloneDeep, map} from 'lodash-es';

import {bindActionCreators} from 'redux';
import {connect} from 'utils/weapp-redux';

const pageConfig = {
  data: {
    imgUrls: [],
    indicatorDots: true,
    autoplay: true,
    interval: 1500,
    duration: 1000,
    circular: true,
    indicatorColor: 'rgba(255, 255, 255, .7)',
    indicatorActiveColor: '#23b9de',
    swiperCurrent: 0,

    currentLatitude: 0,
    currentLongitude: 0,
    distance: 0,

    firstSelectPet: true,
    petList: [],
    selectedPetIdList: {},
    selectedServiceSkus: [],

    days: [],
    availableDurations: [],

    isInfoFull: true,

    // storeIcon,
    // locationIcon,
    // phoneIcon,
    // addPetIcon,
    // selectedIcon,
    fullIcon:
      'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs6E6C_DEtZT9kYCH-A3JdfU.GSiNZHU2uZgo2957a5f218b5dea7f1e2813285d8e6d0.png',
    discountIcon:
      'https://petkit-img3.oss-cn-hangzhou.aliyuncs.com/img/wxa8116ce1a098af54.o6zAJs6E6C_DEtZT9kYCH-A3JdfU.o5dUYZRRjRbfedd2d0fa0fb8c1bf7749bd128d4fab34.png'
  },

  // onLoad(option) {
  //
  //   this.data.isAlreadyReady = true;
  //   this.getBookingDays();
  // },
  /**
   * overwrite onLoad
   */
  onLoad() {
    wx.redirectTo({
      url: '/pages/booking/booking',
    });
  },

  onUnload: function () {
    this._resetPageData();
    this._resetSub();
  },

  onShow() {
    if (!this.data.isAlreadyReady) {
      this.resetBookingTimes();
      this.resetBookingDays();
      this._resetServiceTime();
    }

    this.requestBookingPetList();

    this._getLocation();
    this._onSub();
  },

  onHide() {
    this.data.isAlreadyReady = false;
    this.data.firstSelectPet = true;
    this._resetSub();
  },

  onSwiperChanged(ev) {
    this.setData({
      swiperCurrent: ev.detail.current // current 改变时会触发 change 事件
    });
  },

  _resetServiceTime() {
    const serviceBooking = cloneDeep(this.data.serviceBooking);
    map(serviceBooking.pets, pet => {
      map(pet.services, service => {
        service.resetServiceTimes();
        service.resetServiceDiscount();
      });
    });
    this.saveServiceBooking(serviceBooking);
  },

  showPhones() {
    wx.showActionSheet({
      itemList: this.data.storeInfo.phones,
      success: res => {
        const phone = this.data.storeInfo.phones[res.tapIndex];
        wx.makePhoneCall({
          phoneNumber: phone,
          success: res => console.log(res),
          fail: err => console.log(err)
        });
      },
    });
  },

  selectPet({currentTarget: {dataset: {id}}}) {
    this.resetBookingDays();
    this.resetBookingTimes();

    this.selectBookingPet(id);

    const serviceBooking = cloneDeep(this.data.serviceBooking);
    const petList = this.data.petList;
    const selectedPets = petList.filter(item => item.selected);
    const selectedServiceSkuList = this.data.availableServiceList.filter(item => item.selected);


    serviceBooking.setPets(selectedPets, selectedServiceSkuList);

    this.saveServiceBooking(serviceBooking);
  },

  createPet() {
    wx.navigateTo({
      url: '/pet/pages/pet-list/pet-list'
    });
  },

  selectService({currentTarget: {dataset: {id}}}) {
    const availableServiceList = this.data.availableServiceList.map(item => {
      item.selected = false;
      if (item.id === id) {
        item.selected = true;
      }
      return item;
    });
    this.saveAvailableServiceList(availableServiceList);

    const serviceBooking = cloneDeep(this.data.serviceBooking);
    const petList = this.data.petList;
    const selectedPets = petList.filter(item => item.selected);
    const selectedServiceSkuList = this.data.availableServiceList.filter(item => item.selected);

    serviceBooking.setPets(selectedPets, selectedServiceSkuList);

    this.saveServiceBooking(serviceBooking);

    this.requestAvailableDayAndTooltips();
  },

  selectDay(ev) {
    if (ev) {
      const selectedPets = this.data.petList.filter(item => item.selected);
      const selectedServiceSkuList = this.data.availableServiceList.filter(item => item.selected);

      if (!selectedPets.length) {
        this.showToast({
          message: '请先选择宠物！',
        });

        return ;
      }

      if (!selectedServiceSkuList.length) {
        this.showToast({
          message: '请先选择服务！',
        });

        return ;
      }

      const timeStampt = ev.currentTarget.dataset.time;

      const days = cloneDeep(this.data.days).map(item => {
        item.selected = false;
        if (item.timeStampt === timeStampt) {
          item.selected = true;
        }
        return item;
      });

      this.saveBookingDays(days);

      this.resetBookingTimes();

      this._requestAvailableDurations();
    }

  },

  selectDuration(ev) {
    let time = 0;
    let canbook = ev.currentTarget.dataset.canbook;
    if (ev) {
      time = ev.currentTarget.dataset.time;
    }
    if (!canbook) {
      this.showToast({
        message: '当前时间已满，请选择其它时间！'
      });
      return;
    }

    const duration = this.data.availableDurations
      .map(item => {
        item.selected = false;
        item.dayTime === time && (item.selected = true);
        return item;
      })
      .find(item => item.selected);

    const serviceBooking = cloneDeep(this.data.serviceBooking);
    map(serviceBooking.pets, pet => {
      map(pet.services, service => {
        service.setServiceTimes(time);
        service.setServiceDiscount(duration);
      });
    });

    this.saveBookingTimes(this.data.availableDurations);

    this.saveServiceBooking(serviceBooking);
  },

  gotoBook() {
    const serviceBooking = this.data.serviceBooking;
    if (!serviceBooking.pets.length) {
      this.showToast({
        message: '请选择宠物'
      });
      return;
    }
    if (!serviceBooking.pets[0].services.length) {
      this.showToast({
        message: '请选择服务'
      });
      return;
    }
    if (
      !serviceBooking.pets[0].services[0].serviceStartTime ||
      !serviceBooking.pets[0].services[0].serviceEndTime
    ) {
      this.showToast({
        message: '请选择服务时间'
      });
      return;
    }

    wx.navigateTo({
      url: '/booking/pages/order/order'
    });
  },

  _requestBookingAvailableDay(needToTip) {
    const serviceBooking = this.data.serviceBooking;

    if (!serviceBooking.pets[0] || !serviceBooking.pets[0].petId) {
      if (needToTip) {
        this.showToast({
          message: '请先选择宠物'
        });
      }
      return;
    }

    if (
      !serviceBooking.pets[0].services[0] ||
      !serviceBooking.pets[0].services[0].serviceSkuId
    ) {
      if (needToTip) {
        this.showToast({
          message: '请先选择服务'
        });
      }
      return;
    }

    const storeId = this.data.storeInfo.id;
    const start = this.data.days[0].timeStampt;
    const end = this.data.days[this.data.days.length - 1].timeStampt;
    const serviceSkuId = serviceBooking.pets[0].services[0].serviceSkuId;

    if (start && end && serviceSkuId) {
      this.requestBookingAvailableDay({
        storeId,
        start,
        end,
        serviceSkuId
      });
    }
  },

  _requestAvailableDurations() {
    const selectedServiceSkus = this.data.availableServiceList.filter(item => item.selected);
    const storeInfo = this.data.storeInfo;
    const day = this.data.days.find(item => item.selected).timeStampt;
    this.requestBookingAvailableDuration({
      services: selectedServiceSkus.map(item => item.id),
      storeId: storeInfo.id,
      day
    });
  },

  _getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: result => {
        this.data.currentLatitude = result.latitude;
        this.data.currentLongitude = result.longitude;

        this._setDistance();
      },
    });
  },

  _setDistance() {
    const currentLatitude = this.data.currentLatitude;
    const currentLongitude = this.data.currentLongitude;
    const storeLatitude = this.data.storeInfo.latitude;
    const storeLongitude = this.data.storeInfo.longitude;
    const distance = Number(
      Math.round(
        getDistanceByLatAndLon(
          currentLatitude,
          currentLongitude,
          storeLatitude,
          storeLongitude,
          3
        ) * 1000
      )
    );
    let _distance = '-';
    if (distance < 1000) {
      _distance = distance + 'm';
    } else if (distance > 1000) {
      _distance = (distance/1000).toFixed(2) + 'km';
    }

    this.setData({
      distance,
      _distance
    });
  },

  _getSelectedPetList() {
    const selectedPetList = [];
    for (let index in this.data.selectedPetIdList) {
      const selectedPet = this.data.petList.find(item => item.id === index);
      selectedPet && selectedPetList.push(selectedPet);
    }
    return selectedPetList;
  },

  _onSub() {
    this.data.sub = store.subscribe(() => {
      let state = store.getState();
      let petList = this.data.petList;
      let isInfoFull = true;
      petList.forEach(item => {
        if (item.selected) {
          isInfoFull = this._judgeInfoIsFull(item);
        }
      });
      this.setData({
        isInfoFull: isInfoFull
      });
    });
  },

  _resetSub() {
    if (this.data.sub) {
      this.data.sub();
    }
  },

  _resetPageData() {
    this.resetBookingPetList();
    this.resetAvailableServiceList();
    this.resetBookingTimes();
    this.resetBookingDays();
  },

  _judgeInfoIsFull(selectedPet) {
    let isInfoFull = true;
    if (selectedPet.category && selectedPet.breed && selectedPet.breed !== '' && selectedPet.specs && selectedPet.specs.length > 0) {
      if (selectedPet.category.toLowerCase() === 'dog') {
        isInfoFull = selectedPet.breedSpecAttrId && selectedPet.breedCategorySpecAttrId && selectedPet.specs.length >= 3;
      } else if (selectedPet.category.toLowerCase() === 'cat') {
        isInfoFull = selectedPet.breedSpecAttrId && selectedPet.breedCategorySpecAttrId && selectedPet.specs.length >= 2;
      }
    } else {
      isInfoFull = false;
    }
    return isInfoFull;
  }

};

const mapStateToData = state => ({
  serviceSkuPOList: state.service.booking.serviceSkuPOList,
  availableServiceList: state.service.booking.availableServiceList,
  storeInfo: state.wp.store.warehouse.storeInfo,
  petList: state.user.pet.bookingPetList,
  serviceBooking: state.service.booking.serviceBooking,
  availableDurationPOs: state.service.booking.availableDurationPOs,
  days: state.service.booking.bookingDays,
  availableDurations: state.service.booking.bookingTimes,
  isBookingFull: state.service.booking.isBookingFull,
  selectedServiceSkus: state.service.booking.selectedServiceSkus,
  imgUrls: state.wp.store.warehouse.storeInfo._images
});

const mapDispatchToPage = dispatch =>
  bindActionCreators(
    {
      showToast: acs.global.toast.show,
      requestBookingAvailableDuration: acs.service.booking.requestBookingAvailableDuration,
      getAvailableServiceList: acs.service.booking.getAvailableServiceList,

      getPetList: acs.user.pet.getPetList,
      resetBookingPetList: acs.user.pet.resetBookingPetList,

      saveAvailableServiceList: acs.service.booking.saveAvailableServiceList,
      resetAvailableServiceList: acs.service.booking.resetAvailableServiceList,

      saveServiceBooking: acs.service.booking.saveServiceBooking,

      requestBookingAvailableDay: acs.service.booking.requestBookingAvailableDay,
      getBookingDays: acs.service.booking.getBookingDays,
      saveBookingDays: acs.service.booking.saveBookingDays,
      resetBookingDays: acs.service.booking.resetBookingDays,

      saveBookingTimes: acs.service.booking.saveBookingTimes,
      resetBookingTimes: acs.service.booking.resetBookingTimes,

      requestBookingPetList: acs.user.pet.requestBookingPetList,

      selectBookingPet: acs.user.pet.selectBookingPet,
      requestAvailableDayAndTooltips: acs.service.booking.requestAvailableDayAndTooltips,
    },
    dispatch
  );

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
