@import 'config/theme';

@mixin common-card-style {
  background: #fff;
  padding: u(24);
  border-radius: u(10);
  margin-top: u(24);
}

@mixin wp-crown-booking-service-theme($theme) {
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  view {
    box-sizing: border-box;
  }

  .wp-crown-booking-service {
    &__wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }

    &__swiper {
      height: 100%;

      swiper-item {
        image {
          width: u(750);
          height: u(360);
        }
      }
      &-container {
        position: relative;
        height: u(360);
      }

      &-dots {
        position: absolute;
        left: 0;
        right: 0;
        top: u(40);
        display: flex;
        justify-content: center;
      }

      &-dot {
        margin: 0 u(8);
        width: u(14);
        height: u(14);
        background: rgba(255, 255, 255, .7);
        border-radius: u(8);
        transition: all .6s;

        &.active {
          background: #23b9de;
        }
      }
    }

    &__container {
      position: relative;
      margin: u(-60) u(24) u(24);
    }

    &__store {
      &-card {
        @include common-card-style();
        padding-top: u(28);
        margin-top: 0;
      }

      &-name {
        color: #2f2f2f;
        font-size: u(36);
        line-height: 1em;
        margin-bottom: u(26);
      }

      &-booking-time {
        color: #575d6a;
        font-size: u(24);
        line-height: 1em;
      }

      &-info {
        display: flex;
        align-items: center;
        border-bottom: u(2) solid #ebebeb;
        padding: u(24) 0;
        margin-bottom: u(30);
      }

      &-icon {
        width: u(30);
        height: u(30);
        margin-right: u(16);
        display: flex;
        align-items: center;
        color: rgba(147,151,162,1);
      }

      &-status {
        color: #2f2f2f;
        font-size: u(28);
        margin-right: u(16);
      }

      &-line {
        color: #b0b0b0;
        margin-right: u(24);
      }

      &-time {
        color: #9397a2;
        font-size: u(24);
      }

      &-contact {
        display: flex;
        align-items: center;
        padding-right: u(12);

        &-line {
          width: u(2);
          height: u(48);
          background: #f1f1f1;
          margin-right: u(36);
        }

        &-phone {
          width: u(36);
          height: u(36);
          color: #2034b5;
          font-size: u(40);
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }
      }

      &-location {
        width: calc(100% - #{u(28)} - #{u(16)} - #{u(12)} - #{u(36)} - #{u(40)});

        &-address {
          color: #2f2f2f;
          font-size: u(28);
        }

        &-distance {
          color: #9397a2;
          font-size: u(24);
        }

        &-icon {
          width: u(28);
          height: u(34);
          align-self: flex-start;
          margin-right: u(16);
        }
      }
    }

    &__pet {
      $margin-right: #{u(34)};
      $pet-selector: &;

      &-card {
        @include common-card-style();
        overflow: hidden;
      }

      &-title {
        color: #2f2f2f;
        font-size: u(28);
        line-height: 1em;
        margin-bottom: u(16);
        font-weight: 500;
      }

      &-list {
        display: flex;
        justify-content: flex-start;
        padding-top: u(30);
        width: 100%;
        flex-wrap: wrap;
        &-tip {
          font-size: 12px;
          color: #23B9DE;
        }
      }

      &-item {
        box-sizing: border-box;
        width: calc((100% - (#{$margin-right} * 3)) / 4);
        position: relative;
        margin-right: $margin-right;
        margin-bottom: u(24);
        white-space: normal;
        border: u(4) solid transparent;
        text-align: center;

        &:nth-of-type(4n) {
          margin-right: 0;
        }

        &.active {
          #{$pet-selector}-selected {
            display: block;
          }

          #{$pet-selector}-avatar {
            border-color: #23b9de;
          }
        }
      }

      &-selected {
        display: none;
        width: u(32);
        height: u(32);
        position: absolute;
        top: u(-12);
        right: 0;
        color: mat-color($accent);
        font-size: u(40);
        // display: flex;
        // align-items: center;
      }

      &-avatar {
        width: u(112);
        height: u(112);
        margin-bottom: u(20);
        border-radius: 50%;
        border: u(4) solid transparent;
      }

      &-name {
        text-align: center;
        color: #2f2f2f;
        font-size: u(24);
        line-height: 1em;
      }
    }

    &__service {
      $service-selector: &;

      &-card {
        @include common-card-style();
      }

      &-title {
        color: #2f2f2f;
        font-size: u(28);
        line-height: 1em;
        margin-bottom: u(16);
        font-weight: 500;
      }

      &-subtitle {
        color: #9397a2;
        font-size: u(24);
        line-height: 1em;
        margin-bottom: u(24);
      }

      &-list {
        display: flex;
        align-items: flex-start;
        border-top: u(2) solid #ebebeb;
        padding-top: u(30);
      }

      &-item {
        $margin-right: u(34);

        display: flex;
        align-items: flex-start;
        margin-right: $margin-right;
        width: calc((100% - (#{$margin-right} * 3)) / 4);

        &__container {
          align-self: flex-start;
          text-align: center;
          position: relative;
        }

        &.active {
          #{$service-selector}-selected {
            display: block;
          }

          #{$service-selector}-icon {
            border: u(4) solid #23b9de;
          }
        }
      }

      &-selected {
        display: none;
        width: u(32);
        height: u(32);
        position: absolute;
        top: u(-10);
        right: u(-10);
        color: mat-color($accent);
        font-size: u(40);
      }

      &-icon {
        width: u(112);
        height: u(112);
        margin-bottom: u(20);
        border-radius: u(10);
        border: 1px solid #e3e3e3;
      }

      &-name {
        text-align: center;
        color: #2f2f2f;
        font-size: u(24);
        line-height: 1em;
      }
    }

    &__time {
      $time-selector: &;

      &-card {
        @include common-card-style();
      }

      &-title {
        color: #2f2f2f;
        font-size: u(28);
        line-height: 1em;
        font-weight: 500;
        padding-bottom: u(24);
        border-bottom: 1px solid #ebebeb;
      }

      &-day {
        &-list {
          display: flex;
          align-items: flex-start;
          padding: u(24) 0 0;
          border-bottom: 1px solid #f1f1f1;
          overflow-x: auto;
          overflow-y: hidden;
          height: u(140);
          box-sizing: border-box;
        }

        &-item {
          $margin-right: u(34);

          box-sizing: border-box;
          display: flex;
          align-items: center;
          position: relative;
          margin-right: $margin-right;
          width: u(100);
          height: 100%;
          border-bottom: u(4) solid transparent;

          &.active {
            border-color: #23b9de;
            color: #23b9de;
          }
        }

        &-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          width: u(100);
          height: 100%;
        }

        &-discount-icon {
          position: absolute;
          top: 0;
          right: 0;
          width: u(26);
          height: u(26);
        }

        &-full-icon {
          position: absolute;
          top: 0;
          right: 0;
          width: u(26);
          height: u(26);
        }

        &-date {
          width: u(100);
          text-align: center;
        }

        &-name {
          width: u(100);
          text-align: center;
        }
      }

      &-list {
        display: flex;
        justify-content: flex-start;
        padding-top: u(30);
        width: 100%;
        flex-wrap: wrap;
      }

      &-item {
        $time-item-selector: &;
        $margin: u(26);

        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: $margin;
        margin-bottom: $margin;
        width: calc((100% - (#{$margin} * 3)) / 4);
        border: u(4) solid transparent;
        border-radius: u(10);
        height: u(112);
        background: #f1f1f1;

        &:nth-child(4n) {
          margin-right: 0;
        }

        &__container {
          display: flex;
          flex-direction: column;
          justify-items: center;
          justify-content: center;
          align-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
        }

        &.canbook {
          background: #fff;
          border-color: #e3e3e3;

          #{$time-item-selector}-time {
            color: #2f2f2f;
          }

          #{$time-item-selector}-status {
            color: #2f2f2f;
          }
        }

        &.active {
          border-color: #23b9de;

          #{$time-item-selector}-time {
            color: #23b9de;
          }

          #{$time-item-selector}-status {
            color: #23b9de;
          }
        }

        &-discount-icon {
          position: absolute;
          left: u(-2);
          top: u(-2);
          width: u(50);
          height: u(20);
        }

        &-selected {
          position: absolute;
          top: u(-12);
          right: u(-12);
          width: u(32);
          height: u(32);
        }

        &-time {
          color: #b0b0b0;
          font-size: u(28);
          line-height: 1em;
          margin-bottom: u(16);
        }

        &-status {
          color: #b0b0b0;
          font-size: u(24);
          line-height: 1em;
        }
      }

      &-tips {
        color: #b0b0b0;
        font-size: u(24);
      }
    }

    &__booking-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: u(100);
      color: #fff;
      font-size: u(36);
      background: linear-gradient(#37ddf3, #2cb3d5);
      border-radius: 0;
      &::after {
        border-radius: 0;
      }
    }
  }
}

@include wp-crown-booking-service-theme($lucky-theme);
