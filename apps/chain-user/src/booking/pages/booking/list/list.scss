@import 'config/theme';

@mixin wp-crown-booking-service-list-theme($theme) {
  $primary: map-get($theme, primary);
  $foreground: map-get($theme, foreground);

  .wp-crown-booking-service-list {
    &__wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }

    &_cancel {
      color: #9397a2;
    }

    &__show {
      margin: u(24) u(30) 0 u(30);

      &--empty {
        font-size: u(24);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #B0B0B0;
        text-align: center;
        margin-top: u(8);
        padding-bottom: u(12);
      }

      &--more {
        text-align: center;
        margin: u(8) auto u(30);
        padding: 0 u(40);
        height: u(56);
        display: inline-block;
        line-height: u(56);
        background-color: rgba(242, 244, 248, 1);
        border-radius: u(28);
        border: u(2) solid mat-color($primary);
        color: mat-color($primary);
      }
    }

    &__tabs {
      width: 100%;
      border-top: #EBEBEB 1px solid;
      border-bottom: #ebebeb 1px solid;
      text-align: center;
      height: u(80);
      line-height: u(80);
      display: flex;
      flex-flow: row;
      justify-content: space-between;
      background-color: #fff;
      position: fixed;
      top: 0;
    }

    &__tab {
      margin: auto;
      color: #575d6a;
      font-size: u(28);

      &_active {
        color: mat-color($primary);
        border-bottom: mat-color($primary) u(4) solid;
      }
    }

    &__details {
      width: 100%;
      text-align: center;
      padding-top: u(84);
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }

    &__detail {
      box-sizing: border-box;
      width: u(710);
      border-radius: u(8);
      background-color: #fff;
      text-align: center;
      margin: u(20) auto;
      padding-bottom: u(16);

      &-heading {
        display: flex;
        align-items: stretch;
        box-sizing: border-box;

        padding: u(20);

        &-icon image {
          width: u(64);
          height: u(64);
          margin-right: u(10);
          flex: 0 0 u(64);
        }

        &-content {
          flex: 1;
          text-align: left;

          &-title {
            font-size: u(24);
          }

          &-time {
            color: #9397a2;
            font-size: u(24);
          }
        }

        &-status {
          font-size: u(24);
          color: mat-color($primary);
          flex: 0 0 u(160);
          display: flex;
          justify-content: flex-end;

          &_cancel {
            color: mat-color($primary);
          }
        }
      }

      &-info {
        width: 100%;
        display: flex;
        box-sizing: border-box;
        align-items: stretch;
        padding: 0 u(20);
        height: u(116);

        &-img {
          width: u(112);
          height: u(112);
          border-radius: u(8);
          border: #d8d8d8 u(2) solid;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: u(16);

          box-sizing: border-box;
          padding: u(16);
        }

        &-img image {
          width: 100%;
          height: 100%;
        }

        &-body {
          flex: 1;
          margin-right: u(58);
        }

        &-content {
          display: flex;
          justify-content: flex-start;
          width: 100%;

          &-title {
            font-size: u(28);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-align: left;
            line-height: u(30);
          }

          &-time {
            font-size: u(28);
          }
        }

        &-service-type {
          display: flex;
          margin-top: u(8);
          margin-bottom: u(8);
          font-size: u(24);
          color: #9397a2;
          text-align: left;
        }

        &-discount {
          display: flex;
          justify-content: center;
          border-radius: u(4);
          width: u(116);
          height: u(24);
          background: #ffeff2;
          font-size: u(16);
          color: #ff587d;
          text-align: center;

          &_cancel {
            color: #9397a2;
            background: #f1f1f1;
          }
        }

        &-container {
          &-department {
            display: flex;
            justify-content: flex-end;
            font-size: u(28)
          }

          &-price {
            display: flex;
            justify-content: flex-end;
            font-size: u(24);
            color: map-get($foreground, hint-text);
          }
        }
      }

      &-buttons {
        display: flex;
        box-sizing: border-box;
        padding-top: u(24);
        padding-bottom: u(24);
        justify-content: flex-end;
        height: u(114);

        &-button {
          box-sizing: border-box;
          border-radius: u(26);
          width: u(144);
          height: u(56);
          font-size: u(24);
          margin-right: u(20);

          display: flex;
          align-items: center;
          justify-content: center;

          &-cancel {
            font-size: u(24);
            color: #575d6a;
            border: #d4d4d4 u(2) solid;
          }

          &-pay {
            font-size: u(24);
            color: #fff;
            background-color: mat-color($primary);
          }
        }
      }
    }

    &__item {
      width: u(690);
      height: u(350);
      background-color: #ffffff;
      border-radius: u(16);
      margin-bottom: u(24);
    }

    &__info {
      padding: u(28) u(30) u(32) u(30);

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-title {
          display: flex;
          align-items: center;
          justify-content: center;

          & > text {
            font-size: u(24);
            font-weight: 600;
            font-family: PingFangSC-Semibold, PingFang SC;
          }
        }

        &-status {
          font-size: u(24);
          font-weight: 400;
        }
      }

      &-center {
        display: flex;
        align-items: center;
        padding: u(32) 0;
        border-bottom: u(2) solid #F1F1F1;

        &-icon {
          width: u(80);
          height: u(80);
          border-radius: u(8);
          border: 1px solid #E3E3E3;
          margin-right: u(16);
        }

        &-url {
          width: u(80);
          height: u(80);
        }

        &__item {
          &-date {
            font-size: u(24);
            font-weight: 600;
            line-height: u(64);
          }

          &-message {
            color: #B0B0B0;
            font-size: u(24);
            font-family: PingFangSC-Regular, PingFang SC;
            display: flex;
            align-items: center;
          }
        }
      }

      &-footer {
        display: flex;
        float: right;
        margin-right: u(30);

        &-delete {
          width: u(144);
          height: u(48);
          border-radius: u(48);
          border: u(2) solid mat-color($primary);
          line-height: u(48);
          margin-right: u(16);
          color: mat-color($primary);
          text-align: center;
        }

        &-buy {
          width: u(144);
          height: u(48);
          border-radius: u(48);
          border: 1px solid mat-color($primary);
          line-height: u(48);
          background-color: mat-color($primary);
          color: #ffffff;
          text-align: center;
        }
      }
    }

    &__background {
      width: u(48);
      height: u(48);
      background: linear-gradient(270deg, rgba(128, 188, 253, 1) 0%, rgba(80, 142, 253, 1) 100%);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: u(8);

      & > text {
        font-size: u(28);
        color: #ffffff;
      }
    }

    &__record {
      display: flex;
      align-items: center;
      flex-direction: column;

      &-icon {
        width: u(176);
        height: u(176);
        margin-bottom: u(60);
      }

      &-url {
        width: u(176);
        height: u(176);
      }

      &-word {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-family: PingFangSC-Regular, PingFang SC;

        & > text:nth-child(1) {
          font-size: u(32);
          font-weight: 600;
          line-height: u(48);
          color: map-get($foreground, hint-text);
        }

        & > text:nth-child(2) {
          color: #9397A2;
          font-size: u(24);
          line-height: u(64);
        }
      }
    }
  }
}

@include wp-crown-booking-service-list-theme($lucky-theme);
