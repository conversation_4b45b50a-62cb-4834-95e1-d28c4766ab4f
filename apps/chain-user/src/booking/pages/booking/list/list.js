import {acs} from '@petkit/redux';
import {bindActionCreators} from 'redux';
import {connect} from 'utils/weapp-redux';
import {timeDiffByYear} from 'utils';
import {
  commonService
} from "../../../../services/common.service";

const pageConfig = {
  data: {
    currentTab: 0,
    pageParams: {
      created: {
        params: {
          'paginator.pageIndex': 1,
          'paginator.pageSize': 10,
          status: 'created',
        },
        data: {
          list: [],
          paginator: {}
        },
        // 是否包含从现在起 一年以前的单子
        olderOrder: false
      },
      booking: {
        params: {
          'paginator.pageIndex': 1,
          'paginator.pageSize': 10,
          status: 'booking',
        },
        data: {
          list: [],
          paginator: {}
        },
        // 是否包含从现在起 一年以前的单子
        olderOrder: false
      },
      canceled: {
        params: {
          'paginator.pageIndex': 1,
          'paginator.pageSize': 10,
          status: 'canceled',
        },
        data: {
          list: [],
          paginator: {}
        },
        // 是否包含从现在起 一年以前的单子
        olderOrder: false
      },
    },
    status: ['created', 'booking', 'canceled'],
    activePageParams: {
      params: {
        'paginator.pageIndex': 1,
        'paginator.pageSize': 10,
        status: '',
      },
      data: {
        list: [],
        paginator: {}
      },
      // 是否包含从现在起 一年以前的单子
      olderOrder: false
    },
  },

  cancelOrder(ev) {
    const bookingId = ev.currentTarget.dataset.id;
    const statusOfBooking = ev.currentTarget.dataset.status;
    const that = this;
    wx.showModal({
      title: '',
      content: '是否确定取消该预约的订单？',
      cancelColor: '#2034B5',
      confirmColor: '#2034B5',
      success(res) {
        if (res.confirm) {
          if (statusOfBooking === 'create') {
            that.requestCancelBookingOrder({bookingId: bookingId}, {
              success: () => {
                that.changeOrderStatus(bookingId, 'cancel');
              },
            });
          } else if (statusOfBooking === 'booking') {
            that.requestCancelBookedOrder({bookingId: bookingId}, {
              success: () => {
                that.changeOrderStatus(bookingId, 'cancel');
              },
            });
          }
        }
      }
    });
  },
  /**
   * @description:手动改变预约订单状态，不通过请求接口
   * @param{number} bookingId 预约单id
   * @param{string} status 要变成的状态
   * **/
  changeOrderStatus(bookingId, status) {
    const statusString = {
      cancel: '已取消',
      booking: '预约成功',
      create: '等待您支付',
    };
    const bookingStatusString = statusString[status];

    // 当前生效的列表
    const activePageParams = this.data.activePageParams;
    activePageParams.data.list.forEach((item) => {
      if (item.id === bookingId) {
        item.bookingStatus = status;
        item.bookingStatusString = bookingStatusString;
      }
    });

    this.setData({
      activePageParams
    })
  },


  immediatePay(ev) {
    const that = this;
    const bookingId = ev.currentTarget.dataset.id;
    this.requestRepayData({bookingId}, {
      success: () => {
        if (this.data.repayObject) {
          that._payBooking({bookingId,...this.data.repayObject});
        }
      },
    });
  },

  // 点击'再次购买'
  onBuyAginOrder() {
    const bookingPageUrl = 'pages/booking/booking';
    if (getCurrentPages().some(item => item.route === bookingPageUrl)) {
      const delta = commonService.getNavigateDeltaNumber(bookingPageUrl);
      wx.navigateBack({
        delta
      });
    } else {
      wx.navigateTo({
        url: '/' + bookingPageUrl
      })
    }
  },

  _payBooking(repayObject) {
    const packageString = repayObject.packageString;
    const params = JSON.parse(packageString);
    const timeStamp = params.timeStamp; //时间戳，自1970年以来的秒数
    const nonceStr = params.nonceStr; //随机串
    const packageInfo = params.package;
    const signType = params.signType;  //微信签名方式:
    const paySign = params.paySign;
    const that = this;
    wx.requestPayment({
      timeStamp,
      nonceStr,
      package: packageInfo,
      signType,
      paySign,
      fail: (error) => {
          wx.reportEvent("payment_error",{
              "wxdata_perf_error_code": 0,
              "wxdata_perf_error_msg": typeof error === "object" ? JSON.stringify(error) : error,
              "wxdata_perf_extra_info1": "apps/chain-user/src/booking/pages/booking/list/list.js _payBooking",
              "wxdata_perf_extra_info2": JSON.stringify(params),
              "wxdata_perf_extra_info3": `${packageString}`
          })
        that.showToast({
          message: '支付失败~'
        });
      }
    });

  },

  onLoad(ev) {
    // 处理重定向逻辑
    if (ev && ev.page) {
      commonService.redirectPage(ev);
      return;
    }

    // 1. 小程序获取路由，获取路由参数type,
    // 2.通过type 来切换 this.data.currentTab;
    let currentTab = 1;
    if (ev.type) {
      currentTab = ev.type;
    }
    this.setData({
      currentTab
    });
  },

  onUnload: function () {
    this._resetSub();
    this.initBookingListState();
  },

  // 页面显示/ 切入前台时触发
  onShow() {
    this._requestBookingList();
  },

  _requestBookingList() {
    // 当前选中tab的状态
    const status = this.data.status[this.data.currentTab];
    // 当前选中tab的对应的请求参数
    const params = this.data.pageParams[status].params;

    // 当前选中的tab对应的响应数据
    let {
      list,
      paginator
    } = this.data.pageParams[status].data;

    // 请求
    this.requestBookingList(params, {
      success: () => {

        let olderOrder = this.data.pageParams[status].olderOrder;
        // 拼接列表数据
        const currentList = this.data.bookingInfoData.list;
        if (currentList && currentList.length && !list.some(item => item.id === currentList[0].id)) {
          const temp = currentList.filter(item => !timeDiffByYear(item.creationDate));

          olderOrder = currentList.length !== temp.length;

          list = list.concat(temp || []);
          // 存储后端分页信息
          paginator = this.data.bookingInfoData.paginator;
        }

        const data = {
          list: currentList.length ? list : [],
          paginator
        };

        const pageParams = this.data.pageParams;
        pageParams[status]['data'] = data;
        pageParams[status]['olderOrder'] = olderOrder;

        this.setData({
          activePageParams: pageParams[status]
        });
      },
    });
  },

  onTurnPage() {
    let pageParams = this.data.pageParams;
    const {
      params,
      data
    } = this.data.activePageParams;

    if (params['paginator.pageIndex'] < data.paginator.totalPage) {
      params['paginator.pageIndex'] += 1;
      pageParams[params.status].params = params;

      this._requestBookingList();
    }
  },

  // 程序从前台进入后台时
  onHide() {
    this._resetSub();
  },

  clickTab(ev) {
    const currentTab = ev.target.dataset.current;
    if (this.data.currentTab === currentTab) {
      return false;
    }
    this.setData({
      currentTab
    });

    this._requestBookingList();
  },

  goToDetail(ev) {
    wx.navigateTo({
      url: `/booking/pages/order/order?id=${ev.currentTarget.dataset.id}`,
    });
  },

  _resetSub() {
    if (this.data.sub) {
      this.data.sub();
    }
  }
};


const mapStateToData = state => ({
  bookingInfoData: state.service.bookingList.bookingInfoData,
  payPackage: state.service.booking.payPackage,
  repayObject: state.service.booking.repayObject,
});

const mapDispatchToPage = dispatch => bindActionCreators({
  requestBookingList: acs.service.bookingList.requestBookingListAll,
  showToast: acs.global.toast.show,
  requestCancelBookingOrder: acs.service.booking.requestCancelBookingOrder,
  requestRepayData: acs.service.booking.requestRepayData,
  initBookingListState: acs.service.bookingList.initBookingListState,
  requestCancelBookedOrder: acs.service.booking.requestCancelBookedOrder,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
