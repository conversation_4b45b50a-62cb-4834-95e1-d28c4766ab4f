<view class="page wp-crown-booking-service-list__wrapper">

  <view class='wp-crown-booking-service-list__tabs'>
    <view class="wp-crown-booking-service-list__tab {{ currentTab == 0 ? 'wp-crown-booking-service-list__tab_active': '' }}" data-current="0" bindtap="clickTab">待支付</view>
    <view class="wp-crown-booking-service-list__tab {{ currentTab == 1 ? 'wp-crown-booking-service-list__tab_active': '' }}" data-current="1" bindtap="clickTab">已预约</view>
    <view class="wp-crown-booking-service-list__tab {{ currentTab == 2 ? 'wp-crown-booking-service-list__tab_active': '' }}" data-current="2" bindtap="clickTab">历史订单</view>
  </view>

  <view class='wp-crown-booking-service-list__details'>
    <view class="wp-crown-booking-service-list__show" wx:if="{{activePageParams.data && activePageParams.data.list && activePageParams.data.list.length}}">
      <view class='wp-crown-booking-service-list__item'
            wx:for='{{activePageParams.data.list}}'
            data-id='{{item.id}}'
            wx:key="id">
        <view class="wp-crown-booking-service-list__info" data-id='{{item.id}}' bindtap='goToDetail'>
          <view class="wp-crown-booking-service-list__info-header">
            <view class="wp-crown-booking-service-list__info-header-title">
              <view class="wp-crown-booking-service-list__background">
                <text class="PETKIT icon-ic_bathtub"></text>
              </view>
              <text>专业洗护</text>
            </view>
            <view class="wp-crown-booking-service-list__info-header-status">{{item.bookingStatusString}}</view>
          </view>
          <view class="wp-crown-booking-service-list__info-center">
            <view class="wp-crown-booking-service-list__info-center-icon">
              <image class="wp-crown-booking-service-list__info-center-url" src="{{item.serviceImagePath}}"/>
            </view>
            <view class="wp-crown-booking-service-list__info-center__item">
              <view class="wp-crown-booking-service-list__info-center__item-date">{{item._creationDate}}</view>
              <view class="wp-crown-booking-service-list__info-center__item-message">
                <text>{{item.bookingSpec}}</text>
                <view class="wp-crown-booking-service-list__border"></view>
                <text>消费{{item.price}}元</text>
              </view>
            </view>
          </view>
        </view>
        <view class="wp-crown-booking-service-list__info-footer"
              data-id='{{item.id}}'
              wx:if="{{item.bookingStatus === 'booking' || item.bookingStatus === 'create' || item.bookingStatus === 'cancel'}}">
          <view class="wp-crown-booking-service-list__info-footer-delete"
                wx:if="{{(item.status && item.status !== 'FINISHED' && item.bookingStatus === 'booking') || (item.bookingStatus === 'create') }}"
                data-id="{{item.id}}"
                data-status="{{item.bookingStatus}}"
                catchtap="cancelOrder">取消订单</view>
          <view class="wp-crown-booking-service-list__info-footer-buy"
                wx:if="{{item.bookingStatus === 'cancel' || item.bookingStatus === 'booking'}}"
                bindtap="onBuyAginOrder">再次购买</view>
          <view class='wp-crown-booking-service-list__info-footer-buy'
                wx:if="{{item.bookingStatus === 'create'}}"
                catchtap="immediatePay"
                data-id='{{item.id}}'>立即支付</view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="wp-crown-booking-service-list__show--more"
            bindtap="onTurnPage"
            wx:if="{{activePageParams.data.list.length < activePageParams.data.paginator.totalNum && !activePageParams.olderOrder}}">查看更多</view>
      <view class="wp-crown-booking-service-list__show--empty" wx:else>{{activePageParams.olderOrder ? '仅显示最近一年的订单' : '已经到底啦~'}}</view>
    </view>
  </view>
   <view class="wp-crown-booking-service-list__record" wx:if="{{!activePageParams.data.list || !activePageParams.data.list.length}}">
     <empty-status type="search"></empty-status>
  </view>
</view>
