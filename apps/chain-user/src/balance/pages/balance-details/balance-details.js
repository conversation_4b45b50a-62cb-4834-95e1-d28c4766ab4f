import {
    bindActionCreators,
} from 'redux';
import {
    acs
} from '@petkit/redux';
import {
    connect
} from 'utils/weapp-redux';

const pageConfig = {
    data: {
        title: '变动详情',
        changeType:'- -',
        tradeAmount:'- -',
        orderNo:'- -',
        storeName:'- -',
        time:'- -',
        refundAmount:null,
        type:null
    },
    onLoad(options) {
        console.log()
        const {changeType,tradeAmount,orderNo,storeName,time,refundAmount,type} = JSON.parse(options.data)
        this.setData({
            changeType,tradeAmount,orderNo,storeName,time,refundAmount,type
        })
    },

};

const mapStateToData = state => ({
currencySymbol: state.wp.store.warehouse.warehouseById.currencySymbol || '￥',
});

const mapDispatchToPage = dispatch => bindActionCreators({

}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
