@import "config/theme";

@mixin wp-balance-details-theme($theme) {
  $primary: map-get($theme, primary);
  $primary-grey-1:#9397A2;
  $primary-grey-2:#F5F5F5;
  $primary-grey-3:#F1F1F1;
  $primary-blue-1:#508EFD;
  $primary-blue-2:#8ABBEF;
  $primary-black-1:#000;
  $primary-black-2:#2F2F2F;
  $primary-red-1:#FF6051;
  $primary-white-1:#FFF;
  $header-height:84;

  .wp-balance-details-warp{
    font-size: u(24);
    width: 100vw;
    height: 100vh;
    background-color:$primary-grey-2;
    box-sizing: border-box;
    padding:u(48) u(20);
    .py-24{
      padding-top: u(24);
      padding-bottom: u(24);
    }
    .py-36{
      padding-top: u(36);
      padding-bottom: u(36);
    }
    .px-36{
      padding-left:u(36);
      padding-right: u(36);
    }
    .px-8{
      padding-top: u(8);
      padding-bottom: u(8);
    }
    &_card{
      background-color:$primary-white-1;
      width: 100%;
      border-radius: u(16);
      margin-bottom: u(24);
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      :last-child{
        padding-bottom: 0 !important;
        &:before{
          content: '';
          border: 0 !important;
        }
      }
      &_content{
        height:u(200);
        width:100%;
        text-align:center;
        :first-child{
          line-height: u(44);
          font-size: u(32);
          font-weight: 400;
        }
        :last-child{
          margin-top: u(16);
          line-height: u(44);
          font-size: u(48);
          font-weight: 500;
        }
      }
      &_box{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        font-size: u(28);
        position:relative;
        font-weight: 400;
        &:before{
          content: '';
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          border-bottom: 1px solid $primary-grey-3;
          transform: scaleX(1.04);
        }
      }
      .refund-box{
        :first-child{
          color: $primary-grey-1;
        }
        :last-child{
          color: $primary-red-1;
        }
      }
      .info-box{
        :first-child{
          color: $primary-grey-1;
        }
        :last-child{
          color: $primary-black-1;
        }
       }
      .type-box{
        :first-child{
          color: $primary-black-1;
        }
        :last-child{
          color: $primary-grey-1;
        }
      }

    }

  }

}

@include wp-balance-details-theme($lucky-theme);
