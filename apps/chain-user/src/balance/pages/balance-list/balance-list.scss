@import "config/theme";

@mixin wp-balance-list-theme($theme) {
  $primary: map-get($theme, primary);
  $primary-grey-1:#9397A2;
  $primary-grey-2:#F5F5F5;
  $primary-grey-3:#F1F1F1;
  $primary-blue-1:#508EFD;
  $primary-blue-2:#8ABBEF;
  $primary-black-1:#000;
  $primary-black-2:#2F2F2F;
  $primary-red-1:#FF6051;
  $primary-white-1:#FFF;
  $header-height:84;

  .wp-balance-list-warp{
    font-size: u(24);
    width: 100vw;
    min-height: 100%;
    background-color:$primary-grey-2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &_header{
      position: fixed;
      z-index: 999;
      top:0;
      left: 0;
      width: 100vw;
      background-color:$primary-white-1;
      height: u($header-height);
      &_con{
        background-color:$primary-white-1;
        height: u($header-height);
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0 u(32) 0 u(50);
      }
      &_left{
        line-height: u(34);
        font-size: u(24);
        color:$primary-blue-1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: sticky;
        z-index: 1000;
        &_right_icon{
          font-size: u(24);
        }
      }
      &_right{
        color:$primary-grey-1;
      }
      &_filter{
        position: absolute;
        z-index: -1;
        left: 0;
        bottom:u(-120);
        background-color: $primary-white-1;
        height: u(120);
        width: 100vw;
        border-top: u(1) solid rgba(245, 245, 245, 0.5);
        transition: .3s ease-in;
        display: flex;
        justify-content: space-around;
        align-items: center;
        &_hidden{
          transform: translateY(u(-120));
        }
        &_item{
          z-index: 998;
          width: u(194);
          height: u(72);
          background-color: $primary-grey-2;
          border-radius: u(4);
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .select{
          background-color: $primary-blue-2;
          color: $primary-white-1;
        }
      }
    }
    &_content{
      margin-top:u($header-height);
      height: calc(100vh - 84rpx);
      &_month{
        width: 100vw;
        height: u(80);
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0  u(24);
        &_title{
          display: flex;
          justify-content: center;
          align-items: center;
          height: u(44);
          font-size: u(32);
          font-weight: 500;
          position: relative;
          &_icon{
            font-size: u(18);
          }
        }
      }
      &_list{
        :last-child{

        }
      }
      &_record{
        width: 100%;
        height:u(150);
        background-color: $primary-white-1;
        padding: 0  u(24);
        box-sizing: border-box;
        &_con{
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          padding-top: u(20);
          border-bottom: u(1) solid rgba(245, 245, 245, 0.5);
          &_left{
            font-size: u(28);
            font-weight: 400;
            :first-child{
              color:$primary-black-2;
            }
            :last-child{
              color:$primary-grey-1;
            }
          }
          &_right{
            display: flex;
            justify-content: center;
            align-items: center;
            &_info{
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: flex-end;
              margin-right:u(18);
              :nth-child(2){
                color:$primary-red-1;
              }
            }
            &_icon{
              color:$primary-grey-1;
              font-size: u(24);
            }
          }
        }

      }
     }
    &_bottom_tips{
      margin-top: u(100);
      width: 100vw;
      text-align: center;
      height: u(44);
      color:$primary-grey-1;
      margin-bottom: u(44);
    }
    .wp-balance-list-icon-box{
      transition:0.3s ease-in;
      position: absolute;
      right: u(-34);
      z-index: 1;
      width:u(34);
      height:u(34);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .transform-down-icon-right{
      transform: rotate(90deg);
    }
    .transform-up-icon-right{
      transform: rotate(-90deg);
    }
    .wp-balance-mask{
      width: 100vw;
      height: 100vh;
      position: fixed;
      z-index: 200;
      left: 0;
      top: 0;
      background-color:rgba(0,0,0,0.7);
      transition:0.2s ease-in;
    }
    .wp-balance-bottom_pop{
      position: fixed;
      z-index: 1002;
      bottom: 0;
      left:0;
      height: u(600);
      width: 100vw;
      background-color: $primary-white-1;
      border-radius: u(24)  u(24);
      transition: .3s ease-in-out;
      &.hidden{
        transform: translateY(100%);
      }
      &_header{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding:u(16) u(48);
        font-size: u(32);
        border-bottom: u(1) solid $primary-grey-3;
        :last-child{
          color: #64769C;
        }

      }
      &_picker_view{
        width: 100%;
        height: u(500);
      }
    }
  }

}

@include wp-balance-list-theme($lucky-theme);
