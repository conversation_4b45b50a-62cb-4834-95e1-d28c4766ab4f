import {acs} from '@petkit/redux';
import {bindActionCreators,} from 'redux';
import {connect} from 'utils/weapp-redux';
import {debounce} from "lodash-es";

const date = new Date()
const years = []
const months = []


for (let i = 1990; i <= date.getFullYear(); i++) {
    years.push(i)
}

for (let i = 1; i <= 12; i++) {
    months.push(i)
}


const pageConfig = {
    data: {
        title: '余额变动',
        typeKey: 'all',
        typeOptions: {
            all: '全部',
            decrement: '减少',
            increment: '增加'
        },
        isExpandFilter: false,
        isMouthChange: false,
        isFresh: false,
        recordList: [],
        years, months,
        maxDate: 0,
        minDate: 0,
        value: [9999, 1],
        chooseIndex: [],
        scrollId: null
    },
    onLoad(options) {
        this.freshRecordList()
    },

    changeFilterOptions() {
        console.log('changeFilterOptions')
        this.setData({isExpandFilter: !this.data.isExpandFilter})
    },
    changeMouth({currentTarget}) {
        const {dataset: {date}} = currentTarget
        const {year, month} = date
        const value = [0, 0]
        years.forEach((item, index) => {
            item === year && (value[0] = index)
        })
        months.forEach((item, index) => {
            item === month && (value[1] = index)
        })
        this.setData({isMouthChange: !this.data.isMouthChange, value})
    },
    bindYearAndMouthChange(e) {
        this.setData({chooseIndex: e.detail.value})
    },
    hiddenDateChoose() {
        this.setData({isMouthChange: false})
    },
    confirmDateChoose() {
        const year = years[this.data.chooseIndex[0]];
        const month = months[this.data.chooseIndex[1]];
        this.hiddenDateChoose()
        let flag = true
        const list = this.data.userBalanceRecords.filter(item => {
            return `wx-balance-date-${year}-${month}` === `wx-balance-date-${item.year}-${item.month}`
        })

        this.setData({
            scrollId: list.length > 0 ? `wx-balance-date-${year}-${month}` : 'bottom-tips',
            value: this.data.chooseIndex
        })
    },
    goToRecordDetails({currentTarget}) {
        console.log({currentTarget},)
        const {dataset} = currentTarget
        const {item} = dataset
        wx.navigateTo({
            url: `/balance/pages/balance-details/balance-details?data=${JSON.stringify(item)}`,
        });
    },
    hiddenFilterOptions() {
        this.setData({isExpandFilter: false, isMouthChange: false})
    },
    preventTouchMove() {

    },
    freshRecordList() {
        if(this.data.isFresh) return
        this.setData({isFresh: true})
        this.getUserBalanceRecords({}, {
            success: () => {
                this.changeOption({currentTarget: {dataset: {type: this.data.typeKey}}})
                this.setData({isFresh: false,})
            },

        })
    },
    changeOption({currentTarget}) {
        const {dataset: {type}} = currentTarget
        let recordList = this.data.userBalanceRecords
        if (type !== 'all') {
            recordList = this.data.userBalanceRecords.map(obj=>{
                return {
                    ...obj,
                    records:obj.records.filter(item => {
                        return item.type === type
                    })
                }
            })
        }

        this.setData({
            typeKey: type,
            recordList
        })
    }
};

const mapStateToData = state => ({
    userBalanceRecords: state.user.user.userBalanceRecords,
    currencySymbol: state.wp.store.warehouse.warehouseById.currencySymbol || '￥',
});

const mapDispatchToPage = dispatch => bindActionCreators({
    getUserBalanceRecords: acs.user.user.getUserBalanceRecords,
}, dispatch);

Page(connect(mapStateToData, mapDispatchToPage)(pageConfig));
