<view class="wp-balance-list-warp">
  <!-- 头部 -->
  <view class="wp-balance-list-warp_header">
    <view class="wp-balance-list-warp_header_con">
      <view class="wp-balance-list-warp_header_left" bindtap="changeFilterOptions">
        {{typeOptions[typeKey]}}
        <view class="wp-balance-list-icon-box transform-{{isExpandFilter?'up':'down'}}-icon-right">
          <text class="PETKIT icon-right-arrow wp-balance-list-warp_header_left_right_icon"></text>
        </view>
      </view>
      <view class="wp-balance-list-warp_header_right">当日变动更新延迟10分钟</view>
    </view>
    <view class="wp-balance-list-warp_header_filter {{isExpandFilter?'':'wp-balance-list-warp_header_filter_hidden'}} ">
      <view wx:for="{{typeOptions}}" wx:for-item="item" wx:for-index="index" wx:key="index" class="wp-balance-list-warp_header_filter_item {{index==typeKey?'select':''}}" bindtap="changeOption" data-type="{{index}}">{{item}}</view>
<!--      <view class="wp-balance-list-warp_header_filter_item" bindtap="changeOption" data-type="1">全部</view>-->
<!--      <view class="wp-balance-list-warp_header_filter_item" bindtap="changeOption" data-type="2">减少</view>-->
<!--      <view class="wp-balance-list-warp_header_filter_item" bindtap="changeOption" data-type="3">增加</view>-->
    </view>
  </view>
  <!-- 内容列表 -->
  <scroll-view scroll-y="{{true}}"  refresher-enabled="{{true}}" scroll-into-view="{{scrollId}}" scroll-with-animation="{{true}}" refresher-triggered="{{isFresh}}" bindrefresherrefresh="freshRecordList" class="wp-balance-list-warp_content">
    <block wx:for="{{recordList}}" wx:for-item="item" wx:for-index="index" wx:key="index">
      <view class="wp-balance-list-warp_content_month" id="wx-balance-date-{{item.year}}-{{item.month}}">
        <!--  月份组的标题  -->
        <view class="wp-balance-list-warp_content_month_title" bindtap="changeMouth" data-date="{{item}}">
          <view>{{item.year}}年{{item.month}}月</view>
          <view class="wp-balance-list-icon-box transform-{{isExpandFilter?'up':'down'}}-icon-right">
            <text class="wp-balance-list-warp_content_month_title_icon PETKIT icon-right-arrow"></text>
          </view>
        </view>
      </view>
      <!--  月份下的变动记录 -->
     <view class="wp-balance-list-warp_content_list">
       <block wx:for="{{item.records}}" wx:for-item="itemRecord" wx:for-index="indexRecord" wx:key="indexRecord">
         <view class="wp-balance-list-warp_content_record">
           <view class="wp-balance-list-warp_content_record_con">
             <view class="wp-balance-list-warp_content_record_con_left">
               <view>{{itemRecord.changeType}}</view>
               <view>{{itemRecord.time}}</view>
             </view>
             <view class="wp-balance-list-warp_content_record_con_right" bindtap="goToRecordDetails" data-item="{{itemRecord}}">
               <view class="wp-balance-list-warp_content_record_con_right_info">
                 <view>{{itemRecord.type === 'increment'?'+':'-'}} {{itemRecord.tradeAmount}}</view>
                 <view wx:if="{{itemRecord.type === 'decrement' && itemRecord.refundAmount }}">已退款 ({{currencySymbol}}{{itemRecord.refundAmount}})</view>
               </view>
               <text class="PETKIT icon-right-arrow wp-balance-list-warp_content_record_con_right_icon"></text>
             </view>
           </view>
         </view>
       </block>
     </view>
    </block>
    <!-- 底部提示 -->
    <view class="wp-balance-list-warp_bottom_tips" id="bottom-tips">只显示最近6个月余额变动情况</view>
  </scroll-view>
  <view class="wp-balance-mask" wx:if="{{isExpandFilter || isMouthChange}}" style="z-index:{{isMouthChange ? 1001 : 200}}" catchtouchmove="preventTouchMove"
        bindtap="hiddenFilterOptions"></view>
  <view class="wp-balance-bottom_pop {{isMouthChange?'':'hidden'}}">
    <view class="wp-balance-bottom_pop_header">
      <view bindtap="hiddenDateChoose">取消</view>
      <view bindtap="confirmDateChoose">确认</view>
    </view>
    <picker-view indicator-style="height: 50px;" class="wp-balance-bottom_pop_picker_view" value="{{value}}" bindchange="bindYearAndMouthChange">
      <picker-view-column>
        <view wx:for="{{years}}" wx:for-index="yearIndex" wx:key="yearIndex" style="line-height: 50px; text-align: center;">{{item}}年</view>
      </picker-view-column>
      <picker-view-column>
        <view wx:for="{{months}}" wx:for-index="monthIndex" wx:key="monthIndex" style="line-height: 50px; text-align: center;">{{item}}月</view>
      </picker-view-column>
    </picker-view>
  </view>
</view>
