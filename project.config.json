{"description": "项目配置", "cloudfunctionRoot": "./cloudfunctions/", "setting": {"condition": true, "urlCheck": false, "es6": true, "enhance": true, "postcss": true, "minified": true, "minifyWXSS": true, "minifyWXML": true, "minifyJS": true, "ignoreUploadUnusedFiles": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": false, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": []}, "compileType": "miniprogram", "libVersion": "2.20.3", "projectname": "com-petkit-mp", "appid": "", "isGameTourist": false, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}